<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.hr.mapper.HrStaffPersonInfoMapper">

    <resultMap type="HrStaffPersonInfo" id="HrStaffPersonInfoResult">
        <result property="fdId" column="fd_id"/>
        <result property="fdHierarchyId" column="fd_hierarchy_id"/>
        <result property="fdName" column="fd_name"/>
        <result property="fdSex" column="fd_sex"/>
        <result property="fdDateOfBirth" column="fd_date_of_birth"/>
        <result property="fdBirthdayOfYear" column="fd_birthday_of_year"/>
        <result property="fdStaffNo" column="fd_staff_no"/>
        <result property="fdIdCard" column="fd_id_card"/>
        <result property="fdWorkTime" column="fd_work_time"/>
        <result property="fdTimeOfEnterprise" column="fd_time_of_enterprise"/>
        <result property="fdTrialExpirationTime" column="fd_trial_expiration_time"/>
        <result property="fdTrialOperationPeriod" column="fd_trial_operation_period"/>
        <result property="fdEntryTime" column="fd_entry_time"/>
        <result property="fdPositiveTime" column="fd_positive_time"/>
        <result property="fdOrgDeptName" column="fd_org_dept_name"/>
        <result property="fdActualPositiveTime" column="fd_actual_positive_time"/>
        <result property="fdPositiveRemark" column="fd_positive_remark"/>
        <result property="fdLeaveTime" column="fd_leave_time"/>
        <result property="fdActualLeaveTime" column="fd_actual_leave_time"/>
        <result property="fdIsRehire" column="fd_is_rehire"/>
        <result property="fdRehireTime" column="fd_rehire_time"/>
        <result property="fdEmploymentperiod" column="fd_employmentperiod"/>
        <result property="fdStaffType" column="fd_staff_type"/>
        <result property="fdNameUsedBefore" column="fd_name_used_before"/>
        <result property="fdNation" column="fd_nation"/>
        <result property="fdPoliticalLandscape" column="fd_political_landscape"/>
        <result property="fdDateOfGroup" column="fd_date_of_group"/>
        <result property="fdDateOfParty" column="fd_date_of_party"/>
        <result property="fdHighestEducation" column="fd_highest_education"/>
        <result property="fdHighestDegree" column="fd_highest_degree"/>
        <result property="fdMaritalStatus" column="fd_marital_status"/>
        <result property="fdHealth" column="fd_health"/>
        <result property="fdStature" column="fd_stature"/>
        <result property="fdWeight" column="fd_weight"/>
        <result property="fdLivingPlace" column="fd_living_place"/>
        <result property="fdNativePlace" column="fd_native_place"/>
        <result property="fdHomeplace" column="fd_homeplace"/>
        <result property="fdAccountProperties" column="fd_account_properties"/>
        <result property="fdRegisteredResidence" column="fd_registered_residence"/>
        <result property="fdResidencePoliceStation" column="fd_residence_police_station"/>
        <result property="fdStatus" column="fd_status"/>
        <result property="fdMobileNo" column="fd_mobile_no"/>
        <result property="fdEmail" column="fd_email"/>
        <result property="fdOfficeLocation" column="fd_office_location"/>
        <result property="fdWorkPhone" column="fd_work_phone"/>
        <result property="fdEmergencyContact" column="fd_emergency_contact"/>
        <result property="fdEmergencyContactPhone" column="fd_emergency_contact_phone"/>
        <result property="fdOtherContact" column="fd_other_contact"/>
        <result property="fdRelatedProcess" column="fd_related_process"/>
        <result property="fdOrgPersonId" column="fd_org_person_id"/>
        <result property="fdOrgParentOrgId" column="fd_org_parent_org_id"/>
        <result property="fdOrgParentId" column="fd_org_parent_id"/>
        <result property="fdOrgRankId" column="fd_org_rank_id"/>
        <result property="fdStaffingLevelId" column="fd_staffing_level_id"/>
        <result property="fdStaffEntryId" column="fd_staff_entry_id"/>
        <result property="fdLeaveApplyDate" column="fd_leave_apply_date"/>
        <result property="fdLeavePlanDate" column="fd_leave_plan_date"/>
        <result property="fdLeaveSalaryEndDare" column="fd_leave_salary_end_dare"/>
        <result property="fdLeaveReason" column="fd_leave_reason"/>
        <result property="fdLeaveRemark" column="fd_leave_remark"/>
        <result property="fdNextCompany" column="fd_next_company"/>
        <result property="fdNatureWork" column="fd_nature_work"/>
        <result property="fdLeaveStatus" column="fd_leave_status"/>
        <result property="fdReportLeaderId" column="fd_report_leader_id"/>
        <result property="fdHrReportLeaderId" column="fd_hr_report_leader_id"/>
        <result property="fdWorkAddress" column="fd_work_address"/>
        <result property="fdLoginName" column="fd_login_name"/>
        <result property="fdSalary" column="fd_salary"/>
        <result property="fdWorkTimeDiff" column="fd_work_time_diff"/>
        <result property="fdWorkYearDiff" column="fd_work_year_diff"/>
        <result property="youxiaoqi" column="youxiaoqi"/>
        <result property="txrq" column="txrq"/>
        <result property="gzdi" column="Gzdi"/>
        <result property="sfzs" column="sfzs"/>
        <result property="gzffssgs" column="gzffssgs"/>
        <result property="realshgl" column="realshgl"/>
        <result property="costCenterCode" column="costCenterCode"/>
        <result property="costCenterName" column="costCenterName"/>
        <result property="esg" column="esg"/>
        <result property="workType" column="gslx"/>
        <result property="mutualFund" column="yghzjjje"/>
        <result property="hc" column="hc"/>
        <result property="socialSecurityLocale" column="wxyjjnd"/>
        <result property="socialSecurityOrg1" column="wxyjone"/>
        <result property="socialSecurityOrg2" column="wxyjtwo"/>
        <result property="socialSecurityOrg3" column="wxyjthree"/>
        <result property="thirdPartyPaymentAgency" column="wxyjdsf"/>
    </resultMap>

    <sql id="selectHrStaffPersonInfoVo">
        select fd_id,
               fd_hierarchy_id,
               fd_name,
               (CASE fd_sex WHEN 'M' THEN N'男' ELSE N'女' END) 'fd_sex',
               fd_date_of_birth,
               fd_birthday_of_year,
               fd_staff_no,
               fd_id_card,
               fd_work_time,
               fd_time_of_enterprise,
               fd_trial_expiration_time,
               CASE
                   WHEN fd_trial_operation_period = '' THEN NULL
                   ELSE fd_trial_operation_period
                   END AS                                       fd_trial_operation_period,
               fd_entry_time,
               fd_positive_time,
               fd_org_dept_name,
               fd_actual_positive_time,
               fd_positive_remark,
               fd_leave_time,
               fd_actual_leave_time,
               fd_is_rehire,
               fd_rehire_time,
               fd_employmentperiod,
               fd_staff_type,
               fd_name_used_before,
               fd_nation,
               fd_political_landscape,
               fd_date_of_group,
               fd_date_of_party,
               fd_highest_education,
               fd_highest_degree,
               fd_marital_status,
               fd_health,
               fd_stature,
               fd_weight,
               fd_living_place,
               fd_native_place,
               fd_homeplace,
               fd_account_properties,
               fd_registered_residence,
               fd_residence_police_station,
               fd_status,
               fd_mobile_no,
               fd_email,
               fd_office_location,
               fd_work_phone,
               fd_emergency_contact,
               fd_emergency_contact_phone,
               fd_other_contact,
               fd_related_process,
               fd_org_person_id,
               fd_org_parent_org_id,
               fd_org_parent_id,
               fd_org_rank_id,
               fd_staffing_level_id,
               fd_staff_entry_id,
               fd_leave_apply_date,
               fd_leave_plan_date,
               fd_leave_salary_end_dare,
               fd_leave_reason,
               fd_leave_remark,
               fd_next_company,
               fd_nature_work,
               fd_leave_status,
               fd_report_leader_id,
               fd_hr_report_leader_id,
               fd_work_address,
               fd_login_name,
               fd_salary,
               fd_work_time_diff,
               fd_work_year_diff,
               youxiaoqi,
               txrq,
               Gzdi,
               sfzs,
               gzffssgs,
               realshgl,
               costCenterCode,
               costCenterName,
               esg,
               gslx,
               yghzjjje,
               hc,
               wxyjjnd,
               wxyjone,
               wxyjtwo,
               wxyjthree,
               wxyjdsf
        from hr_staff_person_info
    </sql>

    <select id="selectHrStaffPersonInfoList" parameterType="HrStaffPersonInfo" resultMap="HrStaffPersonInfoResult">
        <include refid="selectHrStaffPersonInfoVo"/>
        <where>
            <if test="fdHierarchyId != null  and fdHierarchyId != ''">and fd_hierarchy_id = #{fdHierarchyId}</if>
            <if test="fdName != null  and fdName != ''">and fd_name like concat('%', #{fdName}, '%')</if>
            <if test="fdSex != null  and fdSex != ''">and fd_sex = #{fdSex}</if>
            <if test="fdDateOfBirth != null ">and fd_date_of_birth = #{fdDateOfBirth}</if>
            <if test="fdBirthdayOfYear != null ">and fd_birthday_of_year = #{fdBirthdayOfYear}</if>
            <if test="fdStaffNo != null  and fdStaffNo != ''">and fd_staff_no = #{fdStaffNo}</if>
            <if test="fdIdCard != null  and fdIdCard != ''">and fd_id_card = #{fdIdCard}</if>
            <if test="fdWorkTime != null ">and fd_work_time = #{fdWorkTime}</if>
            <if test="fdTimeOfEnterprise != null ">and fd_time_of_enterprise = #{fdTimeOfEnterprise}</if>
            <if test="fdTrialExpirationTime != null ">and fd_trial_expiration_time = #{fdTrialExpirationTime}</if>
            <if test="fdTrialOperationPeriod != null  and fdTrialOperationPeriod != ''">and fd_trial_operation_period =
                #{fdTrialOperationPeriod}
            </if>
            <if test="fdEntryTime != null ">and fd_entry_time = #{fdEntryTime}</if>
            <if test="fdPositiveTime != null ">and fd_positive_time = #{fdPositiveTime}</if>
            <if test="fdOrgDeptName != null  and fdOrgDeptName != ''">and fd_org_dept_name like concat('%',
                #{fdOrgDeptName}, '%')
            </if>
            <if test="fdActualPositiveTime != null ">and fd_actual_positive_time = #{fdActualPositiveTime}</if>
            <if test="fdPositiveRemark != null  and fdPositiveRemark != ''">and fd_positive_remark =
                #{fdPositiveRemark}
            </if>
            <if test="fdLeaveTime != null ">and fd_leave_time = #{fdLeaveTime}</if>
            <if test="fdActualLeaveTime != null ">and fd_actual_leave_time = #{fdActualLeaveTime}</if>
            <if test="fdIsRehire != null ">and fd_is_rehire = #{fdIsRehire}</if>
            <if test="fdRehireTime != null ">and fd_rehire_time = #{fdRehireTime}</if>
            <if test="fdEmploymentperiod != null ">and fd_employmentperiod = #{fdEmploymentperiod}</if>
            <if test="fdStaffType != null  and fdStaffType != ''">and fd_staff_type = #{fdStaffType}</if>
            <if test="fdNameUsedBefore != null  and fdNameUsedBefore != ''">and fd_name_used_before =
                #{fdNameUsedBefore}
            </if>
            <if test="fdNation != null  and fdNation != ''">and fd_nation = #{fdNation}</if>
            <if test="fdPoliticalLandscape != null  and fdPoliticalLandscape != ''">and fd_political_landscape =
                #{fdPoliticalLandscape}
            </if>
            <if test="fdDateOfGroup != null ">and fd_date_of_group = #{fdDateOfGroup}</if>
            <if test="fdDateOfParty != null ">and fd_date_of_party = #{fdDateOfParty}</if>
            <if test="fdHighestEducation != null  and fdHighestEducation != ''">and fd_highest_education =
                #{fdHighestEducation}
            </if>
            <if test="fdHighestDegree != null  and fdHighestDegree != ''">and fd_highest_degree = #{fdHighestDegree}
            </if>
            <if test="fdMaritalStatus != null  and fdMaritalStatus != ''">and fd_marital_status = #{fdMaritalStatus}
            </if>
            <if test="fdHealth != null  and fdHealth != ''">and fd_health = #{fdHealth}</if>
            <if test="fdStature != null ">and fd_stature = #{fdStature}</if>
            <if test="fdWeight != null ">and fd_weight = #{fdWeight}</if>
            <if test="fdLivingPlace != null  and fdLivingPlace != ''">and fd_living_place = #{fdLivingPlace}</if>
            <if test="fdNativePlace != null  and fdNativePlace != ''">and fd_native_place = #{fdNativePlace}</if>
            <if test="fdHomeplace != null  and fdHomeplace != ''">and fd_homeplace = #{fdHomeplace}</if>
            <if test="fdAccountProperties != null  and fdAccountProperties != ''">and fd_account_properties =
                #{fdAccountProperties}
            </if>
            <if test="fdRegisteredResidence != null  and fdRegisteredResidence != ''">and fd_registered_residence =
                #{fdRegisteredResidence}
            </if>
            <if test="fdResidencePoliceStation != null  and fdResidencePoliceStation != ''">and
                fd_residence_police_station = #{fdResidencePoliceStation}
            </if>
            <if test="fdStatus != null  and fdStatus != ''">and fd_status = #{fdStatus}</if>
            <if test="fdMobileNo != null  and fdMobileNo != ''">and fd_mobile_no = #{fdMobileNo}</if>
            <if test="fdEmail != null  and fdEmail != ''">and fd_email = #{fdEmail}</if>
            <if test="fdOfficeLocation != null  and fdOfficeLocation != ''">and fd_office_location =
                #{fdOfficeLocation}
            </if>
            <if test="fdWorkPhone != null  and fdWorkPhone != ''">and fd_work_phone = #{fdWorkPhone}</if>
            <if test="fdEmergencyContact != null  and fdEmergencyContact != ''">and fd_emergency_contact =
                #{fdEmergencyContact}
            </if>
            <if test="fdEmergencyContactPhone != null  and fdEmergencyContactPhone != ''">and fd_emergency_contact_phone
                = #{fdEmergencyContactPhone}
            </if>
            <if test="fdOtherContact != null  and fdOtherContact != ''">and fd_other_contact = #{fdOtherContact}</if>
            <if test="fdRelatedProcess != null  and fdRelatedProcess != ''">and fd_related_process =
                #{fdRelatedProcess}
            </if>
            <if test="fdOrgPersonId != null  and fdOrgPersonId != ''">and fd_org_person_id = #{fdOrgPersonId}</if>
            <if test="fdOrgParentOrgId != null  and fdOrgParentOrgId != ''">and fd_org_parent_org_id =
                #{fdOrgParentOrgId}
            </if>
            <if test="fdOrgParentId != null  and fdOrgParentId != ''">and fd_org_parent_id = #{fdOrgParentId}</if>
            <if test="fdOrgRankId != null  and fdOrgRankId != ''">and fd_org_rank_id = #{fdOrgRankId}</if>
            <if test="fdStaffingLevelId != null  and fdStaffingLevelId != ''">and fd_staffing_level_id =
                #{fdStaffingLevelId}
            </if>
            <if test="fdStaffEntryId != null  and fdStaffEntryId != ''">and fd_staff_entry_id = #{fdStaffEntryId}</if>
            <if test="fdLeaveApplyDate != null ">and fd_leave_apply_date = #{fdLeaveApplyDate}</if>
            <if test="fdLeavePlanDate != null ">and fd_leave_plan_date = #{fdLeavePlanDate}</if>
            <if test="fdLeaveSalaryEndDare != null ">and fd_leave_salary_end_dare = #{fdLeaveSalaryEndDare}</if>
            <if test="fdLeaveReason != null  and fdLeaveReason != ''">and fd_leave_reason = #{fdLeaveReason}</if>
            <if test="fdLeaveRemark != null  and fdLeaveRemark != ''">and fd_leave_remark = #{fdLeaveRemark}</if>
            <if test="fdNextCompany != null  and fdNextCompany != ''">and fd_next_company = #{fdNextCompany}</if>
            <if test="fdNatureWork != null  and fdNatureWork != ''">and fd_nature_work = #{fdNatureWork}</if>
            <if test="fdLeaveStatus != null  and fdLeaveStatus != ''">and fd_leave_status = #{fdLeaveStatus}</if>
            <if test="fdReportLeaderId != null  and fdReportLeaderId != ''">and fd_report_leader_id =
                #{fdReportLeaderId}
            </if>
            <if test="fdHrReportLeaderId != null  and fdHrReportLeaderId != ''">and fd_hr_report_leader_id =
                #{fdHrReportLeaderId}
            </if>
            <if test="fdWorkAddress != null  and fdWorkAddress != ''">and fd_work_address = #{fdWorkAddress}</if>
            <if test="fdLoginName != null  and fdLoginName != ''">and fd_login_name like concat('%', #{fdLoginName},
                '%')
            </if>
            <if test="fdSalary != null ">and fd_salary = #{fdSalary}</if>
            <if test="fdWorkTimeDiff != null ">and fd_work_time_diff = #{fdWorkTimeDiff}</if>
            <if test="fdWorkYearDiff != null ">and fd_work_year_diff = #{fdWorkYearDiff}</if>
            <if test="youxiaoqi != null ">and youxiaoqi = #{youxiaoqi}</if>
            <if test="txrq != null ">and txrq = #{txrq}</if>
            <if test="gzdi != null  and gzdi != ''">and Gzdi = #{gzdi}</if>
            <if test="sfzs != null  and sfzs != ''">and sfzs = #{sfzs}</if>
            <if test="gzffssgs != null  and gzffssgs != ''">and gzffssgs = #{gzffssgs}</if>
            <if test="realshgl != null ">and realshgl = #{realshgl}</if>
            <if test="costCenterCode != null and costCenterCode != ''">and costCenterCode = #{costCenterCode}</if>
            <if test="costCenterName != null and costCenterName != ''">and costCenterName = #{costCenterName}</if>
            <if test="esg != null and esg != ''">and esg = #{esg}</if>
        </where>
    </select>

    <select id="selectHrStaffPersonInfoByFdId" parameterType="String" resultMap="HrStaffPersonInfoResult">
        <include refid="selectHrStaffPersonInfoVo"/>
        where fd_id = #{id}
    </select>

    <select id="selectHrStaffPersonInfo" resultMap="HrStaffPersonInfoResult">
        <include refid="selectHrStaffPersonInfoVo"/>
        <where>
            fd_status != 'leave' AND fd_status != 'official'
        </where>
    </select>
    <update id="updateByLoginNames" parameterType="List">
        <foreach collection="list" item="item" open="" close="" separator=";">
            update hr_staff_person_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.costCenterCode != null and item.costCenterCode != ''">costCenterCode
                    =#{item.costCenterCode},
                </if>
                <if test="item.costCenterName != null and item.costCenterName != ''">costCenterName
                    =#{item.costCenterName},
                </if>
                <if test="item.esg != null and item.esg != ''">esg =#{item.esg}</if>
            </trim>
            <where>
                fd_staff_no =#{item.loginName}
            </where>
        </foreach>
    </update>

    <update id="updateByLoginName" parameterType="importArchiveData">
        update hr_staff_person_info set
        <if test="costCenterCode != null and costCenterCode != ''">costCenterCode = #{costCenterCode},</if>
        <if test="costCenterName != null and costCenterName != ''">costCenterName = #{costCenterName},</if>
        <if test="esg != null and esg != ''">esg = #{esg}</if>
        <where>
            fd_staff_no = #{loginName}
        </where>
    </update>

    <select id="selectList" resultMap="HrStaffPersonInfoResult">
        <include refid="selectHrStaffPersonInfoVo"/>
    </select>

    <select id="selectBatchIds" resultMap="HrStaffPersonInfoResult">
        <include refid="selectHrStaffPersonInfoVo"/>
        <where>
            <foreach collection="list" item="id" open="fd_id IN (" close=")" separator=",">
                <!-- 每次遍历要拼接的串 -->
                #{id}
            </foreach>
        </where>
    </select>
</mapper>