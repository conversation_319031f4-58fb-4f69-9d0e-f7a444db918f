<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.hr.mapper.MarriageAndChildbirthMapper">

    <resultMap type="MarriageAndChildbirth" id="MarriageAndChildbirthResult">
        <result property="singleNumber" column="singleNumber"/>
        <result property="applicant" column="applicant"/>
        <result property="applicationDate" column="applicationDate"/>
        <result property="position" column="position"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="department" column="department"/>
        <result property="joinedDate" column="joinedDate"/>
        <result property="cashGiftType" column="cashGiftType"/>
        <result property="weddingDate" column="weddingDate"/>
        <result property="childbearingTime" column="childbearingTime"/>
        <result property="applicationsAmount" column="applicationsAmount"/>
        <result property="distributionTime" column="distributionTime"/>
        <result property="documentStatus" column="documentStatus"/>
        <result property="currentSession" column="currentSession"/>
        <result property="distributionWay" column="distributionWay"/>
        <result property="endDate" column="endDate"/>
        <result property="distributionWay" column="distributionWay"/>
    </resultMap>

    <sql id="selectTps">
        select singleNumber,
               applicant,
               applicationDate,
               position,
               jobNumber,
               department,
               joinedDate,
               cashGiftType,
               weddingDate,
               childbearingTime,
               applicationsAmount,
               distributionTime,
               documentStatus,
               currentSession,
               distributionWay,
               endDate
        from marriageAndChildbirthGiftMoney
    </sql>

    <select id="queryMarriageAndChildbirth" parameterType="MarriageAndChildbirthQuery" resultMap="MarriageAndChildbirthResult">
        <include refid="selectTps"/>
        <where>
            <if test="applyForStartDate != null  and applyForStartDate != ''"> and applicationDate &gt;= #{applyForStartDate}</if>
            <if test="applyForEndDate != null  and applyForEndDate != ''"> and applicationDate &lt;= #{applyForEndDate}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="cashGiftType != null  and cashGiftType != ''"> and cashGiftType like concat('%', #{cashGiftType}, '%')</if>
        </where>
        order by applicationDate desc
    </select>
    <select id="selectAboutSalaryByMonth" parameterType="String" resultMap="MarriageAndChildbirthResult">
        <include refid="selectTps"/>
        where documentStatus = '结束' and (distributionWay = '随工资发放' or distributionWay is null) and  CONVERT(VARCHAR(7), distributionTime, 126) = #{month}
        order by applicationDate desc
    </select>
</mapper>