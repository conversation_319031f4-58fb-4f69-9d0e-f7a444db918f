package com.akesobio.report.costControl.controller;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.report.costControl.domain.MqConfig;
import com.akesobio.report.costControl.service.IMqConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 虚拟岗校验规则配置 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/costControl/mqconfig")
public class MqConfigController extends BaseController
{
    @Autowired
    private IMqConfigService mqConfigService;

    /**
     * 获取虚拟岗校验规则配置列表
     */
    @PreAuthorize("@ss.hasPermi('costControl:mqconfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(MqConfig mqConfig)
    {
        startPage();
        List<MqConfig> list = mqConfigService.selectMqConfigList(mqConfig);
        return getDataTable(list);
    }

    /**
     * 导出虚拟岗校验规则配置列表
     */
    @Log(title = "虚拟岗校验规则配置", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('costControl:mqconfig:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, MqConfig mqConfig)
    {
        List<MqConfig> list = mqConfigService.selectMqConfigList(mqConfig);
        ExcelUtil<MqConfig> util = new ExcelUtil<MqConfig>(MqConfig.class);
        util.exportExcel(response, list, "虚拟岗校验规则配置数据");
    }

    /**
     * 根据配置编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('costControl:mqconfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id)
    {
        return success(mqConfigService.selectMqConfigById(id));
    }

    /**
     * 根据接口名称查询配置信息
     */
    @GetMapping(value = "/interfaceName/{interfaceName}")
    public AjaxResult getByInterfaceName(@PathVariable String interfaceName)
    {
        return success(mqConfigService.selectMqConfigByInterfaceName(interfaceName));
    }

    /**
     * 新增虚拟岗校验规则配置
     */
    @PreAuthorize("@ss.hasPermi('costControl:mqconfig:add')")
    @Log(title = "虚拟岗校验规则配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MqConfig mqConfig)
    {
        if (!mqConfigService.checkInterfaceNameUnique(mqConfig))
        {
            return error("新增配置'" + mqConfig.getInterfaceName() + "'失败，接口名称已存在");
        }
        mqConfig.setCreateBy(getUsername());
        return toAjax(mqConfigService.insertMqConfig(mqConfig));
    }

    /**
     * 修改虚拟岗校验规则配置
     */
    @PreAuthorize("@ss.hasPermi('costControl:mqconfig:edit')")
    @Log(title = "虚拟岗校验规则配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MqConfig mqConfig)
    {
        if (!mqConfigService.checkInterfaceNameUnique(mqConfig))
        {
            return error("修改配置'" + mqConfig.getInterfaceName() + "'失败，接口名称已存在");
        }
        mqConfig.setUpdateBy(getUsername());
        return toAjax(mqConfigService.updateMqConfig(mqConfig));
    }

    /**
     * 删除虚拟岗校验规则配置
     */
    @PreAuthorize("@ss.hasPermi('costControl:mqconfig:remove')")
    @Log(title = "虚拟岗校验规则配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        mqConfigService.deleteMqConfigByIds(ids);
        return success();
    }

    /**
     * 校验接口名称唯一性
     */
    @PostMapping("/checkInterfaceNameUnique")
    public boolean checkInterfaceNameUnique(@RequestBody MqConfig mqConfig)
    {
        return mqConfigService.checkInterfaceNameUnique(mqConfig);
    }
}
