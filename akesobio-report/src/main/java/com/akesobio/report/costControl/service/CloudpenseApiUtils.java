package com.akesobio.report.costControl.service;

import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.costControl.common.SsoUtils;
import com.akesobio.report.costControl.domain.RemoteFileRequest;
import com.akesobio.report.costControl.domain.WorkflowNode;
import com.akesobio.report.costControl.domain.WorkflowUpdate;
import com.akesobio.report.costControl.domain.LovValuesPageResponse;
import com.akesobio.report.costControl.domain.LovValueItem;
import com.akesobio.report.costControl.domain.CostCenterDepartmentSyncRequest;
import com.akesobio.report.costControl.domain.CostCenterDepartmentData;
import com.akesobio.report.costControl.domain.DepartmentCostInfo;
import com.akesobio.report.costControl.domain.CostCenterDepartmentSyncResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.FileEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

@Slf4j
@Service
public class CloudpenseApiUtils {

    @Value("${costControl.token.token_url}")
    private String token_url;
    @Value("${costControl.token.grant_type}")
    private String grant_type;
    @Value("${costControl.token.client_id}")
    private String client_id;
    @Value("${costControl.token.client_secret}")
    private String client_secret;
    @Value("${costControl.baseUrl}")
    public  String baseUrl;
    @Value("${costControl.lovUrl}")
    public  String lovUrl;

    @Value("${costControl.documentUrl}")
    public  String documentUrl;
    @Value("${costControl.upFilesUrl}")
    public  String upFilesUrl;

    @Value("${costControl.remoteFileUrl}")
    public String remoteFileUrl;  // 需要在配置文件中添加对应配置

    @Value("${costControl.costCenterDepartmentBatchUrl}")
    private String costCenterDepartmentBatchUrl;

    @Value("${costControl.getUploadUrl.url}")
    private String getUploadUrl;

    @Value("${costControl.workflowBatch}")
    private String workflowBatch;

    @Value("${costControl.updateExpClaimHeaderUrl}")
    private String updateExpClaimHeaderUrl;
//    public static String baseUrl = "https://taliopenapi.cloudpense.com/";

    public static void main(String[] args) throws Exception {
//        syncProjects("project_list");
    }

    /**
     * 测试费用报销头部更新接口配置
     * @return 配置信息
     */
    public String testUpdateExpClaimHeaderConfig() {
        return "updateExpClaimHeaderUrl: " + updateExpClaimHeaderUrl;
    }


    /**
     * 批量更新审批流
     * @param workflowUpdates 工作流更新数据列表
     * @param locale 语言环境(zh_CN/en_US/ja_JP/zh_TW)
     * @return 接口响应结果
     */
    public JSONObject batchUpdateWorkflows(WorkflowUpdate workflowUpdates, String locale) throws Exception {
        // 1. 参数校验
//        if (workflowUpdates == null || workflowUpdates.size() > MAX_WORKFLOWS) {
//            throw new IllegalArgumentException("工作流条数不能超过" + MAX_WORKFLOWS + "条");
//        }
//        String access_token = getToken();
//        String url = getUploadUrl + "?grant_type=" + grant_type + "&client_id=" + client_id
//                + "&client_secret=" + client_secret + "&access_token=" + access_token;
        // 2. 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("bizId", UUID.randomUUID().toString()); // 生成唯一标识
        requestBody.put("timestamp", System.currentTimeMillis()); // 当前时间戳
        JSONArray dataArray = new JSONArray();
//        for (WorkflowUpdate update : workflowUpdates) {
            JSONObject dataItem = new JSONObject();
            dataItem.put("code", workflowUpdates.getDocumentCode());

            JSONArray pathList = new JSONArray();
            for (WorkflowNode node : workflowUpdates.getNodes()) {
                JSONObject nodeJson = new JSONObject();
                nodeJson.put("workflow_type", node.getWorkflowType());
                nodeJson.put("sequence_num", node.getSequenceNum());
                nodeJson.put("status", node.getStatus());
                nodeJson.put("type", node.getType());
                // 可选字段处理
                if (node.getPositionCode() != null) {
                    nodeJson.put("position_code", node.getPositionCode());
                }
                if (node.getEmployeeNumber() != null) {
                    nodeJson.put("employee_number", node.getEmployeeNumber());
                }
                if (node.getNote() != null) {
                    // 批注超过64字符截取
                    nodeJson.put("note", node.getNote().length() > 64 ?
                            node.getNote().substring(0, 64) : node.getNote());
                }
                if (node.getOpenDate() != null) {
                    nodeJson.put("open_date", node.getOpenDate());
                }
                if (node.getEndDate() != null) {
                    nodeJson.put("end_date", node.getEndDate());
                }

                pathList.add(nodeJson);
            }
            dataItem.put("path_list", pathList);
            dataArray.add(dataItem);
//        }
        requestBody.put("data", dataArray);
        // 3. 设置请求头
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.set("locale", locale); // 设置多语言环境
//
//        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);

        // 4. 发送请求
//        RestTemplate restTemplate = new RestTemplate();
//        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);


        String res = sendPostRequest(workflowBatch,requestBody.toJSONString());

        // 5. 处理响应
        return JSON.parseObject(res);
    }
    public  String syncProjects(String project_list) throws Exception {

        String url = baseUrl+"common/projects/batch";

        Map<String, Object> data = new HashMap<>();
        data.put("code", "project123");
        data.put("project_name", "新项目启动");
        data.put("project_owner", "B00490");
        data.put("project_department", "1794b1930e1dd7e48015c0248c2b4b62");
//        data.put("description", "这是一个新的项目启动，需要各部门配合。");
//        data.put("enabled_flag", "Y");
//        data.put("staff_flag", "N");
//        data.put("project_status", "incomplete");
//        data.put("branch_flag", "Y");
//        data.put("branch_codes", Arrays.asList("1000", "1050"));
//        data.put("parent_code", "parent123");
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("bizId", createBizId());
        jsonMap.put("timestamp", System.currentTimeMillis());
        jsonMap.put("data",  Arrays.asList(data));
        return sendPostRequest(url,JSONObject.toJSONString(jsonMap));
//        jsonMap.putAll(columns);
    }



    public  String getToken() {
        String response = "";
//        String token_url = "https://taliopenapi.cloudpense.com/common/unAuth/tokens/get";
//        String token_url = tokenUrl;
//        String grant_type = "client_credentials";
//        String client_id = "cloudpense2483631363b";
//        String client_secret = "44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98";
        String url = token_url + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret;
        try {
            response = HttpUtils.sendGet(url);
            JSONObject jsonObject = JSON.parseObject(response);
            String accessToken = jsonObject.getJSONObject("data").getString("access_token");
            log.info("response: " + response);
            return accessToken;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    //上传文件 到费控系统
    public  String uploadFile(String filePath) throws MalformedURLException {
        String url = upFilesUrl;
        RestTemplate template = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("multipart/form-data");
        headers.setContentType(type);
        headers.set("access_token", getToken());
        //设置请求体
        FileSystemResource resource = new FileSystemResource(filePath);
//        UrlResource urlResource = new UrlResource("file://"+filePath);
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("file", resource);
        HttpEntity<MultiValueMap<String, Object>> files = new HttpEntity<>(form, headers);
        String attachmentResponse = template.postForObject(url, files, String.class);
        log.info("resJson: " + attachmentResponse);
        log.info("attachmentResponse: " + attachmentResponse);
//        System.out.println(attachmentResponse);
        JSONObject resJson = JSON.parseObject(attachmentResponse);
        if(resJson.getIntValue("resCode") == 200000){
            String attachmentUrl = resJson.getJSONObject("data").getJSONObject("attachment").getString("attachmentURL");
            if(StringUtils.hasText(attachmentUrl)){
                return attachmentUrl;
            }
        }
        return null;
    }

    // 新增远程文件上传方法
    public List<String> uploadRemoteFiles(String fileName,String url) throws Exception {

        List<RemoteFileRequest> files = new ArrayList<>();
        RemoteFileRequest file = new RemoteFileRequest();
        file.setFile_name(fileName);
        file.setFile_url(url);
        file.setPreview_flag(true);
        files.add(file);


        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizId", createBizId());
        requestMap.put("timestamp", System.currentTimeMillis());
        requestMap.put("data", files);

        String access_token = getToken();
        String apiUrl = remoteFileUrl + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret+"&access_token="+access_token;
        log.info("url: " + apiUrl + " requestParams: " + requestMap);
        String response = HttpUtils.sendPostJson(apiUrl,JSONObject.toJSONString(requestMap));

        JSONObject resJson = JSON.parseObject(response);

        if(resJson.getIntValue("resCode") == 200000) {
            List<String> successUrls = new ArrayList<>();
            JSONArray attachments = resJson.getJSONObject("data").getJSONArray("attachment");
            for(int i=0; i<attachments.size(); i++) {
                successUrls.add(attachments.getJSONObject(i).getString("attachmentURL"));
            }
            log.info("successUrls: " + successUrls);
            return successUrls;
        }
        return Collections.emptyList();
    }

    /**
     * 获取文件上传地址 (v3版本)
     * @param fileNames 文件名列表
     * @return 包含文件名、上传URL和附件URL的映射列表
     * @throws Exception 获取失败时抛出异常
     */
    public List<Map<String, String>> getUploadUrl(List<String> fileNames) throws Exception {
        if (fileNames == null || fileNames.size() > 100) {
            throw new IllegalArgumentException("文件名数量不能超过100个");
        }

        String access_token = getToken();
        String url = getUploadUrl + "?grant_type=" + grant_type + "&client_id=" + client_id
                + "&client_secret=" + client_secret + "&access_token=" + access_token;

        // 构建请求体 - 按照v3 API要求
        JSONObject requestBody = new JSONObject();
        requestBody.put("bizId", UUID.randomUUID().toString()); // 业务唯一识别码
        requestBody.put("timestamp", System.currentTimeMillis()); // 时间戳

        // 构建文件数据数组，包含文件名和content_type
        JSONArray dataArray = new JSONArray();
        for (String fileName : fileNames) {
            JSONObject fileObj = new JSONObject();
            fileObj.put("file_name", fileName);
            fileObj.put("content_type", getContentTypeByFileName(fileName));
            dataArray.add(fileObj);
        }
        requestBody.put("data", dataArray);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("locale", "zh_CN"); // 默认使用中文环境

        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

        JSONObject responseJson = JSON.parseObject(response.getBody());
        log.info("v3 API响应: " + responseJson);
        if (responseJson.getIntValue("resCode") != 200000) {
            throw new RuntimeException("获取上传地址失败: " + responseJson.getString("resMsg"));
        }

        // 解析返回数据 - 按照v3 API响应格式
        JSONArray responseDataArray = responseJson.getJSONArray("data");
        List<Map<String, String>> result = new ArrayList<>();
        for (int i = 0; i < responseDataArray.size(); i++) {
            JSONObject item = responseDataArray.getJSONObject(i);
            Map<String, String> fileInfo = new HashMap<>();
            fileInfo.put("fileName", item.getString("file_name"));        // v3字段名
            fileInfo.put("fileURL", item.getString("upload_url"));        // v3字段名
            fileInfo.put("attachmentURL", item.getString("attachment_url")); // v3新增字段
            result.add(fileInfo);
        }
        log.info("解析结果: " + result);
        return result;
    }

    // 使用URL上传文件
    public  boolean uploadFile(String uploadUrl, String filePath) throws IOException {


        // 原有的Apache HttpClient实现 - 已注释
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;

        URL signedUrl = new URL(uploadUrl);
        try {
            HttpPut put = new HttpPut(signedUrl.toString());
            FileEntity entity = new FileEntity(new File(filePath));
            put.setEntity(entity);

            // 智能设置Content-Type头：只有在URL中没有包含content-type参数时才设置
            // 这样可以避免与预签名URL的签名冲突
            if (!uploadUrl.toLowerCase().contains("content-type")) {
                String contentType = Files.probeContentType(Paths.get(filePath));
                if (contentType == null) {
                    contentType = getContentTypeByFileName(filePath);
                }
                put.setHeader("Content-Type", contentType);
                log.info("设置Content-Type: {}", contentType);
            } else {
                log.info("URL中已包含content-type参数，跳过Content-Type头设置");
            }

            httpClient = HttpClients.createDefault();
            response = httpClient.execute(put);
            System.out.println("返回上传状态码：" + response.getStatusLine().getStatusCode());
            System.out.println(response.toString());
            if (response.getStatusLine().getStatusCode() == 200) {
                System.out.println("上传成功");
            }else {
                throw new RuntimeException("上传失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
            if (httpClient != null) {
                httpClient.close();
            }
        }
                return true;


        // 新的OkHttpClient实现
//        OkHttpClient client = new OkHttpClient().newBuilder()
//                .build();
//
//        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("text/plain");
//
//        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
//                .addFormDataPart("file", "document.txt",
//                        RequestBody.create(okhttp3.MediaType.parse("application/octet-stream"),
//                                new File(filePath)))
//                .addFormDataPart("preview_flag", "true")
//                .build();
//
//        Request request = new Request.Builder()
//                .url(uploadUrl)
//                .method("POST", body)
//                .addHeader("access_token", getToken())
//                .build();
//
//        try (Response response = client.newCall(request).execute()) {
//            System.out.println("返回上传状态码：" + response.code());
//            System.out.println(response.toString());
//            if (response.code() == 200) {
//                System.out.println("上传成功");
//        return true;
//            } else {
//                throw new RuntimeException("上传失败，状态码：" + response.code());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new IOException("文件上传异常", e);
//        }
    }


    public  String extractAttachmentUrl(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return "";
        }
        // 示例URL: https://qaoss.cloudpense.com/cpkfsw/16c6095b-2ba4-4cad-9b22-4f7791be2027.pdf?Expires=...
        // 提取路径部分: cpkfsw/16c6095b-2ba4-4cad-9b22-4f7791be2027.pdf

        // 1. 去除协议和域名部分
        int startIndex = fileUrl.indexOf("://") + 3;
        if (startIndex < 3) {
            return "";
        }
        String pathAndQuery = fileUrl.substring(fileUrl.indexOf("/", startIndex) + 1);

        // 2. 去除查询参数部分
        int queryIndex = pathAndQuery.indexOf("?");
        if (queryIndex > 0) {
            return pathAndQuery.substring(0, queryIndex);
        }
        return pathAndQuery;
    }
    public  String uploadDocument( String requestParams) throws Exception {
//        String grant_type = "client_credentials";
//        String client_id = "cloudpense2483631363b";
//        String client_secret = "44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98";
        String access_token = getToken();
        String url = documentUrl + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret+"&access_token="+access_token;
        log.info("url: " + url + " requestParams: " + requestParams);
        String response = HttpUtils.sendPostJson(url,requestParams);
        log.info("response: " + response);
        return response;
    }

    /**
     * 更新费用报销头部信息

     * @param requestParams 请求参数
     * @return 接口响应结果
     * @throws Exception 处理过程中可能出现的异常
     */
    public String updateExpClaimHeader(String requestParams) throws Exception {
        try {
            String access_token = getToken();
            String url = updateExpClaimHeaderUrl + "?grant_type=" + grant_type + "&client_id=" + client_id
                    + "&client_secret=" + client_secret + "&access_token=" + access_token;

            log.info("updateExpClaimHeader - url: {} requestParams: {}", url, requestParams);
            String response = HttpUtils.sendPostJson(url, requestParams);
            log.info("updateExpClaimHeader - response: {}", response);

            // 检查响应结果并确保返回格式与 uploadDocument 一致
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson != null && responseJson.getIntValue("resCode") == 200000) {


                // 确保返回的数据结构包含 document_num，以保持与创建接口的一致性
                JSONObject data = responseJson.getJSONObject("data");
                if (data == null) {
                    data = new JSONObject();
                    responseJson.put("data", data);
                }

                JSONObject header = data.getJSONObject("header");
                if (header == null) {
                    header = new JSONObject();
                    data.put("header", header);
                }



                return responseJson.toJSONString();
            } else {

                return response;
            }

        } catch (Exception e) {

            throw new Exception("更新费用报销头部信息失败: " + e.getMessage(), e);
        }
    }
    //上传lovs
    public String uploadLovs(String requestParams) throws Exception {
      return sendPostRequest(lovUrl,requestParams);
   }

    public  String sendPostRequest(String basis_url, String requestParams) throws Exception {
//        String grant_type = "client_credentials";
//        String client_id = "cloudpense2483631363b";
//        String client_secret = "44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98";
        String access_token = getToken();
        String url = basis_url + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret+"&access_token="+access_token;
        log.info("url: " + url + " requestParams: " + requestParams);
        String response = HttpUtils.sendPostJson(url,requestParams);
        log.info("response: " + response);
        return response;
    }
    public  String getSignature(String document_num, String employee_number) throws Exception {
        String url = baseUrl+"/common/document/signature";
        JSONObject requestParams = new JSONObject();
        requestParams.put("bizId", createBizId());
        requestParams.put("timestamp", new Date().getTime());
        JSONObject data = new JSONObject();
        data.put("document_num",document_num);
        data.put("employee_number",employee_number);
        requestParams.put("data",data);
        return sendPostRequest(url,requestParams.toJSONString());
    }

    public  String getAuthorize(String user_id) throws Exception {
//        String url = "https://taliopenapi.cloudpense.com/common/oauth2/unAuth/authorize";
//        Date date = new Date();
//        JSONObject requestParams = new JSONObject();
//        requestParams.put("bizId", createBizId());
//        requestParams.put("timestamp", date.getTime());
//        JSONObject data = new JSONObject();
//        data.put("user_id",user_id);
//        data.put("company_code", SsoUtils.encrypt("akesobio","2cbb1520-33b7-44",null));
//        data.put("company_key","2cbb1520-33b7-44");
//        data.put("timestamp",date.getTime());
//        requestParams.put("data",data);
        return SsoUtils.encrypt(user_id+","+System.currentTimeMillis(),"06fc8604-1394-4b",null);
    }
    //生成业务ID
    public  String createBizId(){
       return UUID.randomUUID().toString();
    }

    /**
     * 值列表分页查询方法
     *
     * @param bizId 业务唯一识别码，最大长度255
     * @param timestamp 时间戳
     * @param lovName 值列表名称，最大长度64
     * @param pageNum 分页页数，默认1，最小值1
     * @param pageSize 分页条数，默认100，仅支持20、50、100
     * @param startDatetime 最后更新时间开始时间（13位时间戳）
     * @param endDatetime 最后更新时间结束时间（13位时间戳）
     * @param locale 语言环境代码，支持zh_CN、en_US、ja_JP、zh_TW，默认zh_CN
     * @return LovValuesPageResponse 值列表分页查询响应
     * @throws Exception 查询失败时抛出异常
     */
    public LovValuesPageResponse queryLovValuesByPage(String bizId, long timestamp, String lovName,
                                                     Integer pageNum, Integer pageSize,
                                                     Long startDatetime, Long endDatetime,
                                                     String locale) throws Exception {

        // 1. 参数验证
        if (!StringUtils.hasText(bizId) || bizId.length() > 255) {
            throw new IllegalArgumentException("bizId不能为空且长度不能超过255");
        }
        if (!StringUtils.hasText(lovName) || lovName.length() > 64) {
            throw new IllegalArgumentException("lovName不能为空且长度不能超过64");
        }

        // 设置默认值和验证
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 100;
        }
        if (pageSize != 20 && pageSize != 50 && pageSize != 100) {
            throw new IllegalArgumentException("pageSize仅支持20、50、100");
        }
        if (locale == null || locale.trim().isEmpty()) {
            locale = "zh_CN";
        }

        // 2. 构建请求URL
        String url = baseUrl + "/common/fndLovValues/v2/findByPage";

        // 3. 构建请求参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("grant_type", grant_type);
        paramMap.put("client_id", client_id);
        paramMap.put("client_secret", client_secret);
        paramMap.put("access_token", getToken());
        paramMap.put("bizId", bizId);
        paramMap.put("timestamp", String.valueOf(timestamp));
        paramMap.put("lovName", lovName);
        paramMap.put("pageNum", String.valueOf(pageNum));
        paramMap.put("pageSize", String.valueOf(pageSize));
        paramMap.put("locale", locale); // 添加多语言支持

        // 添加可选参数
        if (startDatetime != null) {
            paramMap.put("startDatetime", String.valueOf(startDatetime));
        }
        if (endDatetime != null) {
            paramMap.put("endDatetime", String.valueOf(endDatetime));
        }

        log.info("查询值列表参数: url={}, lovName={}, pageNum={}, pageSize={}, locale={}",
                url, lovName, pageNum, pageSize, locale);

        try {
            // 4. 发送GET请求
            String response = HttpUtils.sendGet(url, paramMap);
            log.info("值列表查询响应: {}", response);

            // 5. 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson == null) {
                throw new RuntimeException("响应解析失败，返回数据为空");
            }

            LovValuesPageResponse result = new LovValuesPageResponse();
            result.setResCode(responseJson.getInteger("resCode"));
            result.setResMsg(responseJson.getString("resMsg"));
            result.setBizId(responseJson.getString("bizId"));

            // 检查响应状态
            if (result.getResCode() == null || result.getResCode() != 200000) {
                log.error("值列表查询失败: resCode={}, resMsg={}", result.getResCode(), result.getResMsg());
                throw new RuntimeException("值列表查询失败: " + result.getResMsg());
            }

            // 获取data对象，分页信息和数据都在data字段内
            JSONObject dataObject = responseJson.getJSONObject("data");
            if (dataObject != null) {
                // 解析分页信息
                result.setPageNum(dataObject.getInteger("page_num"));
                result.setTotalPage(dataObject.getInteger("total_page"));
                result.setPageSize(dataObject.getInteger("page_size"));
                result.setTotalCount(dataObject.getInteger("total_count"));
                result.setIsFirstPage(dataObject.getBoolean("is_first_page"));
                result.setIsLastPage(dataObject.getBoolean("is_last_page"));

                // 解析数据列表
                JSONArray pageInfoArray = dataObject.getJSONArray("page_info");
                if (pageInfoArray != null) {
                    List<LovValueItem> pageInfo = new ArrayList<>();
                    for (int i = 0; i < pageInfoArray.size(); i++) {
                        JSONObject item = pageInfoArray.getJSONObject(i);
                        LovValueItem lovItem = new LovValueItem();
                        lovItem.setCode(item.getString("code"));
                        lovItem.setValueMeaning(item.getString("value_meaning"));
                        lovItem.setEnabledFlag(item.getString("enabled_flag"));
                        lovItem.setLastUpdateDate(item.getString("last_update_date"));
                        pageInfo.add(lovItem);
                    }
                    result.setPageInfo(pageInfo);
                }
            } else {
                log.warn("响应中未找到data字段，可能是API响应格式变更");
            }

            log.info("值列表查询成功: 共{}条记录，当前第{}页", result.getTotalCount(), result.getPageNum());
            return result;

        } catch (Exception e) {
            log.error("值列表查询异常: lovName={}, pageNum={}, pageSize={}", lovName, pageNum, pageSize, e);
            throw new Exception("值列表查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 值列表分页查询方法（简化版本，使用默认参数）
     *
     * @param lovName 值列表名称，最大长度64
     * @return LovValuesPageResponse 值列表分页查询响应
     * @throws Exception 查询失败时抛出异常
     */
    public LovValuesPageResponse queryLovValuesByPage(String lovName) throws Exception {
        return queryLovValuesByPage(createBizId(), System.currentTimeMillis(), lovName,
                                   1, 100, null, null, "zh_CN");
    }

    /**
     * 值列表分页查询方法（带分页参数版本）
     *
     * @param lovName 值列表名称，最大长度64
     * @param pageNum 分页页数，默认1，最小值1
     * @param pageSize 分页条数，默认100，仅支持20、50、100
     * @return LovValuesPageResponse 值列表分页查询响应
     * @throws Exception 查询失败时抛出异常
     */
    public LovValuesPageResponse queryLovValuesByPage(String lovName, Integer pageNum, Integer pageSize) throws Exception {
        return queryLovValuesByPage(createBizId(), System.currentTimeMillis(), lovName,
                                   pageNum, pageSize, null, null, "zh_CN");
    }

    /**
     * 值列表分页查询方法（带时间范围版本）
     *
     * @param lovName 值列表名称，最大长度64
     * @param pageNum 分页页数，默认1，最小值1
     * @param pageSize 分页条数，默认100，仅支持20、50、100
     * @param startDatetime 最后更新时间开始时间（13位时间戳）
     * @param endDatetime 最后更新时间结束时间（13位时间戳）
     * @param locale 语言环境代码，支持zh_CN、en_US、ja_JP、zh_TW，默认zh_CN
     * @return LovValuesPageResponse 值列表分页查询响应
     * @throws Exception 查询失败时抛出异常
     */
    public LovValuesPageResponse queryLovValuesByPage(String lovName, Integer pageNum, Integer pageSize,
                                                     Long startDatetime, Long endDatetime, String locale) throws Exception {
        return queryLovValuesByPage(createBizId(), System.currentTimeMillis(), lovName,
                                   pageNum, pageSize, startDatetime, endDatetime, locale);
    }

    /**
     * 根据文件名获取Content-Type (用于v3 API请求)
     * @param fileName 文件名
     * @return Content-Type字符串
     */
    private String getContentTypeByFileName(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFileName.endsWith(".txt")) {
            return "text/plain";
        } else if (lowerFileName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerFileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerFileName.endsWith(".ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (lowerFileName.endsWith(".pptx")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        } else if (lowerFileName.endsWith(".zip")) {
            return "application/x-zip-compressed";
        } else if (lowerFileName.endsWith(".rar")) {
            return "application/x-rar-compressed";
        } else if (lowerFileName.endsWith(".7z")) {
            return "application/x-7z-compressed";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * 根据文件扩展名获取Content-Type (用于文件上传时的Content-Type头)
     * @param filePath 文件路径
     * @return Content-Type字符串
     */
    private String getContentType(String filePath) {
        String fileName = filePath.toLowerCase();
        if (fileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".txt")) {
            return "text/plain";
        } else if (fileName.endsWith(".doc")) {
            return "application/msword";
        } else if (fileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (fileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (fileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (fileName.endsWith(".ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (fileName.endsWith(".pptx")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        } else if (fileName.endsWith(".zip")) {
            return "application/zip";
        } else if (fileName.endsWith(".rar")) {
            return "application/x-rar-compressed";
        } else if (fileName.endsWith(".7z")) {
            return "application/x-7z-compressed";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * 查询单据详情
     *
     * @param externalId 外部系统单据号或云简单据号，必填
     * @param locale 语言环境代码，支持zh_CN、en_US、ja_JP、zh_TW，默认zh_CN
     * @return 包含单据详情的JSON响应对象
     * @throws Exception 查询失败时抛出异常
     */
    public JSONObject getDocumentDetail(String externalId, String locale) throws Exception {
        // 1. 参数验证
        if (!StringUtils.hasText(externalId)) {
            throw new IllegalArgumentException("externalId不能为空");
        }

        // 设置默认语言环境
        if (locale == null || locale.trim().isEmpty()) {
            locale = "zh_CN";
        }

        // 验证语言环境参数
        if (!Arrays.asList("zh_CN", "zh_TW", "en_US", "ja_JP").contains(locale)) {
            throw new IllegalArgumentException("不支持的语言环境: " + locale + "，仅支持zh_CN、zh_TW、en_US、ja_JP");
        }

        try {
            // 2. 构建请求URL - 修复路径分隔符问题
            String url = baseUrl;
            if (!url.endsWith("/")) {
                url += "/";
            }
            url += "common/document/unique";

            // 3. 构建请求参数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("grant_type", grant_type);
            paramMap.put("client_id", client_id);
            paramMap.put("client_secret", client_secret);
            paramMap.put("access_token", getToken());
            paramMap.put("externalId", externalId);
            paramMap.put("locale", locale);

            log.info("查询单据详情 - url: {}, externalId: {}, locale: {}", url, externalId, locale);

            // 4. 发送GET请求
            String response = HttpUtils.sendGet(url, paramMap);
            log.info("单据详情查询响应: {}", response);

            // 5. 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson == null) {
                throw new RuntimeException("响应解析失败，返回数据为空");
            }

            // 6. 检查响应状态
            Integer resCode = responseJson.getInteger("resCode");
            String resMsg = responseJson.getString("resMsg");

            if (resCode == null) {
                throw new RuntimeException("响应格式异常，缺少resCode字段");
            }

            if (resCode == 200000) {
                // 成功响应
                log.info("单据详情查询成功 - externalId: {}", externalId);
                return responseJson;
            } else if (resCode == 206001) {
                // 单据不存在的特殊处理
                log.warn("单据不存在 - externalId: {}, resMsg: {}", externalId, resMsg);
                return responseJson;
            } else {
                // 其他错误
                log.error("单据详情查询失败 - externalId: {}, resCode: {}, resMsg: {}", externalId, resCode, resMsg);
                throw new RuntimeException("单据详情查询失败: " + resMsg);
            }

        } catch (Exception e) {
            log.error("单据详情查询异常 - externalId: {}, locale: {}", externalId, locale, e);
            throw new Exception("单据详情查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询单据详情（简化版本，使用默认中文语言环境）
     *
     * @param externalId 外部系统单据号或云简单据号，必填
     * @return 包含单据详情的JSON响应对象
     * @throws Exception 查询失败时抛出异常
     */
    public JSONObject getDocumentDetail(String externalId) throws Exception {
        return getDocumentDetail(externalId, "zh_CN");
    }

    /**
     * 为云简系统单据添加附件
     *
     * @param externalId 外部系统单据号或云简系统单据号，必填，最大64字符
     * @param attachmentsMap 附件对象映射，key为附件类型（如"attachments"、"attachments1"等），value为附件列表
     * @param locale 语言环境代码，支持zh_CN、zh_TW、en_US、ja_JP，默认zh_CN
     * @return 包含添加结果的JSON响应对象
     * @throws Exception 添加失败时抛出异常
     */
    public JSONObject addDocumentAttachments(String externalId, Map<String, List<Map<String, String>>> attachmentsMap, String locale) throws Exception {
        // 1. 参数验证
        if (!StringUtils.hasText(externalId)) {
            throw new IllegalArgumentException("externalId不能为空");
        }
        if (externalId.length() > 64) {
            throw new IllegalArgumentException("externalId长度不能超过64字符");
        }
        if (attachmentsMap == null || attachmentsMap.isEmpty()) {
            throw new IllegalArgumentException("attachmentsMap不能为空");
        }
        
        // 计算总附件数量
        int totalAttachments = attachmentsMap.values().stream()
                .mapToInt(List::size)
                .sum();
        if (totalAttachments > 50) {
            throw new IllegalArgumentException("附件总数量不能超过50个");
        }

        // 设置默认语言环境
        if (locale == null || locale.trim().isEmpty()) {
            locale = "zh_CN";
        }

        // 验证语言环境参数
        if (!Arrays.asList("zh_CN", "zh_TW", "en_US", "ja_JP").contains(locale)) {
            throw new IllegalArgumentException("不支持的语言环境: " + locale + "，仅支持zh_CN、zh_TW、en_US、ja_JP");
        }

        try {
            // 2. 构建请求URL - 修复路径分隔符问题
            String url = baseUrl;
            if (!url.endsWith("/")) {
                url += "/";
            }
            url += "common/document/addAttachments";

            // 3. 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("bizId", createBizId()); // 当前接口同步的唯一标识
            requestBody.put("timestamp", System.currentTimeMillis()); // 当前接口同步的时间戳

            // 构建data对象，包含附件数组
            JSONObject dataObject = new JSONObject();

            // 遍历attachmentsMap，为每种附件类型创建对应的数组
            for (Map.Entry<String, List<Map<String, String>>> entry : attachmentsMap.entrySet()) {
                String attachmentKey = entry.getKey(); // 如"attachments"、"attachments1"等
                List<Map<String, String>> attachmentsList = entry.getValue();
                
                if (attachmentsList != null && !attachmentsList.isEmpty()) {
                    JSONArray attachmentsArray = new JSONArray();
                    
                    for (Map<String, String> attachment : attachmentsList) {
                        // 构建单个附件对象
                        JSONObject attachmentObj = new JSONObject();
                        
                        // 添加可选字段
                        if (attachment.containsKey("file_name") && StringUtils.hasText(attachment.get("file_name"))) {
                            String fileName = attachment.get("file_name");
                            if (fileName.getBytes().length > 255) {
                                throw new IllegalArgumentException("文件名长度不能超过255字节");
                            }
                            attachmentObj.put("file_name", fileName);
                        }
                        
                        if (attachment.containsKey("attachment_url") && StringUtils.hasText(attachment.get("attachment_url"))) {
                            attachmentObj.put("attachment_url", attachment.get("attachment_url"));
                        }
                        
//                        if (attachment.containsKey("attachment_type_code") && StringUtils.hasText(attachment.get("attachment_type_code"))) {
//                            attachmentObj.put("attachment_type_code", attachment.get("attachment_type_code"));
//                        }
                        
                        // 只有当附件对象不为空时才添加到数组中
                        if (!attachmentObj.isEmpty()) {
                            attachmentsArray.add(attachmentObj);
                        }
                    }
                    
                    // 将该类型的附件数组添加到data对象中
                    if (!attachmentsArray.isEmpty()) {
                        dataObject.put(attachmentKey, attachmentsArray);
                    }
                }
            }

            requestBody.put("data", dataObject);

            // 4. 构建完整的请求URL（包含认证参数和externalId）
            String access_token = getToken();
            String fullUrl = url + "?grant_type=" + grant_type + "&client_id=" + client_id
                    + "&client_secret=" + client_secret + "&access_token=" + access_token
                    + "&externalId=" + externalId;

            log.info("添加单据附件 - url: {}, externalId: {}, locale: {}, attachments types: {}",
                    url, externalId, locale, attachmentsMap.keySet());

            // 5. 发送POST请求，添加locale头部
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            headers.put("locale", locale); // 设置多语言环境

            String response = HttpUtils.sendPostJson(fullUrl, requestBody.toJSONString(), headers);
            log.info("单据附件添加响应: {}", response);

            // 6. 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson == null) {
                throw new RuntimeException("响应解析失败，返回数据为空");
            }

            // 7. 检查响应状态
            Integer resCode = responseJson.getInteger("resCode");
            String resMsg = responseJson.getString("resMsg");

            if (resCode == null) {
                throw new RuntimeException("响应格式异常，缺少resCode字段");
            }

            if (resCode == 200000) {
                // 成功响应
                log.info("单据附件添加成功 - externalId: {}, 附件类型: {}", externalId, attachmentsMap.keySet());
                return responseJson;
            } else {
                // 错误响应
                log.error("单据附件添加失败 - externalId: {}, resCode: {}, resMsg: {}", externalId, resCode, resMsg);
                throw new RuntimeException("单据附件添加失败: " + resMsg);
            }

        } catch (Exception e) {
            log.error("单据附件添加异常 - externalId: {}, locale: {}, attachments types: {}",
                    externalId, locale, attachmentsMap.keySet(), e);
            throw new Exception("单据附件添加失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为云简系统单据添加附件（简化版本，使用默认中文语言环境）
     *
     * @param externalId 外部系统单据号或云简系统单据号，必填，最大64字符
     * @param attachmentsMap 附件对象映射，key为附件类型（如"attachments"、"attachments1"等），value为附件列表
     * @return 包含添加结果的JSON响应对象
     * @throws Exception 添加失败时抛出异常
     */
    public JSONObject addDocumentAttachments(String externalId, Map<String, List<Map<String, String>>> attachmentsMap) throws Exception {
        return addDocumentAttachments(externalId, attachmentsMap, "zh_CN");
    }

    /**
     * 为云简系统单据添加附件（兼容原有接口）
     *
     * @param externalId 外部系统单据号或云简系统单据号，必填，最大64字符
     * @param attachments 附件对象列表，每个附件包含file_name、attachment_url、attachment_type_code
     * @param locale 语言环境代码，支持zh_CN、zh_TW、en_US、ja_JP，默认zh_CN
     * @return 包含添加结果的JSON响应对象
     * @throws Exception 添加失败时抛出异常
     */
    public JSONObject addDocumentAttachments(String externalId, List<Map<String, String>> attachments, String locale) throws Exception {
        Map<String, List<Map<String, String>>> attachmentsMap = new HashMap<>();
        attachmentsMap.put("attachments", attachments);
        return addDocumentAttachments(externalId, attachmentsMap, locale);
    }

    /**
     * 为云简系统单据添加附件（兼容原有接口，简化版本，使用默认中文语言环境）
     *
     * @param externalId 外部系统单据号或云简系统单据号，必填，最大64字符
     * @param attachments 附件对象列表，每个附件包含file_name、attachment_url、attachment_type_code
     * @return 包含添加结果的JSON响应对象
     * @throws Exception 添加失败时抛出异常
     */
    public JSONObject addDocumentAttachments(String externalId, List<Map<String, String>> attachments) throws Exception {
        return addDocumentAttachments(externalId, attachments, "zh_CN");
    }

    /**
     * 为云简系统单据添加单个附件（便捷方法）
     *
     * @param externalId 外部系统单据号或云简系统单据号，必填，最大64字符
     * @param fileName 文件名，可选，最大255字节
     * @param attachmentUrl 文件地址，可选，需通过文件上传接口预先获取
     * @param attachmentTypeCode 附件类型编码，可选
     * @param locale 语言环境代码，支持zh_CN、zh_TW、en_US、ja_JP，默认zh_CN
     * @return 包含添加结果的JSON响应对象
     * @throws Exception 添加失败时抛出异常
     */
    public JSONObject addDocumentAttachment(String externalId, String fileName, String attachmentUrl,
                                          String attachmentTypeCode, String locale) throws Exception {
        List<Map<String, String>> attachments = new ArrayList<>();
        Map<String, String> attachment = new HashMap<>();

        if (StringUtils.hasText(fileName)) {
            attachment.put("file_name", fileName);
        }
        if (StringUtils.hasText(attachmentUrl)) {
            attachment.put("attachment_url", attachmentUrl);
        }
        if (StringUtils.hasText(attachmentTypeCode)) {
            attachment.put("attachment_type_code", attachmentTypeCode);
        }

        if (!attachment.isEmpty()) {
            attachments.add(attachment);
        } else {
            throw new IllegalArgumentException("至少需要提供一个附件属性（文件名、附件URL或附件类型编码）");
        }

        return addDocumentAttachments(externalId, attachments, locale);
    }

    /**
     * 为云简系统单据添加单个附件（简化版本，使用默认中文语言环境）
     *
     * @param externalId 外部系统单据号或云简系统单据号，必填，最大64字符
     * @param fileName 文件名，可选，最大255字节
     * @param attachmentUrl 文件地址，可选，需通过文件上传接口预先获取
     * @param attachmentTypeCode 附件类型编码，可选
     * @return 包含添加结果的JSON响应对象
     * @throws Exception 添加失败时抛出异常
     */
    public JSONObject addDocumentAttachment(String externalId, String fileName, String attachmentUrl,
                                          String attachmentTypeCode) throws Exception {
        return addDocumentAttachment(externalId, fileName, attachmentUrl, attachmentTypeCode, "zh_CN");
    }

    /**
     * 删除云简系统单据附件
     *
     * @param documentNum 单据号，必填，最大64字符
     * @param attachmentUrl 附件地址，必填，最大255字符
     * @param locale 语言环境代码，支持zh_CN、zh_TW、en_US、ja_JP，默认zh_CN
     * @return 包含删除结果的JSON响应对象
     * @throws Exception 删除失败时抛出异常
     */
    public JSONObject deleteDocumentAttachment(String documentNum, String attachmentUrl, String locale) throws Exception {
        // 1. 参数验证
        if (!StringUtils.hasText(documentNum)) {
            throw new IllegalArgumentException("documentNum不能为空");
        }
        if (documentNum.length() > 64) {
            throw new IllegalArgumentException("documentNum长度不能超过64字符");
        }
        if (!StringUtils.hasText(attachmentUrl)) {
            throw new IllegalArgumentException("attachmentUrl不能为空");
        }
        if (attachmentUrl.length() > 255) {
            throw new IllegalArgumentException("attachmentUrl长度不能超过255字符");
        }

        // 设置默认语言环境
        if (locale == null || locale.trim().isEmpty()) {
            locale = "zh_CN";
        }

        // 验证语言环境参数
        if (!Arrays.asList("zh_CN", "zh_TW", "en_US", "ja_JP").contains(locale)) {
            throw new IllegalArgumentException("不支持的语言环境: " + locale + "，仅支持zh_CN、zh_TW、en_US、ja_JP");
        }

        try {
            // 2. 构建请求URL - 修复路径分隔符问题
            String url = baseUrl;
            if (!url.endsWith("/")) {
                url += "/";
            }
            url += "common/document/attachment";

            // 3. 构建请求参数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("grant_type", grant_type);
            paramMap.put("client_id", client_id);
            paramMap.put("client_secret", client_secret);
            paramMap.put("access_token", getToken());
            paramMap.put("documentNum", documentNum);
            paramMap.put("attachmentUrl", attachmentUrl);
            paramMap.put("bizId", createBizId()); // 业务唯一识别码
            paramMap.put("timestamp", String.valueOf(System.currentTimeMillis())); // 时间戳
            paramMap.put("locale", locale);

            // 4. 构建完整的请求URL（包含认证参数）
            StringBuilder fullUrl = new StringBuilder(url);
            fullUrl.append("?");
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                fullUrl.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            // 删除最后一个多余的 "&" 符号
            if (fullUrl.charAt(fullUrl.length() - 1) == '&') {
                fullUrl.deleteCharAt(fullUrl.length() - 1);
            }

            log.info("删除单据附件 - url: {}, documentNum: {}, attachmentUrl: {}, locale: {}", 
                    fullUrl.toString(), documentNum, attachmentUrl, locale);

            // 5. 发送DELETE请求 - 使用RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Accept", "application/json");
            headers.set("locale", locale); // 设置多语言环境
            
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);
            
            try {
                restTemplate.delete(fullUrl.toString(), requestEntity);
                // DELETE请求成功通常返回空响应，构建标准成功响应
                JSONObject successResponse = new JSONObject();
                successResponse.put("resCode", 200000);
                successResponse.put("resMsg", "success");
                successResponse.put("bizId", paramMap.get("bizId"));
                
                String response = successResponse.toJSONString();
                log.info("单据附件删除响应: {}", response);

                // 成功响应
                log.info("单据附件删除成功 - documentNum: {}, attachmentUrl: {}", documentNum, attachmentUrl);
                return successResponse;
                
            } catch (org.springframework.web.client.HttpClientErrorException | org.springframework.web.client.HttpServerErrorException e) {
                // HTTP错误响应处理
                String responseBody = e.getResponseBodyAsString();
                log.error("单据附件删除失败 - HTTP错误: {}, 响应: {}", e.getStatusCode(), responseBody);
                
                // 尝试解析错误响应
                try {
                    JSONObject errorResponse = JSON.parseObject(responseBody);
                    Integer resCode = errorResponse.getInteger("resCode");
                    String resMsg = errorResponse.getString("resMsg");
                    throw new RuntimeException("单据附件删除失败: " + resMsg + " (HTTP " + e.getStatusCode() + ")");
                } catch (Exception parseException) {
                    throw new RuntimeException("单据附件删除失败: HTTP " + e.getStatusCode() + " - " + responseBody);
                }
            }

        } catch (Exception e) {
            log.error("单据附件删除异常 - documentNum: {}, attachmentUrl: {}, locale: {}", 
                    documentNum, attachmentUrl, locale, e);
            throw new Exception("单据附件删除失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除云简系统单据附件（简化版本，使用默认中文语言环境）
     *
     * @param documentNum 单据号，必填，最大64字符
     * @param attachmentUrl 附件地址，必填，最大255字符
     * @return 包含删除结果的JSON响应对象
     * @throws Exception 删除失败时抛出异常
     */
    public JSONObject deleteDocumentAttachment(String documentNum, String attachmentUrl) throws Exception {
        return deleteDocumentAttachment(documentNum, attachmentUrl, "zh_CN");
    }

    /**
     * 批量同步成本中心部门信息
     * 
     * @param costCenterDepartments 成本中心部门数据列表
     * @param locale 语言环境(zh_CN/en_US/ja_JP/zh_TW)
     * @return 接口响应结果
     * @throws Exception 同步失败时抛出异常
     */
    public CostCenterDepartmentSyncResponse batchSyncCostCenterDepartments(List<CostCenterDepartmentData> costCenterDepartments, String locale) throws Exception {
        // 1. 参数校验
        if (costCenterDepartments == null || costCenterDepartments.isEmpty()) {
            throw new IllegalArgumentException("成本中心部门数据不能为空");
        }
        
        if (costCenterDepartments.size() > 200) {
            throw new IllegalArgumentException("单次数据量不能超过200条，当前数量: " + costCenterDepartments.size());
        }
        
        // 设置默认语言环境
        if (locale == null || locale.trim().isEmpty()) {
            locale = "zh_CN";
        }
        
        // 验证语言环境参数
        if (!Arrays.asList("zh_CN", "en_US", "ja_JP", "zh_TW").contains(locale)) {
            throw new IllegalArgumentException("不支持的语言环境: " + locale + "，仅支持zh_CN、en_US、ja_JP、zh_TW");
        }

        try {
            // 2. 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("bizId", createBizId()); // 生成唯一标识
            requestBody.put("timestamp", System.currentTimeMillis()); // 当前时间戳
            
            JSONArray dataArray = new JSONArray();
            for (CostCenterDepartmentData costCenterData : costCenterDepartments) {
                JSONObject dataItem = new JSONObject();
                dataItem.put("code", costCenterData.getCode());
                
                JSONArray departmentCostList = new JSONArray();
                for (DepartmentCostInfo deptInfo : costCenterData.getDepartmentCostList()) {
                    JSONObject deptItem = new JSONObject();
                    deptItem.put("department_code", deptInfo.getDepartmentCode());
                    deptItem.put("sequence_num", deptInfo.getSequenceNum());
                    deptItem.put("child_include_flag", deptInfo.getChildIncludeFlag());
                    departmentCostList.add(deptItem);
                }
                dataItem.put("department_cost_list", departmentCostList);
                dataArray.add(dataItem);
            }
            requestBody.put("data", dataArray);

            // 3. 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            headers.put("locale", locale); // 设置多语言环境

            // 4. 构建完整的URL
            String access_token = getToken();
            String url = costCenterDepartmentBatchUrl + "?grant_type=" + grant_type + "&client_id=" + client_id
                    + "&client_secret=" + client_secret + "&access_token=" + access_token;

            log.info("批量同步成本中心部门 - url: {}, locale: {}, 数据条数: {}", url, locale, costCenterDepartments.size());
            log.debug("请求体: {}", requestBody.toJSONString());

            // 5. 发送请求
            String response = HttpUtils.sendPostJson(url, requestBody.toJSONString(), headers);
            log.info("成本中心部门同步响应: {}", response);

            // 6. 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson == null) {
                throw new RuntimeException("响应解析失败，返回数据为空");
            }

            CostCenterDepartmentSyncResponse result = JSON.parseObject(response, CostCenterDepartmentSyncResponse.class);
            
            // 7. 检查响应状态
            if (result.getResCode() == null) {
                throw new RuntimeException("响应格式异常，缺少resCode字段");
            }

            if (result.getResCode() == 200000) {
                // 成功响应
                log.info("成本中心部门同步成功 - bizId: {}, 数据条数: {}", result.getBizId(), costCenterDepartments.size());
                return result;
            } else {
                // 错误响应
                log.error("成本中心部门同步失败 - resCode: {}, resMsg: {}", result.getResCode(), result.getResMsg());
                throw new RuntimeException("成本中心部门同步失败: " + result.getResMsg());
            }

        } catch (Exception e) {
            log.error("成本中心部门同步异常 - locale: {}, 数据条数: {}", locale, costCenterDepartments.size(), e);
            throw new Exception("成本中心部门同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量同步成本中心部门信息（简化版本，使用默认中文语言环境）
     * 
     * @param costCenterDepartments 成本中心部门数据列表
     * @return 接口响应结果
     * @throws Exception 同步失败时抛出异常
     */
    public CostCenterDepartmentSyncResponse batchSyncCostCenterDepartments(List<CostCenterDepartmentData> costCenterDepartments) throws Exception {
        return batchSyncCostCenterDepartments(costCenterDepartments, "zh_CN");
    }

}
