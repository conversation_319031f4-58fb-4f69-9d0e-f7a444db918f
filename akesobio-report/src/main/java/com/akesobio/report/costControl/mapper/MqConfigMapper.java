package com.akesobio.report.costControl.mapper;

import com.akesobio.report.costControl.domain.MqConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 虚拟岗校验规则配置 数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface MqConfigMapper extends BaseMapper<MqConfig>
{
    /**
     * 查询虚拟岗校验规则配置信息
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 虚拟岗校验规则配置信息
     */
    public MqConfig selectMqConfig(MqConfig mqConfig);

    /**
     * 通过ID查询虚拟岗校验规则配置
     * 
     * @param id 配置ID
     * @return 虚拟岗校验规则配置信息
     */
    public MqConfig selectMqConfigById(Long id);
    
    /**
     * 查询虚拟岗校验规则配置列表
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 虚拟岗校验规则配置集合
     */
    public List<MqConfig> selectMqConfigList(MqConfig mqConfig);

    /**
     * 根据接口名称查询虚拟岗校验规则配置信息
     * 
     * @param interfaceName 接口名称
     * @return 虚拟岗校验规则配置信息
     */
    public MqConfig checkInterfaceNameUnique(String interfaceName);

    /**
     * 新增虚拟岗校验规则配置
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    public int insertMqConfig(MqConfig mqConfig);

    /**
     * 修改虚拟岗校验规则配置
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    public int updateMqConfig(MqConfig mqConfig);

    /**
     * 删除虚拟岗校验规则配置
     * 
     * @param id 配置ID
     * @return 结果
     */
    public int deleteMqConfigById(Long id);

    /**
     * 批量删除虚拟岗校验规则配置
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteMqConfigByIds(Long[] ids);
}
