package com.akesobio.report.hr.domain.dto;

import com.akesobio.report.hr.domain.entity.OvertimeReportEmployeeMonthly;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 员工加班报表DTO
 *
 * @name: EmployeeOvertimeReportDTO
 * @author: shenglin.qin
 * @date: 2023-06-13 10:24
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeOvertimeReportDTO {

    /** id */
    private String id;

    /** 姓名 */
    private String name;

    /** 性别 */
    private String sex;

    /** 身份证 */
    private String idCard;

    /** 出生日期 */
    private Date birthday;

    /** 邮箱 */
    private String email;

    /** 入职日期 */
    private Date entryDate;

    /** 状态 */
    private String status;

    /** 手机 */
    private String mobile;

    /** 公司id */
    private String orgId;

    /** 部门id */
    private String deptId;

    /** 登录名、工号 */
    private String loginName;

    /** 岗位 */
    private String post;

    /** 公司名称 */
    private String orgName;

    /** 末端部门 */
    private String deptName;

    /** 部门全称 */
    private String deptNameAll;

    /** 职级 */
    private String jobGrade;

    /**
     * 一级部门
     */
    private String firstDept;

    /**
     * 二级部门
     */
    private String secondDept;

    /**
     * 三级部门
     */
    private String thirdDept;

    /**
     * 四级部门
     */
    private String fourthDept;

    /**
     * 部门加班月份数据信息
     */
    private List<OvertimeReportEmployeeMonthly> overtimeReportEmployeeMonthlyList;

}
