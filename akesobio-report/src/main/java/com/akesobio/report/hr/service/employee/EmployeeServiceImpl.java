package com.akesobio.report.hr.service.employee;

import cn.hutool.core.util.StrUtil;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.utils.DateUtils;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.common.utils.collection.CollectionUtils;
import com.akesobio.common.utils.poi.EasyExcelUtils;
import com.akesobio.common.utils.poi.listener.ExcelListener;
import com.akesobio.report.hr.common.constant.CommonConstant;
import com.akesobio.report.hr.common.constant.DateConstant;
import com.akesobio.report.hr.common.constant.PersonnelConstant;
import com.akesobio.report.hr.controller.employee.file.vo.EmployeeFileVO;
import com.akesobio.report.hr.controller.employee.file.vo.ResignationEmployeeFileVO;
import com.akesobio.report.hr.controller.employee.vo.AnnualReportQuery;
import com.akesobio.report.hr.controller.employee.vo.ManpowerStructureQuery;
import com.akesobio.report.hr.controller.kpi.vo.promotion.PromotionQuery;
import com.akesobio.report.hr.convert.personnel.EmployeeConvert;
import com.akesobio.report.hr.dal.dataobject.employee.file.Employee;
import com.akesobio.report.hr.dal.dataobject.employee.file.ResignationProcessInfo;
import com.akesobio.report.hr.dal.dataobject.kpi.EmployeeKpi;
import com.akesobio.report.hr.dal.mapper.employee.file.EmployeeMapper;
import com.akesobio.report.hr.dal.mapper.employee.file.ResignationProcessInfoMapper;
import com.akesobio.report.hr.dal.mapper.employee.file.query.EmployeeQuery;
import com.akesobio.report.hr.domain.common.ImportArchiveData;
import com.akesobio.report.hr.domain.common.TreeData;
import com.akesobio.report.hr.domain.entity.*;
import com.akesobio.report.hr.domain.query.overtime.EmployeeReportQuery;
import com.akesobio.report.hr.mapper.*;
import com.akesobio.report.hr.service.employee.bo.Headcount;
import com.akesobio.report.hr.service.employee.bo.ManpowerStructure;
import com.akesobio.report.hr.service.employee.bo.PersonnelReport;
import com.akesobio.report.hr.service.employee.bo.structureCount.Age;
import com.akesobio.report.hr.service.employee.bo.structureCount.CurrentSeniority;
import com.akesobio.report.hr.service.employee.bo.structureCount.Education;
import com.akesobio.report.hr.service.employee.bo.structureCount.Gender;
import com.akesobio.report.hr.service.kpi.KpiService;
import com.akesobio.report.hr.utils.EmployeeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.akesobio.common.core.domain.AjaxResult.error;
import static com.akesobio.common.core.domain.AjaxResult.success;
import static com.akesobio.common.utils.collection.CollectionUtils.*;
import static com.akesobio.report.hr.common.constant.EmployeeConstant.DateCalcUnitEnum.YEAR;
import static com.akesobio.report.hr.common.constant.EmployeeConstant.SeniorityType.CURRENT_SENIORITY;
import static com.akesobio.report.hr.domain.constant.DbConstant.DB_MAX_PARAM_NUM;
import static com.akesobio.report.hr.utils.EmployeeUtils.calcSeniority;

/**
 * 员工信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmployeeServiceImpl implements IEmployeeService {
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private KpiService kpiService;
    @Autowired
    private HrStaffPersonInfoMapper personInfoMapper;
    @Autowired
    private HrStaffEkpInfoMapper ekpInfoMapper;
    @Autowired
    private HrStaffPersonExpEducMapper expEducMapper;
    @Autowired
    private HrStaffPersonExpContMapper expContMapper;
    @Autowired
    private HrStaffPersonExpQualMapper expQualMapper;
    @Autowired
    private ResignationProcessInfoMapper resignationProcessInfoMapper;

    private static final Set<String> VP_SET = Sets.newHashSet("1794b269c31974a6a6ec3054b2ab1778", "1794b269c4426f50ae993044d089ef64", "1794b269c1852ab05310b8e413795ba5", "1794b7c0ea6f2dc1f9e02314fcbb65a4", "1794b269c5856907c85f9204b2999366", "17b28a7b7d4dd0f87d51cfe440b8c0cf", "17efdeda1d96080a17570064b6283269", "1878cacf9b1f061bcffa8884f38a2e5c", "17d2129e46227ba70ca72e34fb49ce7a");
    private static final Set<String> EDUCATION_SET = Sets.newHashSet("博士", "硕士", "本科", "大专");
    public static final int CURRENT_YEAR = LocalDate.now().getYear();
    /**
     * 上传的文件允许的后缀
     */
    private static final Set<String> FILE_SUFFIX_ALLOW_SET = Sets.newHashSet("xlsx", "xls");

    public static final String RESIGNATION_SUMMARY_TEMPLATE = "离职原因说明：{0}；\n面谈情况说明：{1}；\nHR面谈情况说明：{2}";


    @Override
    public List<Employee> selectEmployeeByJobNumbers(String[] jobNumbers) {
        // 因为曾使用工号的存在要遍历两次，所以这里的上限得改为数据库最大参数数量的一半
        if (jobNumbers.length > DB_MAX_PARAM_NUM / 2) {
            List<Employee> employeeAll = list(new Employee());
            List<String> jobNumberList = Arrays.asList(jobNumbers);
            Set<String> jobNumberSet = new HashSet<>(jobNumberList);
            return CollectionUtils.filterList(employeeAll, employee -> jobNumberSet.contains(employee.getJobNumber()) || jobNumberSet.contains(employee.getJobNumberUsedBefore()));
        }
        return employeeMapper.selectEmployeeByJobNumbers(jobNumbers);
    }

    @Override
    public List<Employee> list(Employee employee) {
        return employeeMapper.selectEmployeeList(employee);
    }

    @Override
    public List<Employee> list(EmployeeQuery query) {
        return employeeMapper.selectList(query);
    }

    @Override
    public List<Employee> listFilterNoKpi(EmployeeQuery query) {
        return employeeMapper.selectHaveKpi(query);
    }

    @Override
    public Map<String, Employee> map(Collection<String> jobNumbers) {
        List<Employee> employeeAll;
        if (jobNumbers.size() < DB_MAX_PARAM_NUM) {
            employeeAll = selectEmployeeByJobNumbers(jobNumbers.toArray(new String[0]));
        } else {
            employeeAll = list(new Employee());
        }
        return convertMap(employeeAll, Employee::getJobNumber);
    }

    @Override
    public List<Employee> listFilterNoOvertime(EmployeeReportQuery query) {
        return employeeMapper.selectEmployeeFilterNoOvertime(query);
    }

    @Override
    public List<Employee> listFilterNoAskForLeaveByYear(EmployeeReportQuery query, boolean isSpecial) {
        LocalDate entryDateLocal;
        LocalDate leaveDateLocal;
        if (query.getYear() == LocalDate.now().getYear()) {
            entryDateLocal = LocalDate.now().minusDays(1);
            // 如果查询本年的，则只查询在职员工和上个月之后离职的员工
            leaveDateLocal = LocalDate.now().minusMonths(1).withDayOfMonth(1);
        } else {
            entryDateLocal = LocalDate.of(query.getYear(), 12, 31);
            leaveDateLocal = LocalDate.of(query.getYear(), 12, 31);
        }
        Date entryDate = Date.from(entryDateLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date leaveDate = Date.from(leaveDateLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        return employeeMapper.selectEmployeeFilterNoAskForLeaveByYear(query, entryDate, leaveDate, isSpecial);
    }

    @Override
    public EmployeeFileVO get(String id) {
        Employee basic = employeeMapper.selectEmployeeById(id);
        if (basic == null) {
            return null;
        }
        HrStaffPersonInfo personInfo = personInfoMapper.selectHrStaffPersonInfoByFdId(id);
        HrStaffEkpInfo ekpInfo = ekpInfoMapper.selectHrStaffEkpInfoByFdId(id);
        HrStaffPersonExpCont contInfo = expContMapper.selectHrStaffPersonExpContByEmpId(id);
        HrStaffPersonExpEduc educInfo = expEducMapper.selectHrStaffPersonExpEducByEmpId(id);
        List<HrStaffPersonExpQual> quals = expQualMapper.selectHrStaffPersonExpQualByEmpId(id);
        EmployeeFileVO detailVO = EmployeeConvert.INSTANCE.dos2detailVO(basic, personInfo, ekpInfo);
        handleDos2Vo(contInfo, educInfo, quals, detailVO);
        return detailVO;
    }

    @Override
    @SuppressWarnings("DuplicatedCode")
    public List<EmployeeFileVO> listFileNew(Employee employee) {
        List<Employee> employeeList = employeeMapper.selectEmployeeList(employee);
        ArrayList<EmployeeFileVO> resultList = new ArrayList<>();

        List<HrStaffPersonInfo> personList = personInfoMapper.selectHrStaffPersonInfoList(null);
        List<HrStaffEkpInfo> ekpList = ekpInfoMapper.selectHrStaffEkpInfoList(null);
        List<HrStaffPersonExpCont> contList = expContMapper.selectHrStaffPersonExpContList(null);
        List<HrStaffPersonExpEduc> educList = expEducMapper.selectHrStaffPersonExpEducList(null);
        List<HrStaffPersonExpQual> qualList = expQualMapper.selectHrStaffPersonExpQualList(null);

        // 把所有实体类集合做成Map，key为员工id
        Map<String, HrStaffPersonInfo> personMap = personList.stream().collect(Collectors.toMap(HrStaffPersonInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, HrStaffEkpInfo> ekpMap = ekpList.stream().collect(Collectors.toMap(HrStaffEkpInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, List<HrStaffPersonExpCont>> contMap = contList.stream().collect(Collectors.groupingBy(HrStaffPersonExpCont::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpEduc>> educMap = educList.stream().collect(Collectors.groupingBy(HrStaffPersonExpEduc::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpQual>> qualMap = qualList.stream().collect(Collectors.groupingBy(HrStaffPersonExpQual::getFdPersonInfoId));

        employeeList.forEach(e -> {
            String id = e.getId();
            HrStaffPersonInfo personInfo = new HrStaffPersonInfo();
            HrStaffEkpInfo ekpInfo = new HrStaffEkpInfo();
            HrStaffPersonExpCont contInfo = new HrStaffPersonExpCont();
            HrStaffPersonExpEduc educInfo = new HrStaffPersonExpEduc();
            List<HrStaffPersonExpQual> quals = new ArrayList<>();
            if (personMap.containsKey(id)) {
                personInfo = personMap.get(id);
            }
            if (ekpMap.containsKey(id)) {
                ekpInfo = ekpMap.get(id);
            }
            if (qualMap.containsKey(id)) {
                quals = qualMap.get(id);
            }
            if (contMap.containsKey(id)) {
                List<HrStaffPersonExpCont> empContList = contMap.get(id);
                Optional<HrStaffPersonExpCont> lastCont = empContList.stream().filter(cont -> cont.getContractEndDate() != null).min((c1, c2) -> c2.getContractEndDate().compareTo(c1.getContractEndDate()));
                if (lastCont.isPresent()) {
                    contInfo = lastCont.get();
                }
            }
            if (educMap.containsKey(id)) {
                List<HrStaffPersonExpEduc> empEducList = educMap.get(id);
                Optional<HrStaffPersonExpEduc> lastEduc = empEducList.stream().filter(educ -> educ.getGraduationDate() != null).min((e1, e2) -> e2.getGraduationDate().compareTo(e1.getGraduationDate()));
                if (lastEduc.isPresent()) {
                    educInfo = lastEduc.get();
                }
            }
            EmployeeFileVO detailVO = EmployeeConvert.INSTANCE.assignment(e, personInfo, ekpInfo);
            if (e.getLastLeaveDate() != null && e.getLastEntryDate() != null) {
                detailVO.setLastTermOfOffice(e.getLastEntryDate() + "至" + e.getLastLeaveDate());
            }
            handleDos2Vo(contInfo, educInfo, quals, detailVO);
            detailVO.setVp(e.getVpName());
            resultList.add(detailVO);
        });
        return resultList;
    }

    /**
     * 查询过去5年的绩效考核结果
     *
     * @param employeeIdSet 查询的员工 ID
     * @return 过去5年的绩效考核结果map
     */
    private Map<String, Map<Integer, EmployeeKpi>> getLast5YearsKpiMap(Set<String> employeeIdSet) {
        List<EmployeeKpi> kpiList = kpiService.listByEmployeeIdsAndYearRange(employeeIdSet, new Integer[]{CURRENT_YEAR - 5, CURRENT_YEAR - 1});
        return kpiList.stream()
                .collect(Collectors.groupingBy(
                        EmployeeKpi::getEmployeeId,
                        Collectors.toMap(EmployeeKpi::getYear, kpi -> kpi, (kpi1, kpi2) -> kpi1)
                ));
    }

    @Override
    public int updateByLoginName(ImportArchiveData o) {
        return personInfoMapper.updateByLoginName(o);
    }

    @Override
    public AjaxResult importData(MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            return error("请选择文件");
        }
        String fileName = file.getOriginalFilename();
        String suffixName = fileName == null ? null : fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        if (!FILE_SUFFIX_ALLOW_SET.contains(suffixName)) {
            return error("文件格式不正确");
        }
        List<ImportArchiveData> list = new EasyExcelUtils<ImportArchiveData>()
                .readExcelData(file, ImportArchiveData.class, new ExcelListener<ImportArchiveData>());
        ArrayList<ImportArchiveData> importArchiveDataFail = new ArrayList<>();
        list.forEach(e -> {
            int flag = this.updateByLoginName(e);
            if (flag < 1) {
                importArchiveDataFail.add(e);
            }
        });
        if (!importArchiveDataFail.isEmpty()) {
            log.info("导入失败 {} 条，共 {} 条", importArchiveDataFail.size(), list.size());
            log.info(importArchiveDataFail.toString());
        }
        return success("导入完成，成功 " + (list.size() - importArchiveDataFail.size()) + " 条，失败 " + importArchiveDataFail.size() + " 条");
    }

    @Override
    public List<EmployeeFileVO> queryEmployeeList(Employee employee) {
        List<Employee> employeeList = employeeMapper.selectEmployeeListD(employee);
        ArrayList<EmployeeFileVO> employeeFileList = new ArrayList<>();

        List<HrStaffPersonInfo> personList = personInfoMapper.selectHrStaffPersonInfoList(null);
        List<HrStaffEkpInfo> ekpList = ekpInfoMapper.selectHrStaffEkpInfoList(null);
        List<HrStaffPersonExpCont> contList = expContMapper.selectHrStaffPersonExpContList(null);
        List<HrStaffPersonExpEduc> educList = expEducMapper.selectHrStaffPersonExpEducList(null);
        List<HrStaffPersonExpQual> qualList = expQualMapper.selectHrStaffPersonExpQualList(null);

        // 把所有实体类集合做成Map，key为员工id
        Map<String, HrStaffPersonInfo> personMap = personList.stream().collect(Collectors.toMap(HrStaffPersonInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, HrStaffEkpInfo> ekpMap = ekpList.stream().collect(Collectors.toMap(HrStaffEkpInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, List<HrStaffPersonExpCont>> contMap = contList.stream().collect(Collectors.groupingBy(HrStaffPersonExpCont::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpEduc>> educMap = educList.stream().collect(Collectors.groupingBy(HrStaffPersonExpEduc::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpQual>> qualMap = qualList.stream().collect(Collectors.groupingBy(HrStaffPersonExpQual::getFdPersonInfoId));

        employeeList.forEach(e -> {
            String id = e.getId();
            HrStaffPersonInfo personInfo = new HrStaffPersonInfo();
            HrStaffEkpInfo ekpInfo = new HrStaffEkpInfo();
            HrStaffPersonExpCont contInfo = new HrStaffPersonExpCont();
            HrStaffPersonExpEduc educInfo = new HrStaffPersonExpEduc();
            List<HrStaffPersonExpQual> quals = new ArrayList<>();
            if (personMap.containsKey(id)) {
                personInfo = personMap.get(id);
            }
            if (ekpMap.containsKey(id)) {
                ekpInfo = ekpMap.get(id);
            }
            if (qualMap.containsKey(id)) {
                quals = qualMap.get(id);
            }
            if (contMap.containsKey(id)) {
                List<HrStaffPersonExpCont> empContList = contMap.get(id);
                Optional<HrStaffPersonExpCont> lastCont = empContList.stream().filter(cont -> cont.getContractEndDate() != null).min((c1, c2) -> c2.getContractEndDate().compareTo(c1.getContractEndDate()));
                if (lastCont.isPresent()) {
                    contInfo = lastCont.get();
                }
            }
            if (educMap.containsKey(id)) {
                List<HrStaffPersonExpEduc> empEducList = educMap.get(id);
                Optional<HrStaffPersonExpEduc> lastEduc = empEducList.stream().filter(educ -> educ.getGraduationDate() != null).min((e1, e2) -> e2.getGraduationDate().compareTo(e1.getGraduationDate()));
                if (lastEduc.isPresent()) {
                    educInfo = lastEduc.get();
                }
            }
            EmployeeFileVO detailVO = EmployeeConvert.INSTANCE.assignment(e, personInfo, ekpInfo);
            if (e.getLastLeaveDate() != null && e.getLastEntryDate() != null) {
                detailVO.setLastTermOfOffice(e.getLastEntryDate() + "至" + e.getLastLeaveDate());
            }
            handleDos2Vo(contInfo, educInfo, quals, detailVO);
            detailVO.setVp(e.getVpName());
            employeeFileList.add(detailVO);
        });
        return employeeFileList;
    }

    @Override
    public List<PersonnelReport> annualPersonnelReport(AnnualReportQuery query) {
        List<Employee> employeeList = employeeMapper.selectForPersonnelReport(query);
        // 搜索条件有部门没公司或者有公司没部门，自动切换主体
        if (StringUtils.isNotEmpty(query.getCompany()) && StringUtils.isEmpty(query.getDepartment())) {
            query.setMainBody(PersonnelConstant.ReportMainBody.COMPANY.getValue());
        } else if (StringUtils.isEmpty(query.getCompany()) && StringUtils.isNotEmpty(query.getDepartment())) {
            query.setMainBody(PersonnelConstant.ReportMainBody.DEPARTMENT.getValue());
        }
        // 把部门全称设置为一级部门，后续分组用
        employeeList.forEach(emp -> emp.setDeptNameAll(EmployeeUtils.getSubDept(emp.getDeptNameAll(), 1)));
        List<PersonnelReport> reportList = null;
        if (query.getMainBody() == PersonnelConstant.ReportMainBody.COMPANY.getValue()) {
            reportList = groupingForPersonnelReportAndHandleReport(employeeList, query.getYear(), PersonnelConstant.ReportMainBody.COMPANY, Employee::getOrgName, Employee::getDeptNameAll);
        } else if (query.getMainBody() == PersonnelConstant.ReportMainBody.DEPARTMENT.getValue()) {
            reportList = groupingForPersonnelReportAndHandleReport(employeeList, query.getYear(), PersonnelConstant.ReportMainBody.DEPARTMENT, Employee::getDeptNameAll, Employee::getOrgName);
        }
        return reportList;
    }

    @Override
    public List<ManpowerStructure> manpowerStructure(ManpowerStructureQuery query) {
        List<Employee> employeeList = employeeMapper.selectForManpowerStructure(query);
        // 搜索条件有部门没公司或者有公司没部门，自动切换主体
        if (StringUtils.isNotEmpty(query.getCompany()) && StringUtils.isEmpty(query.getDepartment())) {
            query.setMainBody(PersonnelConstant.ReportMainBody.COMPANY.getValue());
        } else if (StringUtils.isEmpty(query.getCompany()) && StringUtils.isNotEmpty(query.getDepartment())) {
            query.setMainBody(PersonnelConstant.ReportMainBody.DEPARTMENT.getValue());
        }
        // 把部门全称设置为一级部门，后续分组用
        employeeList.forEach(emp -> emp.setDeptNameAll(EmployeeUtils.getSubDept(emp.getDeptNameAll(), 1)));
        List<ManpowerStructure> structureList = null;
        if (query.getMainBody() == PersonnelConstant.ReportMainBody.COMPANY.getValue()) {
            structureList = groupingForManpowerStructureAndCountStructure(employeeList, PersonnelConstant.ReportMainBody.COMPANY, Employee::getOrgName, Employee::getDeptNameAll);
        } else if (query.getMainBody() == PersonnelConstant.ReportMainBody.DEPARTMENT.getValue()) {
            structureList = groupingForManpowerStructureAndCountStructure(employeeList, PersonnelConstant.ReportMainBody.DEPARTMENT, Employee::getDeptNameAll, Employee::getOrgName);
        }
        return structureList;
    }

    @Override
    public List<Employee> listHavePromotionQualification(PromotionQuery query) {
        return employeeMapper.selectHavePromotionQualification(query);

    }

    /**
     * 根据条件为人力结构分组遍历并统计人力结构
     *
     * @param employeeList                   员工集合
     * @param mainBodyType                   主体类型
     * @param mainBodyGroupingCondition      主体分类依据
     * @param secondaryBodyGroupingCondition 次级体分类依据
     * @return 人力结构集合
     */
    private List<ManpowerStructure> groupingForManpowerStructureAndCountStructure(List<Employee> employeeList,
                                                                                  PersonnelConstant.ReportMainBody mainBodyType,
                                                                                  Function<Employee, String> mainBodyGroupingCondition,
                                                                                  Function<Employee, String> secondaryBodyGroupingCondition) {
        List<ManpowerStructure> reportList = new ArrayList<>();
        // 根据主体分组条件分组转换成Map
        Map<String, List<Employee>> mainBodyUnsortedMap = convertMultiMap(employeeList, mainBodyGroupingCondition);
        List<Map.Entry<String, List<Employee>>> mainBodySortedEntryList = new ArrayList<>(mainBodyUnsortedMap.entrySet());
        // 根据主体的人数倒序排序
        mainBodySortedEntryList.sort((o1, o2) -> o2.getValue().size() - o1.getValue().size());
        // 所有的汇总
        mainBodySortedEntryList.forEach(item -> {
            String mainBodyName = item.getKey();
            List<Employee> mainBodyEmployeeList = item.getValue();
            Map<String, List<Employee>> secondaryBodyMap = convertMultiMap(mainBodyEmployeeList, secondaryBodyGroupingCondition);
            // 处理结构
            handleStructure(reportList, mainBodyType, mainBodyName, mainBodyEmployeeList, secondaryBodyMap);
        });
        ManpowerStructure total = new ManpowerStructure();
        if (PersonnelConstant.ReportMainBody.COMPANY.equals(mainBodyType)) {
            total.setCompany("总数");
        } else {
            total.setDepartment("总数");
        }
        countStructureDetail(total, employeeList);
        reportList.add(total);
        return reportList;
    }

    /**
     * 处理结构
     *
     * @param structureList        汇总的结构集合
     * @param mainBodyType         查询主体类型
     * @param mainBodyName         主体名称 (比如查询主体为公司则为公司名称)
     * @param mainBodyEmployeeList 主体员工集合 (比如查询主体为公司则为这个分公司下的员工集合)
     * @param secondaryBodyMap     次体Map (key:次体名称, value:次体员工集合 比如查询主体为公司，这个Map则键为部门，值为该部门的员工)
     */
    private void handleStructure(List<ManpowerStructure> structureList, PersonnelConstant.ReportMainBody mainBodyType,
                                 String mainBodyName, List<Employee> mainBodyEmployeeList, Map<String, List<Employee>> secondaryBodyMap) {
        ManpowerStructure mainBodyTotal = new ManpowerStructure();
        if (PersonnelConstant.ReportMainBody.COMPANY.equals(mainBodyType)) {
            mainBodyTotal.setCompany(mainBodyName);
            mainBodyTotal.setDepartment("汇总");
        } else {
            mainBodyTotal.setDepartment(mainBodyName);
            mainBodyTotal.setCompany("汇总");
        }
        // 计算主体的汇总结构
        countStructureDetail(mainBodyTotal, mainBodyEmployeeList);
        // 各次体的结构
        List<Map.Entry<String, List<Employee>>> secondaryBodySortedEntryList = new ArrayList<>(secondaryBodyMap.entrySet());
        secondaryBodySortedEntryList.sort((o1, o2) -> o2.getValue().size() - o1.getValue().size());
        secondaryBodySortedEntryList.forEach(secondaryBodyItem -> {
            String secondaryBodyName = secondaryBodyItem.getKey();
            List<Employee> departmentEmployeeList = secondaryBodyItem.getValue();
            ManpowerStructure departmentReport = new ManpowerStructure();
            if (PersonnelConstant.ReportMainBody.COMPANY.equals(mainBodyType)) {
                departmentReport.setCompany(mainBodyName);
                departmentReport.setDepartment(secondaryBodyName);
            } else {
                departmentReport.setDepartment(mainBodyName);
                departmentReport.setCompany(secondaryBodyName);
            }
            countStructureDetail(departmentReport, departmentEmployeeList);
            structureList.add(departmentReport);
        });
        structureList.add(mainBodyTotal);
    }

    /**
     * 计算人力结构详情
     *
     * @param counter      结构统计，已设置好主体和次体名称
     * @param employeeList 需要统计的员工列表
     */
    private void countStructureDetail(ManpowerStructure counter, List<Employee> employeeList) {
        Headcount headcount = countManpowerStructureHeadcount(employeeList);
        // 统计完上年人数和入离职相关之后把离职员工移除
        employeeList = employeeList.stream().filter(employee -> (!"离职".equals(employee.getEmployeeStatus()) && employee.getLeaveDate() == null)
                || (employee.getLeaveDate() != null && DateUtils.toLocalDate(employee.getLeaveDate()).isAfter(LocalDate.now())))
                .collect(Collectors.toList());
        Education education = countManpowerStructureEducation(employeeList);
        Age age = countManpowerStructureAge(employeeList);
        CurrentSeniority seniority = countManpowerStructureSeniority(employeeList);
        Gender gender = countManpowerStructureGender(employeeList);
        counter.setHeadcount(headcount);
        counter.setEducation(education);
        counter.setAge(age);
        counter.setSeniority(seniority);
        counter.setGender(gender);
    }

    /**
     * 统计人力结构的司龄人数结构
     *
     * @param employeeList 员工信息集合
     * @return 司龄人数结构
     */
    private static CurrentSeniority countManpowerStructureSeniority(List<Employee> employeeList) {
        CurrentSeniority seniority = new CurrentSeniority();
        int below1 = (int) employeeList.stream().filter(employee -> calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue() < 1).count();
        int year1To3 = (int) employeeList.stream().filter(employee -> 1 <= calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue()
                && calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue() < 3).count();
        int year3To5 = (int) employeeList.stream().filter(employee -> 3 <= calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue()
                && calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue() < 5).count();
        int year5To8 = (int) employeeList.stream().filter(employee -> 5 <= calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue()
                && calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue() < 8).count();
        int year8To15 = (int) employeeList.stream().filter(employee -> 8 <= calcSeniority(employee, CURRENT_SENIORITY, YEAR).doubleValue()).count();
        seniority.setBelow1(below1);
        seniority.setYear1To3(year1To3);
        seniority.setYear3To5(year3To5);
        seniority.setYear5To8(year5To8);
        seniority.setYear8To15(year8To15);
        return seniority;
    }

    /**
     * 统计人力结构的年龄人数结构
     *
     * @param employeeList 员工信息集合
     * @return 年龄人数结构
     */
    private static Age countManpowerStructureAge(List<Employee> employeeList) {
        Age age = new Age();
        int under25 = (int) employeeList.stream().filter(employee -> EmployeeUtils.getAge(employee) != null
                && EmployeeUtils.getAge(employee) < 25).count();
        int age25To35 = (int) employeeList.stream().filter(employee -> EmployeeUtils.getAge(employee) != null
                && 25 <= EmployeeUtils.getAge(employee) && EmployeeUtils.getAge(employee) < 35).count();
        int age35To45 = (int) employeeList.stream().filter(employee -> EmployeeUtils.getAge(employee) != null
                && 35 <= EmployeeUtils.getAge(employee) && EmployeeUtils.getAge(employee) < 45).count();
        int age45To55 = (int) employeeList.stream().filter(employee -> EmployeeUtils.getAge(employee) != null
                && 45 <= EmployeeUtils.getAge(employee) && EmployeeUtils.getAge(employee) < 55).count();
        int over55 = (int) employeeList.stream().filter(employee -> EmployeeUtils.getAge(employee) == null
                || EmployeeUtils.getAge(employee) >= 55).count();
        age.setUnder25(under25);
        age.setAge25To35(age25To35);
        age.setAge35To45(age35To45);
        age.setAge45To55(age45To55);
        age.setOver55(over55);
        return age;
    }

    /**
     * 统计人力结构的性别人数结构
     *
     * @param employeeList 员工信息集合
     * @return 性别人数结构
     */
    private static Gender countManpowerStructureGender(List<Employee> employeeList) {
        Gender gender = new Gender();
        int male = (int) employeeList.stream().filter(employee -> "男".equals(employee.getSex())).count();
        int female = (int) employeeList.stream().filter(employee -> "女".equals(employee.getSex())).count();
        gender.setMale(male);
        gender.setFemale(female);
        return gender;
    }

    /**
     * 统计人力结构的知识人数结构
     *
     * @param employeeList 员工信息集合
     * @return 知识人数结构
     */
    private static Education countManpowerStructureEducation(List<Employee> employeeList) {
        Education education = new Education();
        // 用contains是因为学历中有可能不是完全匹配，比如硕士研究生毕业
        int doctor = (int) employeeList.stream().filter(employee -> employee.getHighestEducation() != null
                && employee.getHighestEducation().contains("博士")).count();
        int master = (int) employeeList.stream().filter(employee -> employee.getHighestEducation() != null
                && employee.getHighestEducation().contains("硕士")).count();
        int bachelor = (int) employeeList.stream().filter(employee -> employee.getHighestEducation() != null
                && employee.getHighestEducation().contains("本科")).count();
        int associateDegree = (int) employeeList.stream().filter(employee -> employee.getHighestEducation() != null
                && employee.getHighestEducation().contains("大专")).count();
        int other = (int) employeeList.stream().filter(employee -> employee.getHighestEducation() == null
                || isOtherEducation(employee.getHighestEducation())).count();
        education.setDoctor(doctor);
        education.setMaster(master);
        education.setBachelor(bachelor);
        education.setAssociateDegree(associateDegree);
        education.setOther(other);
        return education;
    }

    /**
     * 是否其他学历
     *
     * @param target 需要判断的目标学历
     * @return 是否其他学历
     */
    private static boolean isOtherEducation(String target) {
        for (String key : EDUCATION_SET) {
            if (target.contains(key)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算人力结构增减人数相关
     *
     * @param employeeList 员工信息集合
     * @return 人数统计业务对象
     */
    private static Headcount countManpowerStructureHeadcount(List<Employee> employeeList) {
        Headcount headcount = new Headcount();
        LocalDate now = LocalDate.now();
        int lastYear = (int) employeeList.stream().filter(employee -> DateUtils.toLocalDate(employee.getEntryDate()).getYear() != now.getYear()).count();
        int lastMonthIncrease = (int) employeeList.stream().filter(employee -> DateUtils.toLocalDate(employee.getEntryDate()).getYear() == now.getYear()
                && DateUtils.toLocalDate(employee.getEntryDate()).getMonthValue() == now.getMonthValue()).count();
        int lastMonthDecrease = (int) employeeList.stream().filter(employee -> employee.getLeaveDate() != null
                && DateUtils.toLocalDate(employee.getLeaveDate()).getYear() == now.getYear()
                && DateUtils.toLocalDate(employee.getLeaveDate()).getMonthValue() == now.getMonthValue()).count();
        // 统计完上年人数和入离职相关之后把离职员工移除
        employeeList = employeeList.stream().filter(employee -> (!"离职".equals(employee.getEmployeeStatus()) && employee.getLeaveDate() == null)
                || (employee.getLeaveDate() != null && DateUtils.toLocalDate(employee.getLeaveDate()).isAfter(now)))
                .collect(Collectors.toList());
        int realtime = employeeList.size();
        int intern = (int) employeeList.stream().filter(employee -> "在职".equals(employee.getEmployeeStatus()) && "实习".equals(employee.getEmployeeType())).count();
        headcount.setLastTime(lastYear);
        headcount.setIncrease(lastMonthIncrease);
        headcount.setDecrease(lastMonthDecrease);
        headcount.setRealtime(realtime);
        headcount.setIntern(intern);
        return headcount;
    }

    /**
     * 为HR年报分组归类并统计报表
     *
     * @param employeeList                   员工集合
     * @param year                           所属年份
     * @param mainBodyType                   主体类型
     * @param mainBodyGroupingCondition      主体分组条件
     * @param secondaryBodyGroupingCondition 次体分组条件
     * @return 报表集合
     */
    private List<PersonnelReport> groupingForPersonnelReportAndHandleReport(List<Employee> employeeList, int year,
                                                                            PersonnelConstant.ReportMainBody mainBodyType,
                                                                            Function<Employee, String> mainBodyGroupingCondition,
                                                                            Function<Employee, String> secondaryBodyGroupingCondition) {
        List<PersonnelReport> reportList = new ArrayList<>();
        Map<String, List<Employee>> mainBodyMap = convertMultiMap(employeeList, mainBodyGroupingCondition);

        mainBodyMap.forEach((mainBodyName, mainBodyEmployeeList) -> {
            try {
                System.out.println("开始处理主体: " + mainBodyName + ", 员工数量: " + mainBodyEmployeeList.size());
                // Map<String, List<Employee>> secondaryBodyMap = convertMultiMap(mainBodyEmployeeList, secondaryBodyGroupingCondition);
                // 使用方法引用时处理null
                Map<String, List<Employee>> secondaryBodyMap = mainBodyEmployeeList.stream()
                        .collect(Collectors.groupingBy(
                                emp -> Optional.ofNullable(secondaryBodyGroupingCondition.apply(emp))
                                        .orElse("部门已变更或删除")
                        ));
                System.out.println("二级分组结果: " + secondaryBodyMap.keySet());
                // 主体总数
                PersonnelReport mainBodyTotal = new PersonnelReport();
                if (PersonnelConstant.ReportMainBody.COMPANY.equals(mainBodyType)) {
                    mainBodyTotal.setCompany(mainBodyName);
                    mainBodyTotal.setDepartment("汇总");
                } else {
                    mainBodyTotal.setDepartment(mainBodyName);
                    mainBodyTotal.setCompany("汇总");
                }
                // 处理公司所有部门汇总
                handleMonthlyReport(mainBodyTotal, mainBodyEmployeeList, year);
                // 各部门的数据统计
                secondaryBodyMap.forEach((secondaryBodyName, secondaryBodyEmployeeList) -> {
                    System.out.println("处理二级主体: " + secondaryBodyName);
                    PersonnelReport secondaryBodyReport = new PersonnelReport();
                    if (PersonnelConstant.ReportMainBody.COMPANY.equals(mainBodyType)) {
                        secondaryBodyReport.setCompany(mainBodyName);
                        secondaryBodyReport.setDepartment(secondaryBodyName);
                    } else {
                        secondaryBodyReport.setDepartment(mainBodyName);
                        secondaryBodyReport.setCompany(secondaryBodyName);
                    }
                    handleMonthlyReport(secondaryBodyReport, secondaryBodyEmployeeList, year);
                    reportList.add(secondaryBodyReport);
                });
                reportList.add(mainBodyTotal);
            } catch (Exception e) {
                System.err.println("处理主体 " + mainBodyName + " 时出错: " + e.getMessage());
                e.printStackTrace();
            }
        });
        PersonnelReport total = new PersonnelReport();
        if (PersonnelConstant.ReportMainBody.COMPANY.equals(mainBodyType)) {
            total.setCompany("总数");
        } else {
            total.setDepartment("总数");
        }
        handleMonthlyReport(total, employeeList, year);
        reportList.add(total);
        return reportList;
    }

    /**
     * 处理年度报表统计中的每个月份数据
     *
     * @param year         年份
     * @param employeeList 符合条件的员工集合
     * @param report       统计报表
     */
    private void handleMonthlyReport(PersonnelReport report, List<Employee> employeeList, int year) {
        // 初始化容量：12个月 + 一个总共
        HashMap<Integer, Headcount> departmentMonthlyMap = new HashMap<>((int) (DateConstant.MONTHS_PER_YEAR / 0.75) + 1);
        for (int i = 0; i <= DateConstant.MONTHS_PER_YEAR; i++) {
            Headcount monthly = countPersonnelReportHeadcount(employeeList, year, i);
            departmentMonthlyMap.put(i, monthly);
        }
        report.setMonthly(departmentMonthlyMap);
    }

    /**
     * 计算单位人员统计
     *
     * @param employeeList 员工列表
     * @param year         年份
     * @param month        月份 (0为年度)
     * @return 单位人员统计
     */
    private Headcount countPersonnelReportHeadcount(List<Employee> employeeList, int year, int month) {

        // 月份校验
        if (month > CommonConstant.TimeUnit.MAX_MONTH.getValue()) {
            return null;
        }
        Headcount headcount = new Headcount();
        // 年度统计
        if (month == PersonnelConstant.ReportUnit.YEAR.getValue()) {
            // 上年末总数
            int lastYear = (int) employeeList.stream().filter(employee -> DateUtils.toLocalDate(employee.getEntryDate()).getYear() != year).count();
            int increase = (int) employeeList.stream().filter(employee -> DateUtils.toLocalDate(employee.getEntryDate()).getYear() == year).count();
            int decrease = (int) employeeList.stream().filter(employee -> employee.getLeaveDate() != null && DateUtils.toLocalDate(employee.getLeaveDate()).getYear() == year).count();
            int realTime = (int) employeeList.stream().filter(employee -> {
                // 上个月人数 = 入职时间 < 当前月份 && (离职为空 || 离职时间大于当前月份)
                return (employee.getLeaveDate() == null || !DateUtils.toLocalDate(employee.getLeaveDate()).isBefore(LocalDate.of(year + 1, 1, 1)));
            }).count();
            headcount.setLastTime(lastYear);
            headcount.setIncrease(increase);
            headcount.setDecrease(decrease);
            headcount.setRealtime(realTime);
        } else {
            LocalDate currentMonth = LocalDate.of(year, month, 1);
            int lastMonth = (int) employeeList.stream().filter(employee -> {
                // 上个月人数 = 入职时间 < 当前月份 && (离职为空 || 离职时间大于当前月份)
                return DateUtils.toLocalDate(employee.getEntryDate()).isBefore(currentMonth)
                        && (employee.getLeaveDate() == null || !DateUtils.toLocalDate(employee.getLeaveDate()).isBefore(currentMonth));
            }).count();
            int increase = (int) employeeList.stream().filter(employee -> DateUtils.toLocalDate(employee.getEntryDate()).getYear() == year
                    && DateUtils.toLocalDate(employee.getEntryDate()).getMonthValue() == month).count();
            int decrease = (int) employeeList.stream().filter(employee -> employee.getLeaveDate() != null
                    && DateUtils.toLocalDate(employee.getLeaveDate()).getYear() == year
                    && DateUtils.toLocalDate(employee.getLeaveDate()).getMonthValue() == month).count();
            int realTime = (int) employeeList.stream().filter(employee -> {
                // 上个月人数 = 入职时间 < 当前月份 && (离职为空 || 离职时间大于当前月份)
                return DateUtils.toLocalDate(employee.getEntryDate()).isBefore(currentMonth.plusMonths(1))
                        && (employee.getLeaveDate() == null || !DateUtils.toLocalDate(employee.getLeaveDate()).isBefore(currentMonth.plusMonths(1)));
            }).count();
            headcount.setLastTime(lastMonth);
            headcount.setIncrease(increase);
            headcount.setDecrease(decrease);
            headcount.setRealtime(realTime);
        }
        return headcount;
    }

    @Override
    public List<ResignationEmployeeFileVO> queryDepart(Employee employee) {
        List<Employee> employeeList = employeeMapper.selectEmployeeListByDate(employee);
        ArrayList<ResignationEmployeeFileVO> employeeDetailList = new ArrayList<>();

        Set<String> empIdSet = convertSet(employeeList, Employee::getId);
        List<HrStaffPersonInfo> personList = personInfoMapper.selectHrStaffPersonInfoList(null);
        List<HrStaffEkpInfo> ekpList = ekpInfoMapper.selectHrStaffEkpInfoList(null);
        List<HrStaffPersonExpCont> contList = expContMapper.selectHrStaffPersonExpContList(null);
        List<HrStaffPersonExpEduc> educList = expEducMapper.selectHrStaffPersonExpEducList(null);
        Map<String, ResignationProcessInfo> resignationProcessInfoMap;
        List<ResignationProcessInfo> resignationProcessInfoList;
        if (empIdSet.size() > DB_MAX_PARAM_NUM) {
            resignationProcessInfoList = resignationProcessInfoMapper.selectList();
            resignationProcessInfoList = filterList(resignationProcessInfoList, info -> empIdSet.contains(info.getApplicantId()));
        } else {
            resignationProcessInfoList = resignationProcessInfoMapper.selectListByApplicantId(empIdSet);
        }
        resignationProcessInfoMap = convertMap(resignationProcessInfoList, ResignationProcessInfo::getApplicantId);

        // 把所有实体类集合做成Map，key为员工id
        Map<String, HrStaffPersonInfo> personMap = personList.stream().collect(Collectors.toMap(HrStaffPersonInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, HrStaffEkpInfo> ekpMap = ekpList.stream().collect(Collectors.toMap(HrStaffEkpInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, List<HrStaffPersonExpCont>> contMap = contList.stream().collect(Collectors.groupingBy(HrStaffPersonExpCont::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpEduc>> educMap = educList.stream().collect(Collectors.groupingBy(HrStaffPersonExpEduc::getFdPersonInfoId));

        employeeList.forEach(e -> {
            String id = e.getId();
            HrStaffPersonInfo personInfo = new HrStaffPersonInfo();
            HrStaffEkpInfo ekpInfo = new HrStaffEkpInfo();
            HrStaffPersonExpCont contInfo = new HrStaffPersonExpCont();
            HrStaffPersonExpEduc educInfo = new HrStaffPersonExpEduc();
            if (personMap.containsKey(id)) {
                personInfo = personMap.get(id);
            }
            if (ekpMap.containsKey(id)) {
                ekpInfo = ekpMap.get(id);
            }
            if (contMap.containsKey(id)) {
                List<HrStaffPersonExpCont> empContList = contMap.get(id);
                Optional<HrStaffPersonExpCont> lastCont = empContList.stream().filter(cont -> cont.getContractEndDate() != null).min((c1, c2) -> c2.getContractEndDate().compareTo(c1.getContractEndDate()));
                if (lastCont.isPresent()) {
                    contInfo = lastCont.get();
                }
            }
            if (educMap.containsKey(id)) {
                List<HrStaffPersonExpEduc> empEducList = educMap.get(id);
                Optional<HrStaffPersonExpEduc> lastEduc = empEducList.stream().filter(educ -> educ.getGraduationDate() != null).min((e1, e2) -> e2.getGraduationDate().compareTo(e1.getGraduationDate()));
                if (lastEduc.isPresent()) {
                    educInfo = lastEduc.get();
                }
            }
            ResignationEmployeeFileVO result = EmployeeConvert.INSTANCE.dos2departVO(e, personInfo, ekpInfo);
            handleDos2Vo(contInfo, educInfo, result);
            if (resignationProcessInfoMap.containsKey(id)) {
                ResignationProcessInfo info = resignationProcessInfoMap.get(id);
                String resignationSummary = RESIGNATION_SUMMARY_TEMPLATE
                        .replace("{0}", info.getReason() == null ? "无" : info.getReason())
                        .replace("{1}", info.getInterviewDescription() == null ? "无" : info.getInterviewDescription())
                        .replace("{2}", info.getHrInterviewDescription() == null ? "无" : info.getHrInterviewDescription());
                String notRecommendRehireDescription = info.getNotRecommendRehireDescription();
                if (StrUtil.isNotBlank(notRecommendRehireDescription)) {
                    resignationSummary += "；\n不建议录取原因：" + notRecommendRehireDescription;
                }
                result.setResignationSummary(resignationSummary);
            }
            employeeDetailList.add(result);
        });
        return employeeDetailList;
    }

    private static void handleDos2Vo(HrStaffPersonExpCont contInfo, HrStaffPersonExpEduc educInfo,
                                     List<HrStaffPersonExpQual> quals, EmployeeFileVO detailVO) {
        String certificateStr = quals.stream().map(HrStaffPersonExpQual::getFdCertificateName).collect(Collectors.toList()).toString().replace("[", "").replace("]", "");
        if (contInfo != null) {
            detailVO.setContractEndDate(contInfo.getContractEndDate());
        }
        if (educInfo != null) {
            detailVO.setFdMajor(educInfo.getFdMajor());
            detailVO.setFdSchoolName(educInfo.getFdSchoolName());
            detailVO.setGraduationDate(educInfo.getGraduationDate());
        }
        detailVO.setCertificate(certificateStr);
        detailVO.setTitleCertificate(certificateStr);
    }

    private static void handleDos2Vo(HrStaffPersonExpCont contInfo, HrStaffPersonExpEduc educInfo, ResignationEmployeeFileVO detailVO) {
        if (contInfo != null) {
            detailVO.setContractEndDate(contInfo.getContractEndDate());
        }
        if (educInfo != null) {
            detailVO.setFdMajor(educInfo.getFdMajor());
            detailVO.setFdSchoolName(educInfo.getFdSchoolName());
            detailVO.setGraduationDate(educInfo.getGraduationDate());
        }
    }

    @Override
    @SuppressWarnings("DuplicatedCode")
    public List<EmployeeFileVO> listFile(Employee employeeQuery) {

        List<EmployeeFileVO> detailList = new ArrayList<>();

        // 获取所有相关的实体类
        List<Employee> employeeList = list(employeeQuery);
        List<HrStaffPersonInfo> personList = personInfoMapper.selectHrStaffPersonInfoList(null);
        List<HrStaffEkpInfo> ekpList = ekpInfoMapper.selectHrStaffEkpInfoList(null);
        List<HrStaffPersonExpCont> contList = expContMapper.selectHrStaffPersonExpContList(null);
        List<HrStaffPersonExpEduc> educList = expEducMapper.selectHrStaffPersonExpEducList(null);
        List<HrStaffPersonExpQual> qualList = expQualMapper.selectHrStaffPersonExpQualList(null);

        // 把所有实体类集合做成Map，key为员工id
        Map<String, HrStaffPersonInfo> personMap = personList.stream().collect(Collectors.toMap(HrStaffPersonInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, HrStaffEkpInfo> ekpMap = ekpList.stream().collect(Collectors.toMap(HrStaffEkpInfo::getFdId, Function.identity(), (key1, key2) -> key1));
        Map<String, List<HrStaffPersonExpCont>> contMap = contList.stream().collect(Collectors.groupingBy(HrStaffPersonExpCont::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpEduc>> educMap = educList.stream().collect(Collectors.groupingBy(HrStaffPersonExpEduc::getFdPersonInfoId));
        Map<String, List<HrStaffPersonExpQual>> qualMap = qualList.stream().collect(Collectors.groupingBy(HrStaffPersonExpQual::getFdPersonInfoId));

        // 遍历所有员工，获取对应的实体类，为员工详情信息注入值
        for (Employee employee : employeeList) {
            String id = employee.getId();
            HrStaffPersonInfo personInfo = new HrStaffPersonInfo();
            HrStaffEkpInfo ekpInfo = new HrStaffEkpInfo();
            HrStaffPersonExpCont contInfo = new HrStaffPersonExpCont();
            HrStaffPersonExpEduc educInfo = new HrStaffPersonExpEduc();
            List<HrStaffPersonExpQual> quals = new ArrayList<>();
            if (personMap.containsKey(id)) {
                personInfo = personMap.get(id);
            }
            if (ekpMap.containsKey(id)) {
                ekpInfo = ekpMap.get(id);
            }
            if (qualMap.containsKey(id)) {
                quals = qualMap.get(id);
            }
            if (contMap.containsKey(id)) {
                List<HrStaffPersonExpCont> empContList = contMap.get(id);
                Optional<HrStaffPersonExpCont> lastCont = empContList.stream().filter(cont -> cont.getContractEndDate() != null).min((c1, c2) -> c2.getContractEndDate().compareTo(c1.getContractEndDate()));
                if (lastCont.isPresent()) {
                    contInfo = lastCont.get();
                }
            }
            if (educMap.containsKey(id)) {
                List<HrStaffPersonExpEduc> empEducList = educMap.get(id);
                Optional<HrStaffPersonExpEduc> lastEduc = empEducList.stream().filter(educ -> educ.getGraduationDate() != null).min((e1, e2) -> e2.getGraduationDate().compareTo(e1.getGraduationDate()));
                if (lastEduc.isPresent()) {
                    educInfo = lastEduc.get();
                }
            }
            EmployeeFileVO detailVO = EmployeeConvert.INSTANCE.dos2detailVO(employee, personInfo, ekpInfo);
            if (employee.getLastLeaveDate() != null && employee.getLastEntryDate() != null) {
                detailVO.setLastTermOfOffice(employee.getLastEntryDate() + "至" + employee.getLastLeaveDate());
            }
            handleDos2Vo(contInfo, educInfo, quals, detailVO);
            detailList.add(detailVO);
        }

        return detailList;
    }

    @Override
    public TreeData getRoleLine(String id) {
        TreeData roleLine = new TreeData();
        List<Employee> employeeAll = list(new Employee());
        Map<String, Employee> employeeMap = employeeAll.stream().collect(Collectors.toMap(Employee::getId, Function.identity(), (key1, key2) -> key2));
        Employee currentEmp = employeeMap.get(id);
        roleLine.setId(id);
        roleLine.setLabel(currentEmp.getName());
        List<TreeData> subordinateTree = getSubordinateTree(id, employeeAll);
        roleLine.setChildren(subordinateTree);
        roleLine = generateLeader(roleLine, employeeMap, currentEmp);
        return roleLine;
    }

    /**
     * 获取下属角色线树
     *
     * @param id          目标员工 ID
     * @param employeeAll 所有员工
     * @return 下属角色线树
     */
    private static List<TreeData> getSubordinateTree(String id, List<Employee> employeeAll) {
        List<Employee> subordinateEmployees = employeeAll.stream().filter(emp -> id.equals(emp.getLeaderId())).collect(Collectors.toList());
        List<TreeData> subTree = new ArrayList<>();
        for (Employee subEmp : subordinateEmployees) {
            String subordinateId = subEmp.getId();
            TreeData children = new TreeData();
            children.setId(subordinateId);
            children.setLabel(subEmp.getName());
            children.setChildren(getSubordinateTree(subordinateId, employeeAll));
            subTree.add(children);
        }
        return subTree;
    }

    /**
     * 获取领导信息
     *
     * @param roleLine    角色线
     * @param employeeMap 员工 Map
     * @param currentEmp  当前员工
     * @return 领导节点数据
     */
    private TreeData generateLeader(TreeData roleLine, Map<String, Employee> employeeMap, Employee currentEmp) {
        // CEO 返回
        if (currentEmp.getLeaderId() == null) {
            return roleLine;
        }
        Employee leader;
        // 除CEO以外，直属必须在角色线存在
        do {
            List<TreeData> currentEmpTree = Collections.singletonList(roleLine);
            leader = employeeMap.get(currentEmp.getLeaderId());
            roleLine = new TreeData();
            roleLine.setId(leader.getId());
            roleLine.setLabel(leader.getName());
            roleLine.setChildren(currentEmpTree);
            currentEmp = leader;
            // 角色线如果有VP仅到VP，过滤CEO，如果没有VP，则不过滤
        } while (currentEmp.getLeaderId() != null && notVp(currentEmp));
        return roleLine;
    }

    /**
     * 判断不是分管VP
     */
    private boolean notVp(Employee currentEmp) {
        return !VP_SET.contains(currentEmp.getId());
    }

}
