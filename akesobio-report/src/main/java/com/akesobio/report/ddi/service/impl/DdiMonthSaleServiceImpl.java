package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiMonthSaleMapper;
import com.akesobio.report.ddi.domain.DdiMonthSale;
import com.akesobio.report.ddi.service.IDdiMonthSaleService;

/**
 * 月销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class DdiMonthSaleServiceImpl implements IDdiMonthSaleService {
    @Autowired
    private DdiMonthSaleMapper ddiMonthSaleMapper;


    /**
     * 查询月销售列表
     *
     * @param ddiMonthSale 月销售
     * @return 月销售
     */
    @Override
    public List<DdiMonthSale> selectDdiMonthSaleList(DdiMonthSale ddiMonthSale) {
        return ddiMonthSaleMapper.selectDdiMonthSaleList(ddiMonthSale);
    }

    /**
     * 查询所有月销售列表
     *
     * @param ddiMonthSale 月销售
     * @return 月销售
     */
    @Override
    public List<DdiMonthSale> selectAllDdiMonthSaleList(DdiMonthSale ddiMonthSale) {
        return ddiMonthSaleMapper.selectAllDdiMonthSaleList(ddiMonthSale);
    }
}
