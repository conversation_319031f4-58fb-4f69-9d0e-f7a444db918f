package com.akesobio.report.ddi.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiDayProcurementMapper;
import com.akesobio.report.ddi.domain.DdiDayProcurement;
import com.akesobio.report.ddi.service.IDdiDayProcurementService;

/**
 * 日采购Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class DdiDayProcurementServiceImpl implements IDdiDayProcurementService 
{
    @Autowired
    private DdiDayProcurementMapper ddiDayProcurementMapper;



    /**
     * 查询日采购列表
     * 
     * @param ddiDayProcurement 日采购
     * @return 日采购
     */
    @Override
    public List<DdiDayProcurement> selectDdiDayProcurementList(DdiDayProcurement ddiDayProcurement)
    {
        return ddiDayProcurementMapper.selectDdiDayProcurementList(ddiDayProcurement);
    }

}
