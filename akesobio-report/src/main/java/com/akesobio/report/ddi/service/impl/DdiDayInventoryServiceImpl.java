package com.akesobio.report.ddi.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiDayInventoryMapper;
import com.akesobio.report.ddi.domain.DdiDayInventory;
import com.akesobio.report.ddi.service.IDdiDayInventoryService;

/**
 * 日库存Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class DdiDayInventoryServiceImpl implements IDdiDayInventoryService 
{
    @Autowired
    private DdiDayInventoryMapper ddiDayInventoryMapper;


    /**
     * 查询日库存列表
     * 
     * @param ddiDayInventory 日库存
     * @return 日库存
     */
    @Override
    public List<DdiDayInventory> selectDdiDayInventoryList(DdiDayInventory ddiDayInventory)
    {
        return ddiDayInventoryMapper.selectDdiDayInventoryList(ddiDayInventory);
    }

}
