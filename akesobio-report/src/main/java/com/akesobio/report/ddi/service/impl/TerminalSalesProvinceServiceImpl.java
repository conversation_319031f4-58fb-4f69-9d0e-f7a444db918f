package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.TerminalSalesProvinceMapper;
import com.akesobio.report.ddi.domain.TerminalSalesProvince;
import com.akesobio.report.ddi.service.ITerminalSalesProvinceService;

/**
 * 终端销售统计表-省份Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class TerminalSalesProvinceServiceImpl implements ITerminalSalesProvinceService {
    @Autowired
    private TerminalSalesProvinceMapper terminalSalesProvinceMapper;

    /**
     * 查询终端销售统计表-省份
     *
     * @param id 终端销售统计表-省份主键
     * @return 终端销售统计表-省份
     */
    @Override
    public TerminalSalesProvince selectTerminalSalesProvinceById(Integer id) {
        return terminalSalesProvinceMapper.selectTerminalSalesProvinceById(id);
    }

    /**
     * 查询终端销售统计表-省份列表
     *
     * @param terminalSalesProvince 终端销售统计表-省份
     * @return 终端销售统计表-省份
     */
    @Override
    public List<TerminalSalesProvince> selectTerminalSalesProvinceList(TerminalSalesProvince terminalSalesProvince) {
        return terminalSalesProvinceMapper.selectTerminalSalesProvinceList(terminalSalesProvince);
    }

    /**
     * 新增终端销售统计表-省份
     *
     * @param terminalSalesProvince 终端销售统计表-省份
     * @return 结果
     */
    @Override
    public int insertTerminalSalesProvince(TerminalSalesProvince terminalSalesProvince) {
        return terminalSalesProvinceMapper.insertTerminalSalesProvince(terminalSalesProvince);
    }

    /**
     * 修改终端销售统计表-省份
     *
     * @param terminalSalesProvince 终端销售统计表-省份
     * @return 结果
     */
    @Override
    public int updateTerminalSalesProvince(TerminalSalesProvince terminalSalesProvince) {
        return terminalSalesProvinceMapper.updateTerminalSalesProvince(terminalSalesProvince);
    }

    /**
     * 批量删除终端销售统计表-省份
     *
     * @param ids 需要删除的终端销售统计表-省份主键
     * @return 结果
     */
    @Override
    public int deleteTerminalSalesProvinceByIds(Integer[] ids) {
        return terminalSalesProvinceMapper.deleteTerminalSalesProvinceByIds(ids);
    }

    /**
     * 删除终端销售统计表-省份信息
     *
     * @param id 终端销售统计表-省份主键
     * @return 结果
     */
    @Override
    public int deleteTerminalSalesProvinceById(Integer id) {
        return terminalSalesProvinceMapper.deleteTerminalSalesProvinceById(id);
    }
}
