package com.akesobio.report.ddi.quartz;

import com.akesobio.report.ddi.domain.*;
import com.akesobio.report.ddi.mapper.DdiDaySaleMapper;
import com.akesobio.report.ddi.mapper.ProvincePerformanceMapper;
import com.akesobio.report.ddi.mapper.TerminalSalesHospitalMapper;
import com.akesobio.report.ddi.mapper.TerminalSalesProvinceMapper;
import com.akesobio.report.ddi.service.ITerminalSalesDirectorService;
import com.akesobio.report.ddi.service.ITerminalSalesProvinceService;
import com.akesobio.report.ddi.util.GetComputerName;
import com.akesobio.report.ddi.util.MapRanking;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Component("TerminalSalesProvinceTask")
public class TerminalSalesProvinceTask {
    @Autowired
    private DdiDaySaleMapper ddiDaySaleMapper;

    @Autowired
    private ITerminalSalesProvinceService provinceService;

    @Autowired
    private ProvincePerformanceMapper provincePerformanceMapper;

    @Autowired
    private TerminalSalesProvinceMapper terminalSalesProvinceMapper;

    /**
     * 定时DDI终端销售统计表-省份
     */
    public void terminalSalesProvinceTask() {
        String computerName = GetComputerName.getComputerName();
        System.out.println("计算机名称：" + computerName);
        if (computerName.equals("SERVER22")) {
            /** 昨天日期 */
            LocalDate yesterdayNew = LocalDate.now().minusDays(1);
            // 循环从昨天日期1天前到9天前
            for (int i = 9; i >= 0; i--) {
                // 计算日期
                LocalDate date = yesterdayNew.minusDays(i);
                // 转换为 Date 对象
                Date javaDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
                if (javaDate != null && !javaDate.equals(new Date(0))) {
                    TerminalSalesProvince province = new TerminalSalesProvince();
                    province.setSalesDate(javaDate);
                    terminalSalesProvinceMapper.updateProvinceStatus(province);
                }
            }


            int year2024 = 2024;

            /** 开坦尼  依达方日销售计算开始日期 */
            String date = "2024-08-01";

            /** 依达方日销售计算开始日期 */
            /* String date1 = "2024-05-31";*/

            String firstDateYear2024 = "2024-01-01";
            String endDateYear2024 = "2024-12-31";

            String firstDateYear2025 = "2025-01-01";
            String endDateYear2025 = "2025-12-31";

            /** 开坦尼 单价 */
            BigDecimal ktnUnitPrice = new BigDecimal(1860);
            /** 依达方 单价 */
            BigDecimal ydfUnitPrice = new BigDecimal(736);

            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            for (int d = 10; d >= 1; d--) {

                /** 昨天日期 */
                LocalDate yesterday = LocalDate.now().minusDays(d);
                /** 前天日期 */
                LocalDate beforeday = LocalDate.now().minusDays(d + 1);
                /** 昨天日期 当周周一 */
                LocalDate monday = yesterday.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                /** 昨天日期 当周周日 */
                LocalDate sunday = yesterday.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                /** 上周昨天日期 */
                LocalDate lastYesterday = LocalDate.now().minusDays(d + 7);
                /** 昨天日期 上周周一 */
                LocalDate lastMonday = lastYesterday.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                /** 昨天日期 上周周日 */
                LocalDate lastSunday = lastYesterday.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                /** 昨天日期当月的第一天 */
                LocalDate firstDayOfMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth());
                /** 昨天日期当月的最后一天 */
                //LocalDate lastDayOfMonth = yesterday.with(TemporalAdjusters.lastDayOfMonth());
                LocalDate lastDayOfMonth = yesterday;
                /** 昨天日期上个月的第一天 */
                LocalDate firstDayOfLastMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1);
                /** 昨天日期上个月的最后一天 */
                LocalDate lastDayOfLastMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth()).minusDays(1);

                Month currentQuarterStartMonth = GetComputerName.getQuarterStartMonth(yesterday.getMonthValue());
                /** 昨天日期 当前季度开始日期 */
                LocalDate quarterStartDate = yesterday.withMonth(currentQuarterStartMonth.getValue())
                        .with(TemporalAdjusters.firstDayOfMonth());
                /** 昨天日期 当前季度结束日期 */
//            LocalDate quarterEndDate = quarterStartDate.plusMonths(2)
//                    .with(TemporalAdjusters.lastDayOfMonth());
                LocalDate quarterEndDate = yesterday;
                int lastQuarterStartMonthValue = (currentQuarterStartMonth.getValue() - 3 + 12) % 12; // Wrap around to previous year if needed
                Month lastQuarterStartMonth = Month.of(lastQuarterStartMonthValue);
                /** 昨天日期 上季度开始日期 */
                LocalDate lastQuarterStartDate = yesterday.minusMonths(2)
                        .withMonth(lastQuarterStartMonth.getValue())
                        .with(TemporalAdjusters.firstDayOfMonth());
                /** 昨天日期 上季度结束日期 */
                LocalDate lastQuarterEndDate = lastQuarterStartDate.plusMonths(2)
                        .with(TemporalAdjusters.lastDayOfMonth());

                List<String> provinceList = new ArrayList<>();
                provinceList.add("上海市");
                provinceList.add("江苏省");
                provinceList.add("安徽省");
                provinceList.add("浙江省");
                provinceList.add("江西省");
                provinceList.add("福建省");
                provinceList.add("广东省");
                provinceList.add("海南省");
                provinceList.add("贵州省");
                provinceList.add("云南省");
                provinceList.add("湖北省");
                provinceList.add("湖南省");
                provinceList.add("广西壮族自治区");
                provinceList.add("陕西省");
                provinceList.add("宁夏回族自治区");
                provinceList.add("河南省");
                provinceList.add("新疆维吾尔自治区");
                provinceList.add("青海省");
                provinceList.add("重庆市");
                provinceList.add("四川省");
                provinceList.add("甘肃省");
                provinceList.add("北京市");
                provinceList.add("内蒙古自治区");
                provinceList.add("天津市");
                provinceList.add("山西省");
                provinceList.add("河北省");
                provinceList.add("山东省");
                provinceList.add("黑龙江省");
                provinceList.add("吉林省");
                provinceList.add("辽宁省");


                List<String> productNameList = new ArrayList<>();
                productNameList.add("开坦尼");
                productNameList.add("依达方");

                List<String> isTargetTerminalList = new ArrayList<>();
                isTargetTerminalList.add("是");
                isTargetTerminalList.add("否");


                for (int k = 0; k < productNameList.size(); k++) {
                    /** 产品名称 */
                    String productName = productNameList.get(k);
                    /** 产品编码 */
                    String productCode = "";
                    switch (productName) {
                        case "开坦尼":
                            productCode = "4101000001";
                            break;
                        case "依达方":
                            productCode = "4101000010";
                            break;
                    }
                    for (int j = 0; j < isTargetTerminalList.size(); j++) {
                        String isTargetTerminal = isTargetTerminalList.get(j);
                        for (int i = 0; i < provinceList.size(); i++) {
                            try {

                                BigDecimal data = new BigDecimal(10000);

                                /** 销售省份 */
                                String salesProvince = provinceList.get(i);
                                /** 销售区域 */
                                String salesArea = "";
                                /** 销售大区 */
                                String salesRegion = "";
                                switch (salesProvince) {
                                    case "上海市":
                                        salesArea = "东中国区";
                                        salesRegion = "上海大区";
                                        break;
                                    case "江苏省":
                                        salesArea = "东中国区";
                                        salesRegion = "江苏大区";
                                        break;
                                    case "安徽省":
                                        salesArea = "中中国区";
                                        salesRegion = "安徽大区";
                                        break;
                                    case "浙江省":
                                        salesArea = "东中国区";
                                        salesRegion = "浙江1大区";
                                        break;
                                    case "江西省":
                                        salesArea = "南中国一区";
                                        salesRegion = "闽赣大区";
                                        break;
                                    case "福建省":
                                        salesArea = "南中国一区";
                                        salesRegion = "闽赣大区";
                                        break;
                                    case "广东省":
                                        salesArea = "南中国二区";
                                        salesRegion = "广东2大区";
                                        break;
                                    case "海南省":
                                        salesArea = "南中国二区";
                                        salesRegion = "广东3大区";
                                        break;
                                    case "贵州省":
                                        salesArea = "南中国二区";
                                        salesRegion = "云贵大区";
                                        break;
                                    case "云南省":
                                        salesArea = "南中国二区";
                                        salesRegion = "云贵大区";
                                        break;
                                    case "湖北省":
                                        salesArea = "中中国区";
                                        salesRegion = "湖北大区";
                                        break;
                                    case "湖南省":
                                        salesArea = "南中国一区";
                                        salesRegion = "湖南大区";
                                        break;
                                    case "广西壮族自治区":
                                        salesArea = "南中国二区";
                                        salesRegion = "广西大区";
                                        break;
                                    case "河南省":
                                        salesArea = "中中国区";
                                        salesRegion = "河南大区";
                                        break;
                                    case "陕西省":
                                        salesArea = "西中国区";
                                        salesRegion = "西北1大区";
                                        break;
                                    case "宁夏回族自治区":
                                        salesArea = "西中国区";
                                        salesRegion = "西北1大区";
                                        break;
                                    case "新疆维吾尔自治区":
                                        salesArea = "西中国区";
                                        salesRegion = "西北2大区";
                                        break;
                                    case "青海省":
                                        salesArea = "西中国区";
                                        salesRegion = "西北1大区";
                                        break;
                                    case "甘肃省":
                                        salesArea = "西中国区";
                                        salesRegion = "西北1大区";
                                        break;
                                    case "重庆市":
                                        salesArea = "西中国区";
                                        salesRegion = "重庆大区";
                                        break;
                                    case "四川省":
                                        salesArea = "西中国区";
                                        salesRegion = "四川大区";
                                        break;
                                    case "北京市":
                                        salesArea = "北中国区";
                                        salesRegion = "京蒙大区";
                                        break;
                                    case "内蒙古自治区":
                                        salesArea = "北中国区";
                                        salesRegion = "京蒙大区";
                                        break;
                                    case "河北省":
                                        salesArea = "北中国区";
                                        salesRegion = "津晋冀大区";
                                        break;
                                    case "天津市":
                                        salesArea = "北中国区";
                                        salesRegion = "津晋冀大区";
                                        break;
                                    case "山西省":
                                        salesArea = "北中国区";
                                        salesRegion = "津晋冀大区";
                                        break;
                                    case "山东省":
                                        salesArea = "中中国区";
                                        salesRegion = "山东大区";
                                        break;
                                    case "黑龙江省":
                                        salesArea = "北中国区";
                                        salesRegion = "东北大区";
                                        break;
                                    case "吉林省":
                                        salesArea = "北中国区";
                                        salesRegion = "东北大区";
                                        break;
                                    case "辽宁省":
                                        salesArea = "北中国区";
                                        salesRegion = "东北大区";
                                        break;
                                }

                                /** 销售日期 */
                                LocalDate salesDate = yesterday;
                                /** 查询日期昨天 */
                                String queryDate = salesDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                /** 查询日期前天 */
                                String queryBeforeDate = beforeday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                                /** 日销量 */
                                BigDecimal dailySalesVolume = new BigDecimal(0);

                                /** 日销量金额(万元) */
                                BigDecimal dailySalesVolumeAmount = new BigDecimal(0);

                                DdiDaySale daySale = new DdiDaySale();
                                daySale.setCommercialProvince(salesProvince);
                                daySale.setQueryDate(queryDate);
                                daySale.setStandardProductName(productName);
                                daySale.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> daySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(daySale);
                                if (daySaleList.size() > 0) {
                                    dailySalesVolume = daySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    switch (productName) {
                                        case "开坦尼":
                                            dailySalesVolumeAmount = daySaleList.stream()
                                                    .filter(a -> a.getStandardQuantity() != null)
                                                    .map(a -> a.getStandardQuantity().multiply(ktnUnitPrice))
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                        case "依达方":
                                            dailySalesVolumeAmount = daySaleList.stream()
                                                    .filter(a -> a.getStandardQuantity() != null)
                                                    .map(a -> a.getStandardQuantity().multiply(ydfUnitPrice))
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                    }
                                }

                                /** 日排名 */
                                //Integer dailyRanking = dailyRankingMap.get(salesProvince);
                                Integer dailyRanking = null;

                                /** 日环比增长率 */
                                String dailyGrowthRate = "";

                                /** 前天销量 */
                                BigDecimal beforeDaySalesVolume = new BigDecimal(0);
                                DdiDaySale beforeDaySale = new DdiDaySale();
                                beforeDaySale.setQueryDate(queryBeforeDate);
                                beforeDaySale.setCommercialProvince(salesProvince);
                                beforeDaySale.setStandardProductName(productName);
                                beforeDaySale.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> beforeDaySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(beforeDaySale);
                                if (beforeDaySaleList.size() > 0) {
                                    beforeDaySalesVolume = beforeDaySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }

                                /** 日对比 */
                                BigDecimal dailyComparison = new BigDecimal(0);
                                if (beforeDaySalesVolume.compareTo(BigDecimal.ZERO) == 0) {

                                } else {
                                    dailyComparison = (dailySalesVolume.subtract(beforeDaySalesVolume))
                                            .divide(beforeDaySalesVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                                }
                                if (dailyComparison.compareTo(BigDecimal.ZERO) == 0) {
                                    dailyGrowthRate = "0%";
                                } else {
                                    dailyGrowthRate = dailyComparison.toString() + "%";
                                }

                                /** 周销量 */
                                BigDecimal weeklySalesVolume = new BigDecimal(0);
                                DdiDaySale weeklySales = new DdiDaySale();
                                Map<String, Object> weeklySalesMap = weeklySales.getParams();
                                weeklySalesMap.put("beginShipmentDate", monday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                weeklySalesMap.put("endShipmentDate", sunday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                weeklySales.setCommercialProvince(salesProvince);
                                weeklySales.setParams(weeklySalesMap);
                                weeklySales.setStandardProductName(productName);
                                weeklySales.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> weeklySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(weeklySales);
                                if (weeklySalesList.size() > 0) {
                                    weeklySalesVolume = weeklySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }

                                /** 前周销量 */
                                BigDecimal beforeWeeklySalesVolume = new BigDecimal(0);
                                DdiDaySale beforeWeeklySales = new DdiDaySale();
                                Map<String, Object> beforeWeeklySalesMap = beforeWeeklySales.getParams();
                                beforeWeeklySalesMap.put("beginShipmentDate", lastMonday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                beforeWeeklySalesMap.put("endShipmentDate", lastSunday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                beforeWeeklySales.setCommercialProvince(salesProvince);
                                beforeWeeklySales.setParams(beforeWeeklySalesMap);
                                beforeWeeklySales.setStandardProductName(productName);
                                beforeWeeklySales.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> beforeWeeklySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(beforeWeeklySales);
                                if (beforeWeeklySalesList.size() > 0) {
                                    beforeWeeklySalesVolume = beforeWeeklySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }

                                /** 周排名 */
                                //Integer weeklyRanking = weeklyRankingMap.get(salesProvince);
                                Integer weeklyRanking = null;

                                /** 周环比增长率 */
                                String weeklyGrowthRate = "";
                                /** 周对比 */
                                BigDecimal weeklyComparison = new BigDecimal(0);
                                if (beforeWeeklySalesVolume.compareTo(BigDecimal.ZERO) == 0) {

                                } else {
                                    weeklyComparison = (weeklySalesVolume.subtract(beforeWeeklySalesVolume))
                                            .divide(beforeWeeklySalesVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                                }
                                if (weeklyComparison.compareTo(BigDecimal.ZERO) == 0) {
                                    weeklyGrowthRate = "0%";
                                } else {
                                    weeklyGrowthRate = weeklyComparison.toString() + "%";
                                }

                                /** 月销量 */
                                BigDecimal monthlySalesVolume = new BigDecimal(0);

                                /** 月销量金额(万元) */
                                BigDecimal monthlySalesVolumeAmount = new BigDecimal(0);

                                DdiDaySale monthlySales = new DdiDaySale();
                                Map<String, Object> monthlySalesMap = monthlySales.getParams();
                                monthlySalesMap.put("beginShipmentDate", firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                monthlySalesMap.put("endShipmentDate", lastDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                monthlySales.setCommercialProvince(salesProvince);
                                monthlySales.setParams(monthlySalesMap);
                                monthlySales.setStandardProductName(productName);
                                monthlySales.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> monthlySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(monthlySales);
                                if (monthlySalesList.size() > 0) {
                                    monthlySalesVolume = monthlySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    switch (productName) {
                                        case "开坦尼":
                                            monthlySalesVolumeAmount = monthlySalesList.stream()
                                                    .filter(a -> a.getStandardQuantity() != null)
                                                    .map(a -> a.getStandardQuantity().multiply(ktnUnitPrice))
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                        case "依达方":
                                            monthlySalesVolumeAmount = monthlySalesList.stream()
                                                    .filter(a -> a.getStandardQuantity() != null)
                                                    .map(a -> a.getStandardQuantity().multiply(ydfUnitPrice))
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                    }
                                }

                                /** 前月销量 */
                                BigDecimal beforeMonthlySalesVolume = new BigDecimal(0);
                                DdiDaySale beforeMonthlySales = new DdiDaySale();
                                Map<String, Object> beforeMonthlySalesMap = beforeMonthlySales.getParams();
                                beforeMonthlySalesMap.put("beginShipmentDate", firstDayOfLastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                beforeMonthlySalesMap.put("endShipmentDate", lastDayOfLastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                beforeMonthlySales.setCommercialProvince(salesProvince);
                                beforeMonthlySales.setParams(beforeMonthlySalesMap);
                                beforeMonthlySales.setStandardProductName(productName);
                                beforeMonthlySales.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> list = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(beforeMonthlySales);
                                if (list.size() > 0) {
                                    beforeMonthlySalesVolume = list.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }

                                /** 月排名 */
                                //Integer monthlyRanking = monxthlyRankingMap.get(salesProvince);
                                Integer monthlyRanking = null;

                                /** 月环比增长率 */
                                String monthlyGrowthRate = "";

                                /** 月对比 */
                                BigDecimal monthlyComparison = new BigDecimal(0);
                                if (beforeMonthlySalesVolume.compareTo(BigDecimal.ZERO) == 0) {

                                } else {
                                    monthlyComparison = (monthlySalesVolume.subtract(beforeMonthlySalesVolume))
                                            .divide(beforeMonthlySalesVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                                }
                                if (monthlyComparison.compareTo(BigDecimal.ZERO) == 0) {
                                    monthlyGrowthRate = "0%";
                                } else {
                                    monthlyGrowthRate = monthlyComparison.toString() + "%";
                                }

                                /** 季度销量 */
                                BigDecimal quarterlySales = new BigDecimal(0);

                                /** 季度销量金额(万元) */
                                BigDecimal quarterlySalesAmount = new BigDecimal(0);

                                DdiDaySale quarterSale = new DdiDaySale();
                                Map<String, Object> quarterSaleMap = daySale.getParams();
                                quarterSaleMap.put("beginShipmentDate", quarterStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                quarterSaleMap.put("endShipmentDate", quarterEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                quarterSale.setCommercialProvince(salesProvince);
                                quarterSale.setParams(quarterSaleMap);
                                quarterSale.setStandardProductName(productName);
                                quarterSale.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> quarterSaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(quarterSale);
                                if (quarterSaleList.size() > 0) {
                                    quarterlySales = quarterSaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    switch (productName) {
                                        case "开坦尼":
                                            quarterlySalesAmount = quarterSaleList.stream()
                                                    .filter(a -> a.getStandardQuantity() != null)
                                                    .map(a -> a.getStandardQuantity().multiply(ktnUnitPrice))
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                        case "依达方":
                                            quarterlySalesAmount = quarterSaleList.stream()
                                                    .filter(a -> a.getStandardQuantity() != null)
                                                    .map(a -> a.getStandardQuantity().multiply(ydfUnitPrice))
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                    }
                                }

                                /** 上季度销量 */
                                BigDecimal beforeQuarterlySales = new BigDecimal(0);
                                DdiDaySale beforeQuarterlySale = new DdiDaySale();
                                Map<String, Object> beforeQuarterlySaleMap = daySale.getParams();
                                beforeQuarterlySaleMap.put("beginShipmentDate", lastQuarterStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                beforeQuarterlySaleMap.put("endShipmentDate", lastQuarterEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                beforeQuarterlySale.setCommercialProvince(salesProvince);
                                beforeQuarterlySale.setParams(beforeQuarterlySaleMap);
                                beforeQuarterlySale.setStandardProductName(productName);
                                beforeQuarterlySale.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> beforeQuarterlySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(beforeQuarterlySale);
                                if (beforeQuarterlySaleList.size() > 0) {
                                    beforeQuarterlySales = beforeQuarterlySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }

                                /** 季度排名 */
                                //Integer quarterlyRanking = quarterlyRankingMap.get(salesProvince);
                                Integer quarterlyRanking = null;

                                /** 季度环比增长率 */
                                String quarterlyGrowthRate = "";

                                /** 季度对比 */
                                BigDecimal quarterlyComparison = new BigDecimal(0);
                                if (beforeQuarterlySales.compareTo(BigDecimal.ZERO) == 0) {

                                } else {
                                    quarterlyComparison = (quarterlySales.subtract(beforeQuarterlySales))
                                            .divide(beforeQuarterlySales, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                                }
                                if (quarterlyComparison.compareTo(BigDecimal.ZERO) == 0) {
                                    quarterlyGrowthRate = "0%";
                                } else {
                                    quarterlyGrowthRate = quarterlyComparison.toString() + "%";
                                }


                                /** 24年累计销量 */
                                BigDecimal salesVolumeYear24 = new BigDecimal(0);
                                /** 24年累计销量金额(万元) */
                                BigDecimal salesVolumeAmountYear24 = new BigDecimal(0);

                                /** 25年累计销量 */
                                BigDecimal salesVolumeYear25 = new BigDecimal(0);
                                /** 25年累计销量金额(万元) */
                                BigDecimal salesVolumeAmountYear25 = new BigDecimal(0);

                                DdiDaySale afterAprilSale = new DdiDaySale();
                                Map<String, Object> afterAprilSaleMap = daySale.getParams();
                                afterAprilSaleMap.put("beginShipmentDate", firstDateYear2025);
                                afterAprilSaleMap.put("endShipmentDate", endDateYear2025);
                                //afterAprilSaleMap.put("endShipmentDate", yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                afterAprilSale.setCommercialProvince(salesProvince);
                                afterAprilSale.setParams(afterAprilSaleMap);
                                afterAprilSale.setStandardProductName(productName);
                                afterAprilSale.setIsTargetTerminal(isTargetTerminal);
                                List<DdiDaySale> afterAprilSaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList2025(afterAprilSale);
                                if (afterAprilSaleList.size() > 0) {
                                    salesVolumeYear25 = afterAprilSaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                            .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    switch (productName) {
                                        case "开坦尼":
                                            salesVolumeAmountYear25 = salesVolumeYear25.multiply(ktnUnitPrice)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                                            break;
                                        case "依达方":
                                            salesVolumeAmountYear25 = salesVolumeYear25.multiply(ydfUnitPrice)
                                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);

                                    }
                                }

                                /** 24年排名 */
                                //Integer rankingYear24 = yearRankingMap.get(salesProvince);
                                Integer rankingYear24 = null;


                                /** 上市后累计销量 */
                                BigDecimal salesAfterListing = new BigDecimal(0);

                                /** 上市后累计销量金额(万元) */
                                BigDecimal salesAfterListingAmount = new BigDecimal(0);


                                /** 上市后累计销量排名 */
                                //Integer salesRankingAfterListing = afterListingRankingMap.get(salesProvince);
                                Integer salesRankingAfterListing = null;

                                TerminalSalesProvince terminalSalesProvince = new TerminalSalesProvince();

                                terminalSalesProvince.setSalesArea(salesArea);
                                terminalSalesProvince.setSalesRegion(salesRegion);
                                terminalSalesProvince.setSalesProvince(salesProvince);
                                terminalSalesProvince.setSalesDate(Date.from(salesDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                                terminalSalesProvince.setDailySalesVolume(dailySalesVolume);
                                terminalSalesProvince.setDailyRanking(dailyRanking);
                                terminalSalesProvince.setDailyGrowthRate(dailyGrowthRate);
                                terminalSalesProvince.setWeeklySalesVolume(weeklySalesVolume);
                                terminalSalesProvince.setWeeklyRanking(weeklyRanking);
                                terminalSalesProvince.setWeeklyGrowthRate(weeklyGrowthRate);
                                terminalSalesProvince.setMonthlySalesVolume(monthlySalesVolume);
                                terminalSalesProvince.setMonthlyRanking(monthlyRanking);
                                terminalSalesProvince.setMonthlyGrowthRate(monthlyGrowthRate);
                                terminalSalesProvince.setQuarterlySales(quarterlySales);
                                terminalSalesProvince.setQuarterlyRanking(quarterlyRanking);
                                terminalSalesProvince.setQuarterlyGrowthRate(quarterlyGrowthRate);
                                terminalSalesProvince.setSalesVolumeYear24(salesVolumeYear24);
                                terminalSalesProvince.setRankingYear24(rankingYear24);
                                terminalSalesProvince.setSalesAfterListing(salesAfterListing);
                                terminalSalesProvince.setSalesRankingAfterListing(salesRankingAfterListing);
                                terminalSalesProvince.setProductName(productName);
                                terminalSalesProvince.setProductCode(productCode);
                                terminalSalesProvince.setDailySalesVolumeAmount(dailySalesVolumeAmount);
                                terminalSalesProvince.setMonthlySalesVolumeAmount(monthlySalesVolumeAmount);
                                terminalSalesProvince.setQuarterlySalesAmount(quarterlySalesAmount);
                                terminalSalesProvince.setSalesVolumeAmountYear24(salesVolumeAmountYear24);
                                terminalSalesProvince.setSalesAfterListingAmount(salesAfterListingAmount);
                                terminalSalesProvince.setSalesVolumeYear25(salesVolumeYear25);
                                terminalSalesProvince.setSalesVolumeAmountYear25(salesVolumeAmountYear25);
                                terminalSalesProvince.setIsTargetTerminal(isTargetTerminal);

                                int result = provinceService.insertTerminalSalesProvince(terminalSalesProvince);

                                if (result == 1) {
                                    successNum++;
                                    successMsg.append("<br/>" + successNum + "、执行日期" + yesterday + "、产品名称" + productName + " 销售省份 " + salesProvince + " 添加成功");
                                }
                            } catch (Exception e) {
                                failureNum++;
                                String msg = "<br/>" + failureNum + "、执行日期" + yesterday + "、产品名称" + productName + " 销售省份 " + provinceList.get(i) + " 添加失败：";
                                failureMsg.append(msg + e.getMessage());
                            }
                        }
                    }
                }
            }
            log.info("新增成功：" + successNum + "条--" + successMsg.toString());
            log.error("新增失败：--" + failureNum + "条--" + failureMsg.toString());
        } else {
            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--定时DDI终端销售统计表-省份--不在10.10.2.51服务器执行的定时任务--跳过");
        }
    }
}
