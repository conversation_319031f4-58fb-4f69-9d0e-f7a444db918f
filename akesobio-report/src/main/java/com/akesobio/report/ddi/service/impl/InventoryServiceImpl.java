package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.InventoryMapper;
import com.akesobio.report.ddi.domain.Inventory;
import com.akesobio.report.ddi.service.IInventoryService;

/**
 * DDI-库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class InventoryServiceImpl implements IInventoryService {
    @Autowired
    private InventoryMapper inventoryMapper;

    /**
     * 查询DDI-库存
     *
     * @param id DDI-库存主键
     * @return DDI-库存
     */
    @Override
    public Inventory selectInventoryById(Integer id) {
        return inventoryMapper.selectInventoryById(id);
    }

    /**
     * 查询DDI-库存列表
     *
     * @param inventory DDI-库存
     * @return DDI-库存
     */
    @Override
    public List<Inventory> selectInventoryList(Inventory inventory) {
        return inventoryMapper.selectInventoryList(inventory);
    }

    /**
     * 新增DDI-库存
     *
     * @param inventory DDI-库存
     * @return 结果
     */
    @Override
    public int insertInventory(Inventory inventory) {
        return inventoryMapper.insertInventory(inventory);
    }

    /**
     * 修改DDI-库存
     *
     * @param inventory DDI-库存
     * @return 结果
     */
    @Override
    public int updateInventory(Inventory inventory) {
        return inventoryMapper.updateInventory(inventory);
    }

    /**
     * 批量删除DDI-库存
     *
     * @param ids 需要删除的DDI-库存主键
     * @return 结果
     */
    @Override
    public int deleteInventoryByIds(Integer[] ids) {
        return inventoryMapper.deleteInventoryByIds(ids);
    }

    /**
     * 删除DDI-库存信息
     *
     * @param id DDI-库存主键
     * @return 结果
     */
    @Override
    public int deleteInventoryById(Integer id) {
        return inventoryMapper.deleteInventoryById(id);
    }
}
