package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.InventoryReportMapper;
import com.akesobio.report.ddi.domain.InventoryReport;
import com.akesobio.report.ddi.service.IInventoryReportService;

/**
 * 进销存报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class InventoryReportServiceImpl implements IInventoryReportService {
    @Autowired
    private InventoryReportMapper inventoryReportMapper;

    /**
     * 查询进销存报表
     *
     * @param id 进销存报表主键
     * @return 进销存报表
     */
    @Override
    public InventoryReport selectInventoryReportById(Integer id) {
        return inventoryReportMapper.selectInventoryReportById(id);
    }

    /**
     * 查询进销存报表列表
     *
     * @param inventoryReport 进销存报表
     * @return 进销存报表
     */
    @Override
    public List<InventoryReport> selectInventoryReportList(InventoryReport inventoryReport) {
        return inventoryReportMapper.selectInventoryReportList(inventoryReport);
    }

    /**
     * 新增进销存报表
     *
     * @param inventoryReport 进销存报表
     * @return 结果
     */
    @Override
    public int insertInventoryReport(InventoryReport inventoryReport) {
        return inventoryReportMapper.insertInventoryReport(inventoryReport);
    }

    /**
     * 修改进销存报表
     *
     * @param inventoryReport 进销存报表
     * @return 结果
     */
    @Override
    public int updateInventoryReport(InventoryReport inventoryReport) {
        return inventoryReportMapper.updateInventoryReport(inventoryReport);
    }

    /**
     * 批量删除进销存报表
     *
     * @param ids 需要删除的进销存报表主键
     * @return 结果
     */
    @Override
    public int deleteInventoryReportByIds(Integer[] ids) {
        return inventoryReportMapper.deleteInventoryReportByIds(ids);
    }

    /**
     * 删除进销存报表信息
     *
     * @param id 进销存报表主键
     * @return 结果
     */
    @Override
    public int deleteInventoryReportById(Integer id) {
        return inventoryReportMapper.deleteInventoryReportById(id);
    }
}
