package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyDaySaleMapper;
import com.akesobio.report.ddi.domain.DdiPharmacyDaySale;
import com.akesobio.report.ddi.service.IDdiPharmacyDaySaleService;

/**
 * 药房日销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class DdiPharmacyDaySaleServiceImpl implements IDdiPharmacyDaySaleService {
    @Autowired
    private DdiPharmacyDaySaleMapper ddiPharmacyDaySaleMapper;


    /**
     * 查询药房日销售列表
     *
     * @param ddiPharmacyDaySale 药房日销售
     * @return 药房日销售
     */
    @Override
    public List<DdiPharmacyDaySale> selectDdiPharmacyDaySaleList(DdiPharmacyDaySale ddiPharmacyDaySale) {
        return ddiPharmacyDaySaleMapper.selectDdiPharmacyDaySaleList(ddiPharmacyDaySale);
    }
}
