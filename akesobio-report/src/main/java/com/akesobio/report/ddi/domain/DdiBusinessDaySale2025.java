package com.akesobio.report.ddi.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 2025商业日销售对象 ddi_day_sale
 *
 * <AUTHOR>
 * @date 2025-02-09
 */
public class DdiBusinessDaySale2025 extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 执行月
     */
    @Excel(name = "执行月")
    private String executionMonth;

    /**
     * 业务月
     */
    @Excel(name = "业务月")
    private String businessMonth;

    /**
     * 二级部门
     */
    @Excel(name = "二级部门")
    private String secondaryDepartment;

    /**
     * 三级部门
     */
    @Excel(name = "三级部门")
    private String thirdLevelDepartments;

    /**
     * 四级部门
     */
    @Excel(name = "四级部门")
    private String fourthLevelDepartment;

    /**
     * 五级部门
     */
    @Excel(name = "五级部门")
    private String fifthLevelDepartment;

    /**
     * 商业级别
     */
    @Excel(name = "商业级别")
    private String commercialGrade;

    /**
     * 商业省份
     */
    @Excel(name = "商业省份")
    private String commercialProvince;


    /**
     * 商业编码
     */
    @Excel(name = "商业编码")
    private String businessCoding;

    /**
     * 商业名称
     */
    @Excel(name = "商业名称")
    private String businessName;

    /**
     * 出库日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出库日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date shipmentDate;

    /**
     * 客户编码
     */
    @Excel(name = "客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称")
    private String customerName;

    /**
     * 自费药房级别
     */
    @Excel(name = "自费药房级别")
    private String pharmacyLevel;

    /**
     * 标准产品编码
     */
    @Excel(name = "标准产品编码")
    private String standardProductCodes;

    /**
     * 标准产品名称
     */
    @Excel(name = "标准产品名称")
    private String standardProductName;

    /**
     * 标准产品规格
     */
    @Excel(name = "标准产品规格")
    private String standardProductSpecification;

    /**
     * 标准单位
     */
    @Excel(name = "标准单位")
    private String standardUnits;

    /**
     * 标准数量
     */
    @Excel(name = "标准数量")
    private BigDecimal standardQuantity;

    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    @Excel(name = "金额")
    private BigDecimal amount;

    /**
     * 标准批号
     */
    @Excel(name = "标准批号")
    private String standardLotNumber;

    /**
     * 下游供货商编码
     */
    @Excel(name = "下游供货商编码")
    private String downstreamVendorCode;

    /**
     * 下游供货商名称
     */
    @Excel(name = "下游供货商名称")
    private String downstreamVendorName;

    /**
     * 是否目标终端
     */
    @Excel(name = "是否目标终端")
    private String isTargetTerminal;

    /**
     * 一级属性
     */
    @Excel(name = "一级属性")
    private String primaryAttribute;

    /**
     * 下游收货方地址
     */
    @Excel(name = "下游收货方地址")
    private String downstreamShipAddress;

    /**
     * 产品编码
     */
    @Excel(name = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称")
    private String productName;

    /**
     * 产品规格
     */
    @Excel(name = "产品规格")
    private String productSpecifications;

    /**
     * 生产厂家
     */
    @Excel(name = "生产厂家")
    private String manufacturer;

    /**
     * 数量单位
     */
    @Excel(name = "数量单位")
    private String quantityUnits;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private BigDecimal quantity;

    /**
     * 产品单价
     */
    @Excel(name = "产品单价")
    private BigDecimal productUnitPrice;

    /**
     * 产品金额
     */
    @Excel(name = "产品金额")
    private BigDecimal productAmount;

    /**
     * 产品批号
     */
    @Excel(name = "产品批号")
    private String productLotNumber;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderNumber;

    /**
     * 患者(会员)ID
     */
    @Excel(name = "患者(会员)ID")
    private String menberId;

    /**
     * 效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationDate;

    /**
     * 批准文号
     */
    @Excel(name = "批准文号")
    private String approvalNumber;

    /**
     * 适应症
     */
    @Excel(name = "适应症")
    private String indications;

    /**
     * 数据类型
     */
    @Excel(name = "数据类型")
    private String dataType;

    /**
     * 运维状态
     */
    @Excel(name = "运维状态")
    private String maintenanceStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date creationTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updated;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deletionTime;

    /**
     * 删除状态
     */
    //@Excel(name = "删除状态")
    private Integer deleteStatus;

    /**
     * 省份集合
     */
    @TableField(exist = false)
    private List<String> provinceList;

    /**
     * 总监区集合
     */
    @TableField(exist = false)
    private List<String> directorAreaList;

    /**
     * 大区集合
     */
    @TableField(exist = false)
    private List<String> regionList;

    /**
     * 地区集合
     */
    @TableField(exist = false)
    private List<String> areaList;


    public String getPharmacyLevel() {
        return pharmacyLevel;
    }

    public void setPharmacyLevel(String pharmacyLevel) {
        this.pharmacyLevel = pharmacyLevel;
    }

    public String getIsTargetTerminal() {
        return isTargetTerminal;
    }

    public void setIsTargetTerminal(String isTargetTerminal) {
        this.isTargetTerminal = isTargetTerminal;
    }

    public String getPrimaryAttribute() {
        return primaryAttribute;
    }

    public void setPrimaryAttribute(String primaryAttribute) {
        this.primaryAttribute = primaryAttribute;
    }

    public List<String> getDirectorAreaList() {
        return directorAreaList;
    }

    public void setDirectorAreaList(List<String> directorAreaList) {
        this.directorAreaList = directorAreaList;
    }

    public List<String> getRegionList() {
        return regionList;
    }

    public void setRegionList(List<String> regionList) {
        this.regionList = regionList;
    }

    public List<String> getAreaList() {
        return areaList;
    }

    public void setAreaList(List<String> areaList) {
        this.areaList = areaList;
    }

    public String getSecondaryDepartment() {
        return secondaryDepartment;
    }

    public void setSecondaryDepartment(String secondaryDepartment) {
        this.secondaryDepartment = secondaryDepartment;
    }

    public String getThirdLevelDepartments() {
        return thirdLevelDepartments;
    }

    public void setThirdLevelDepartments(String thirdLevelDepartments) {
        this.thirdLevelDepartments = thirdLevelDepartments;
    }

    public String getFourthLevelDepartment() {
        return fourthLevelDepartment;
    }

    public void setFourthLevelDepartment(String fourthLevelDepartment) {
        this.fourthLevelDepartment = fourthLevelDepartment;
    }

    public String getFifthLevelDepartment() {
        return fifthLevelDepartment;
    }

    public void setFifthLevelDepartment(String fifthLevelDepartment) {
        this.fifthLevelDepartment = fifthLevelDepartment;
    }

    public String getCommercialGrade() {
        return commercialGrade;
    }

    public void setCommercialGrade(String commercialGrade) {
        this.commercialGrade = commercialGrade;
    }

    public String getCommercialProvince() {
        return commercialProvince;
    }

    public void setCommercialProvince(String commercialProvince) {
        this.commercialProvince = commercialProvince;
    }

    public List<String> getProvinceList() {
        return provinceList;
    }

    public void setProvinceList(List<String> provinceList) {
        this.provinceList = provinceList;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setExecutionMonth(String executionMonth) {
        this.executionMonth = executionMonth;
    }

    public String getExecutionMonth() {
        return executionMonth;
    }

    public void setBusinessMonth(String businessMonth) {
        this.businessMonth = businessMonth;
    }

    public String getBusinessMonth() {
        return businessMonth;
    }

    public void setBusinessCoding(String businessCoding) {
        this.businessCoding = businessCoding;
    }

    public String getBusinessCoding() {
        return businessCoding;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setShipmentDate(Date shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public Date getShipmentDate() {
        return shipmentDate;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setStandardProductCodes(String standardProductCodes) {
        this.standardProductCodes = standardProductCodes;
    }

    public String getStandardProductCodes() {
        return standardProductCodes;
    }

    public void setStandardProductName(String standardProductName) {
        this.standardProductName = standardProductName;
    }

    public String getStandardProductName() {
        return standardProductName;
    }

    public void setStandardProductSpecification(String standardProductSpecification) {
        this.standardProductSpecification = standardProductSpecification;
    }

    public String getStandardProductSpecification() {
        return standardProductSpecification;
    }

    public void setStandardUnits(String standardUnits) {
        this.standardUnits = standardUnits;
    }

    public String getStandardUnits() {
        return standardUnits;
    }

    public void setStandardQuantity(BigDecimal standardQuantity) {
        this.standardQuantity = standardQuantity;
    }

    public BigDecimal getStandardQuantity() {
        return standardQuantity;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setStandardLotNumber(String standardLotNumber) {
        this.standardLotNumber = standardLotNumber;
    }

    public String getStandardLotNumber() {
        return standardLotNumber;
    }

    public void setDownstreamVendorCode(String downstreamVendorCode) {
        this.downstreamVendorCode = downstreamVendorCode;
    }

    public String getDownstreamVendorCode() {
        return downstreamVendorCode;
    }

    public void setDownstreamVendorName(String downstreamVendorName) {
        this.downstreamVendorName = downstreamVendorName;
    }

    public String getDownstreamVendorName() {
        return downstreamVendorName;
    }

    public void setDownstreamShipAddress(String downstreamShipAddress) {
        this.downstreamShipAddress = downstreamShipAddress;
    }

    public String getDownstreamShipAddress() {
        return downstreamShipAddress;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductSpecifications(String productSpecifications) {
        this.productSpecifications = productSpecifications;
    }

    public String getProductSpecifications() {
        return productSpecifications;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setQuantityUnits(String quantityUnits) {
        this.quantityUnits = quantityUnits;
    }

    public String getQuantityUnits() {
        return quantityUnits;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setProductUnitPrice(BigDecimal productUnitPrice) {
        this.productUnitPrice = productUnitPrice;
    }

    public BigDecimal getProductUnitPrice() {
        return productUnitPrice;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductLotNumber(String productLotNumber) {
        this.productLotNumber = productLotNumber;
    }

    public String getProductLotNumber() {
        return productLotNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setMenberId(String menberId) {
        this.menberId = menberId;
    }

    public String getMenberId() {
        return menberId;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setIndications(String indications) {
        this.indications = indications;
    }

    public String getIndications() {
        return indications;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setMaintenanceStatus(String maintenanceStatus) {
        this.maintenanceStatus = maintenanceStatus;
    }

    public String getMaintenanceStatus() {
        return maintenanceStatus;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("executionMonth", getExecutionMonth())
                .append("businessMonth", getBusinessMonth())
                .append("businessCoding", getBusinessCoding())
                .append("businessName", getBusinessName())
                .append("shipmentDate", getShipmentDate())
                .append("customerCode", getCustomerCode())
                .append("customerName", getCustomerName())
                .append("standardProductCodes", getStandardProductCodes())
                .append("standardProductName", getStandardProductName())
                .append("standardProductSpecification", getStandardProductSpecification())
                .append("standardUnits", getStandardUnits())
                .append("standardQuantity", getStandardQuantity())
                .append("unitPrice", getUnitPrice())
                .append("amount", getAmount())
                .append("standardLotNumber", getStandardLotNumber())
                .append("downstreamVendorCode", getDownstreamVendorCode())
                .append("downstreamVendorName", getDownstreamVendorName())
                .append("downstreamShipAddress", getDownstreamShipAddress())
                .append("productCode", getProductCode())
                .append("productName", getProductName())
                .append("productSpecifications", getProductSpecifications())
                .append("manufacturer", getManufacturer())
                .append("quantityUnits", getQuantityUnits())
                .append("quantity", getQuantity())
                .append("productUnitPrice", getProductUnitPrice())
                .append("productAmount", getProductAmount())
                .append("productLotNumber", getProductLotNumber())
                .append("orderNumber", getOrderNumber())
                .append("menberId", getMenberId())
                .append("expirationDate", getExpirationDate())
                .append("approvalNumber", getApprovalNumber())
                .append("indications", getIndications())
                .append("dataType", getDataType())
                .append("maintenanceStatus", getMaintenanceStatus())
                .append("creationTime", getCreationTime())
                .append("updated", getUpdated())
                .append("deletionTime", getDeletionTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
