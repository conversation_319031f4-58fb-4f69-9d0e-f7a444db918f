package com.akesobio.report.ddi.quartz;

import com.akesobio.report.ddi.domain.*;
import com.akesobio.report.ddi.mapper.DdiDaySaleMapper;
import com.akesobio.report.ddi.mapper.RegionPerformanceMapper;
import com.akesobio.report.ddi.mapper.TerminalSalesHospitalMapper;
import com.akesobio.report.ddi.mapper.TerminalSalesOfficeMapper;
import com.akesobio.report.ddi.service.ITerminalSalesHospitalService;
import com.akesobio.report.ddi.service.ITerminalSalesOfficeService;
import com.akesobio.report.ddi.util.GetComputerName;
import com.akesobio.report.ddi.util.MapRanking;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Component("TerminalSalesOfficeTask")
public class TerminalSalesOfficeTask {

    @Autowired
    private DdiDaySaleMapper ddiDaySaleMapper;

    @Autowired
    private ITerminalSalesOfficeService officeService;

    @Autowired
    private RegionPerformanceMapper regionPerformanceMapper;

    @Autowired
    private TerminalSalesOfficeMapper terminalSalesOfficeMapper;

    /**
     * 定时DDI终端销售统计表-办事处
     */
    public void terminalSalesOfficeTask() {
        String computerName = GetComputerName.getComputerName();
        System.out.println("计算机名称：" + computerName);
        if (computerName.equals("SERVER22")) {
            /** 昨天日期 */
            LocalDate yesterdayNew = LocalDate.now().minusDays(1);
            // 循环从昨天日期1天前到9天前
            for (int i = 9; i >= 0; i--) {
                // 计算日期
                LocalDate date = yesterdayNew.minusDays(i);
                // 转换为 Date 对象
                Date javaDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
                if (javaDate != null && !javaDate.equals(new Date(0))) {
                    TerminalSalesOffice office = new TerminalSalesOffice();
                    office.setSalesDate(javaDate);
                    terminalSalesOfficeMapper.updateOfficeStatus(office);
                }
            }


            int year2024 = 2024;
            String date = "2024-04-01";

            String firstDateYear2024 = "2024-01-01";
            String endDateYear2024 = "2024-12-31";

            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            for (int d = 10; d >= 1; d--) {
                /** 昨天日期 */
                LocalDate yesterday = LocalDate.now().minusDays(d);
                /** 前天日期 */
                LocalDate beforeday = LocalDate.now().minusDays(d + 1);
                /** 昨天日期 当周周一 */
                LocalDate monday = yesterday.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                /** 昨天日期 当周周日 */
                LocalDate sunday = yesterday.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                /** 上周昨天日期 */
                LocalDate lastYesterday = LocalDate.now().minusDays(d + 7);
                /** 昨天日期 上周周一 */
                LocalDate lastMonday = lastYesterday.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                /** 昨天日期 上周周日 */
                LocalDate lastSunday = lastYesterday.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                /** 昨天日期当月的第一天 */
                LocalDate firstDayOfMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth());
                /** 昨天日期当月的最后一天 */
                //LocalDate lastDayOfMonth = yesterday.with(TemporalAdjusters.lastDayOfMonth());
                LocalDate lastDayOfMonth = yesterday;
                /** 昨天日期上个月的第一天 */
                LocalDate firstDayOfLastMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1);
                /** 昨天日期上个月的最后一天 */
                LocalDate lastDayOfLastMonth = yesterday.with(TemporalAdjusters.firstDayOfMonth()).minusDays(1);

                Month currentQuarterStartMonth = GetComputerName.getQuarterStartMonth(yesterday.getMonthValue());
                /** 昨天日期 当前季度开始日期 */
                LocalDate quarterStartDate = yesterday.withMonth(currentQuarterStartMonth.getValue())
                        .with(TemporalAdjusters.firstDayOfMonth());
                /** 昨天日期 当前季度结束日期 */
//            LocalDate quarterEndDate = quarterStartDate.plusMonths(2)
//                    .with(TemporalAdjusters.lastDayOfMonth());
                LocalDate quarterEndDate = yesterday;
                int lastQuarterStartMonthValue = (currentQuarterStartMonth.getValue() - 3 + 12) % 12; // Wrap around to previous year if needed
                Month lastQuarterStartMonth = Month.of(lastQuarterStartMonthValue);
                /** 昨天日期 上季度开始日期 */
                LocalDate lastQuarterStartDate = yesterday.minusMonths(2)
                        .withMonth(lastQuarterStartMonth.getValue())
                        .with(TemporalAdjusters.firstDayOfMonth());
                /** 昨天日期 上季度结束日期 */
                LocalDate lastQuarterEndDate = lastQuarterStartDate.plusMonths(2)
                        .with(TemporalAdjusters.lastDayOfMonth());

                List<String> addressList = new ArrayList<>();
                addressList.add("常州区");
                addressList.add("淮宿盐区");
                addressList.add("南京1区");
                addressList.add("南京2区");
                addressList.add("南京3区");
                addressList.add("南通区");
                addressList.add("苏州区");
                addressList.add("无锡区");
                addressList.add("徐连区");
                addressList.add("扬镇泰区");
                addressList.add("上海1区");
                addressList.add("上海2区");
                addressList.add("上海3区");
                addressList.add("上海5区");
                addressList.add("上海6区");
                addressList.add("上海7区");
                addressList.add("杭州1区");
                addressList.add("杭州2区");
                addressList.add("杭州3区");
                addressList.add("杭州5区");
                addressList.add("金衢区");
                addressList.add("宁波区");
                addressList.add("绍嘉湖区");
                addressList.add("台州区");
                addressList.add("温丽区");
                addressList.add("福州1区");
                addressList.add("福州2区");
                addressList.add("赣东区");
                addressList.add("赣南区");
                addressList.add("闽西南区");
                addressList.add("南昌1区");
                addressList.add("南昌2区");
                addressList.add("厦门区");
                addressList.add("安庆区");
                addressList.add("蚌埠区");
                addressList.add("合肥1区");
                addressList.add("合肥2区");
                addressList.add("皖北区");
                addressList.add("芜湖区");
                addressList.add("鄂西北区");
                addressList.add("鄂西南区");
                addressList.add("武汉1区");
                addressList.add("武汉2区");
                addressList.add("武汉3区");
                addressList.add("武汉5区");
                addressList.add("湘南区");
                addressList.add("湘西区");
                addressList.add("长沙1区");
                addressList.add("长沙2区");
                addressList.add("长沙3区");
                addressList.add("桂北区");
                addressList.add("桂东区");
                addressList.add("南宁1区");
                addressList.add("南宁2区");
                addressList.add("贵州区");
                addressList.add("昆明1区");
                addressList.add("昆明2区");
                addressList.add("广州1区");
                addressList.add("广州2区");
                addressList.add("广州3区");
                addressList.add("广州5区");
                addressList.add("广州6区");
                addressList.add("广州7区");
                addressList.add("海南粤西区");
                addressList.add("江中珠区");
                addressList.add("深圳区");
                addressList.add("粤东区");
                addressList.add("豫北区");
                addressList.add("豫东区");
                addressList.add("豫南区");
                addressList.add("郑州1区");
                addressList.add("郑州2区");
                addressList.add("郑州3区");
                addressList.add("郑州5区");
                addressList.add("甘肃1区");
                addressList.add("甘肃2区");
                addressList.add("青宁区");
                addressList.add("陕西1区");
                addressList.add("陕西2区");
                addressList.add("陕西3区");
                addressList.add("新疆1区");
                addressList.add("新疆2区");
                addressList.add("川北区");
                addressList.add("川南区");
                addressList.add("四川1区");
                addressList.add("四川2区");
                addressList.add("四川3区");
                addressList.add("重庆1区");
                addressList.add("重庆2区");
                addressList.add("重庆3区");
                addressList.add("北京1区");
                addressList.add("北京2区");
                addressList.add("北京3区");
                addressList.add("北京5区");
                addressList.add("北京6区");
                addressList.add("北京7区");
                addressList.add("蒙1区");
                addressList.add("蒙2区");
                addressList.add("冀北区");
                addressList.add("冀南区");
                addressList.add("冀中1区");
                addressList.add("冀中2区");
                addressList.add("晋北区");
                addressList.add("晋南区");
                addressList.add("天津1区");
                addressList.add("天津2区");
                addressList.add("济南1区");
                addressList.add("济南2区");
                addressList.add("济南3区");
                addressList.add("临沂区");
                addressList.add("鲁北区");
                addressList.add("鲁中区");
                addressList.add("青岛区");
                addressList.add("烟威区");
                addressList.add("大连1区");
                addressList.add("沈阳1区");
                addressList.add("沈阳2区");
                addressList.add("沈阳3区");
                addressList.add("黑龙江1区");
                addressList.add("黑龙江2区");
                addressList.add("长春1区");
                addressList.add("长春2区");


                Map<String, BigDecimal> dailyMap = new HashMap<>();
                Map<String, BigDecimal> weeklyMap = new HashMap<>();
                Map<String, BigDecimal> monxthlyMap = new HashMap<>();
                Map<String, BigDecimal> quarterMap = new HashMap<>();
                Map<String, BigDecimal> yearMap = new HashMap<>();

                for (int i = 0; i < addressList.size(); i++) {
                    /** 销售地区 */
                    String salesAddress = addressList.get(i);
                    /** 查询日期昨天 */
                    String queryDate = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    /** 日销量 */
                    BigDecimal dailySalesVolume = new BigDecimal(0);
                    DdiDaySale daySale = new DdiDaySale();
                    daySale.setFifthLevelDepartment(salesAddress);
                    daySale.setQueryDate(queryDate);
                    List<DdiDaySale> daySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(daySale);
                    if (daySaleList.size() > 0) {
                        dailySalesVolume = daySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    dailyMap.put(salesAddress, dailySalesVolume);

                    /** 周销量 */
                    BigDecimal weeklySalesVolume = new BigDecimal(0);
                    DdiDaySale weeklySales = new DdiDaySale();
                    Map<String, Object> weeklySalesMap = weeklySales.getParams();
                    weeklySalesMap.put("beginShipmentDate", monday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    weeklySalesMap.put("endShipmentDate", sunday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    weeklySales.setFifthLevelDepartment(salesAddress);
                    weeklySales.setParams(weeklySalesMap);
                    List<DdiDaySale> weeklySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList(weeklySales);
                    if (weeklySalesList.size() > 0) {
                        weeklySalesVolume = weeklySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    weeklyMap.put(salesAddress, weeklySalesVolume);

                    /** 月销量 */
                    BigDecimal monthlySalesVolume = new BigDecimal(0);
                    DdiDaySale monthlySales = new DdiDaySale();
                    Map<String, Object> monthlySalesMap = monthlySales.getParams();
                    monthlySalesMap.put("beginShipmentDate", firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    monthlySalesMap.put("endShipmentDate", lastDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    monthlySales.setFifthLevelDepartment(salesAddress);
                    monthlySales.setParams(monthlySalesMap);
                    List<DdiDaySale> monthlySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList(monthlySales);
                    if (monthlySalesList.size() > 0) {
                        monthlySalesVolume = monthlySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    monxthlyMap.put(salesAddress, monthlySalesVolume);

                    /** 季度销量 */
                    BigDecimal quarterSalesVolume = new BigDecimal(0);
                    DdiDaySale quarterSale = new DdiDaySale();
                    Map<String, Object> quarterSaleMap = daySale.getParams();
                    quarterSaleMap.put("beginShipmentDate", quarterStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    quarterSaleMap.put("endShipmentDate", quarterEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    quarterSale.setFifthLevelDepartment(salesAddress);
                    quarterSale.setParams(quarterSaleMap);
                    List<DdiDaySale> quarterSaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(quarterSale);
                    if (quarterSaleList.size() > 0) {
                        quarterSalesVolume = quarterSaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    quarterMap.put(salesAddress, quarterSalesVolume);

                    /** 24年1-3月销量 */
                    BigDecimal januaryToMarchSalesVolume = new BigDecimal(0);
                    /** 24年4月后销量 */
                    BigDecimal afterAprilSalesVolume = new BigDecimal(0);
                    /** 24年累计销量 */
                    BigDecimal salesVolumeYear24 = new BigDecimal(0);
                    RegionPerformance r = new RegionPerformance();
                    r.setYear(year2024);
                    r.setSalesAddress(salesAddress);
                    List<RegionPerformance> regionPerformanceList = regionPerformanceMapper.selectRegionPerformanceList(r);
                    if (regionPerformanceList.size() > 0) {
                        /** 24年1-3月销量 医院购进 */
                        BigDecimal hospitalPurchases = regionPerformanceList.stream().filter(a -> a.getHospitalPurchases() != null)
                                .map(RegionPerformance::getHospitalPurchases).reduce(BigDecimal.ZERO, BigDecimal::add);
                        /** 24年1-3月销量 DTP销售 */
                        BigDecimal dtpSales = regionPerformanceList.stream().filter(a -> a.getDtpSales() != null)
                                .map(RegionPerformance::getDtpSales).reduce(BigDecimal.ZERO, BigDecimal::add);
                        januaryToMarchSalesVolume = hospitalPurchases.add(dtpSales);
                    }
                    DdiDaySale afterAprilSale = new DdiDaySale();
                    Map<String, Object> afterAprilSaleMap = daySale.getParams();
                    afterAprilSaleMap.put("beginShipmentDate", date);
                    afterAprilSaleMap.put("endShipmentDate", endDateYear2024);
                    //afterAprilSaleMap.put("endShipmentDate", yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    afterAprilSale.setFifthLevelDepartment(salesAddress);
                    afterAprilSale.setParams(afterAprilSaleMap);
                    List<DdiDaySale> afterAprilSaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(afterAprilSale);
                    if (afterAprilSaleList.size() > 0) {
                        afterAprilSalesVolume = afterAprilSaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    salesVolumeYear24 = januaryToMarchSalesVolume.add(afterAprilSalesVolume);
                    yearMap.put(salesAddress, salesVolumeYear24);
                }

                Map<String, Integer> dailyRankingMap = MapRanking.getRankings(dailyMap);
                Map<String, Integer> weeklyRankingMap = MapRanking.getRankings(weeklyMap);
                Map<String, Integer> monxthlyRankingMap = MapRanking.getRankings(monxthlyMap);
                Map<String, Integer> quarterlyRankingMap = MapRanking.getRankings(quarterMap);
                Map<String, Integer> yearRankingMap = MapRanking.getRankings(yearMap);


                for (int i = 0; i < addressList.size(); i++) {
                    try {
                        TerminalSalesOffice terminalSalesOffice = new TerminalSalesOffice();
                        /** 销售地区 */
                        String salesAddress = addressList.get(i);
                        /** 销售区域 */
                        String salesArea = "";
                        /** 销售大区 */
                        String salesRegion = "";
                        switch (salesAddress) {
                            case "常州区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "淮宿盐区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "南京1区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "南京2区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "南京3区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "南通区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "苏州区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "无锡区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "徐连区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "扬镇泰区":
                                salesArea = "东中国区";
                                salesRegion = "江苏大区";
                                break;
                            case "上海1区":
                                salesArea = "东中国区";
                                salesRegion = "上海大区";
                                break;
                            case "上海2区":
                                salesArea = "东中国区";
                                salesRegion = "上海大区";
                                break;
                            case "上海3区":
                                salesArea = "东中国区";
                                salesRegion = "上海大区";
                                break;
                            case "上海5区":
                                salesArea = "东中国区";
                                salesRegion = "上海大区";
                                break;
                            case "上海6区":
                                salesArea = "东中国区";
                                salesRegion = "上海大区";
                                break;
                            case "上海7区":
                                salesArea = "东中国区";
                                salesRegion = "上海大区";
                                break;
                            case "杭州1区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "杭州2区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "杭州3区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "杭州5区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "金衢区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "宁波区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "绍嘉湖区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "台州区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "温丽区":
                                salesArea = "东中国区";
                                salesRegion = "浙江大区";
                                break;
                            case "福州1区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "福州2区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "赣东区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "赣南区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "闽西南区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "南昌1区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "南昌2区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "厦门区":
                                salesArea = "东中国区";
                                salesRegion = "闽赣大区";
                                break;
                            case "安庆区":
                                salesArea = "东中国区";
                                salesRegion = "安徽大区";
                                break;
                            case "蚌埠区":
                                salesArea = "东中国区";
                                salesRegion = "安徽大区";
                                break;
                            case "合肥1区":
                                salesArea = "东中国区";
                                salesRegion = "安徽大区";
                                break;
                            case "合肥2区":
                                salesArea = "东中国区";
                                salesRegion = "安徽大区";
                                break;
                            case "皖北区":
                                salesArea = "东中国区";
                                salesRegion = "安徽大区";
                                break;
                            case "芜湖区":
                                salesArea = "东中国区";
                                salesRegion = "安徽大区";
                                break;
                            case "鄂西北区":
                                salesArea = "南中国区";
                                salesRegion = "湖北大区";
                                break;
                            case "鄂西南区":
                                salesArea = "南中国区";
                                salesRegion = "湖北大区";
                                break;
                            case "武汉1区":
                                salesArea = "南中国区";
                                salesRegion = "湖北大区";
                                break;
                            case "武汉2区":
                                salesArea = "南中国区";
                                salesRegion = "湖北大区";
                                break;
                            case "武汉3区":
                                salesArea = "南中国区";
                                salesRegion = "湖北大区";
                                break;
                            case "武汉5区":
                                salesArea = "南中国区";
                                salesRegion = "湖北大区";
                                break;
                            case "湘南区":
                                salesArea = "南中国区";
                                salesRegion = "湖南大区";
                                break;
                            case "湘西区":
                                salesArea = "南中国区";
                                salesRegion = "湖南大区";
                                break;
                            case "长沙1区":
                                salesArea = "南中国区";
                                salesRegion = "湖南大区";
                                break;
                            case "长沙2区":
                                salesArea = "南中国区";
                                salesRegion = "湖南大区";
                                break;
                            case "长沙3区":
                                salesArea = "南中国区";
                                salesRegion = "湖南大区";
                                break;
                            case "桂北区":
                                salesArea = "南中国区";
                                salesRegion = "广西大区";
                                break;
                            case "桂东区":
                                salesArea = "南中国区";
                                salesRegion = "广西大区";
                                break;
                            case "南宁1区":
                                salesArea = "南中国区";
                                salesRegion = "广西大区";
                                break;
                            case "南宁2区":
                                salesArea = "南中国区";
                                salesRegion = "广西大区";
                                break;
                            case "贵州区":
                                salesArea = "南中国区";
                                salesRegion = "云贵大区";
                                break;
                            case "昆明1区":
                                salesArea = "南中国区";
                                salesRegion = "云贵大区";
                                break;
                            case "昆明2区":
                                salesArea = "南中国区";
                                salesRegion = "云贵大区";
                                break;
                            case "广州1区":
                                salesArea = "南中国区";
                                salesRegion = "广东1区";
                                break;
                            case "广州2区":
                                salesArea = "南中国区";
                                salesRegion = "广东1区";
                                break;
                            case "广州3区":
                                salesArea = "南中国区";
                                salesRegion = "广东1区";
                                break;
                            case "广州5区":
                                salesArea = "南中国区";
                                salesRegion = "广东1区";
                                break;
                            case "广州6区":
                                salesArea = "南中国区";
                                salesRegion = "广东1区";
                                break;
                            case "广州7区":
                                salesArea = "南中国区";
                                salesRegion = "广东1区";
                                break;
                            case "海南粤西区":
                                salesArea = "南中国区";
                                salesRegion = "广东2区";
                                break;
                            case "江中珠区":
                                salesArea = "南中国区";
                                salesRegion = "广东2区";
                                break;
                            case "深圳区":
                                salesArea = "南中国区";
                                salesRegion = "广东2区";
                                break;
                            case "粤东区":
                                salesArea = "南中国区";
                                salesRegion = "广东2区";
                                break;
                            case "豫北区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "豫东区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "豫南区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "郑州1区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "郑州2区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "郑州3区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "郑州5区":
                                salesArea = "西中国区";
                                salesRegion = "河南大区";
                                break;
                            case "甘肃1区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "甘肃2区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "青宁区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "陕西1区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "陕西2区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "陕西3区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "新疆1区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "新疆2区":
                                salesArea = "西中国区";
                                salesRegion = "西北大区";
                                break;
                            case "川北区":
                                salesArea = "西中国区";
                                salesRegion = "四川大区";
                                break;
                            case "川南区":
                                salesArea = "西中国区";
                                salesRegion = "四川大区";
                                break;
                            case "四川1区":
                                salesArea = "西中国区";
                                salesRegion = "四川大区";
                                break;
                            case "四川2区":
                                salesArea = "西中国区";
                                salesRegion = "四川大区";
                                break;
                            case "四川3区":
                                salesArea = "西中国区";
                                salesRegion = "四川大区";
                                break;
                            case "重庆1区":
                                salesArea = "西中国区";
                                salesRegion = "重庆大区";
                                break;
                            case "重庆2区":
                                salesArea = "西中国区";
                                salesRegion = "重庆大区";
                                break;
                            case "重庆3区":
                                salesArea = "西中国区";
                                salesRegion = "重庆大区";
                                break;
                            case "北京1区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "北京2区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "北京3区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "北京5区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "北京6区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "北京7区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "蒙1区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "蒙2区":
                                salesArea = "北中国一区";
                                salesRegion = "京蒙大区";
                                break;
                            case "冀北区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "冀南区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "冀中1区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "冀中2区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "晋北区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "晋南区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "天津1区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "天津2区":
                                salesArea = "北中国一区";
                                salesRegion = "津晋冀大区";
                                break;
                            case "济南1区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "济南2区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "济南3区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "临沂区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "鲁北区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "鲁中区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "青岛区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "烟威区":
                                salesArea = "北中国二区";
                                salesRegion = "山东大区";
                                break;
                            case "大连1区":
                                salesArea = "北中国二区";
                                salesRegion = "辽宁大区";
                                break;
                            case "沈阳1区":
                                salesArea = "北中国二区";
                                salesRegion = "辽宁大区";
                                break;
                            case "沈阳2区":
                                salesArea = "北中国二区";
                                salesRegion = "辽宁大区";
                                break;
                            case "沈阳3区":
                                salesArea = "北中国二区";
                                salesRegion = "辽宁大区";
                                break;
                            case "黑龙江1区":
                                salesArea = "北中国二区";
                                salesRegion = "黑吉大区";
                                break;
                            case "黑龙江2区":
                                salesArea = "北中国二区";
                                salesRegion = "黑吉大区";
                                break;
                            case "长春1区":
                                salesArea = "北中国二区";
                                salesRegion = "黑吉大区";
                                break;
                            case "长春2区":
                                salesArea = "北中国二区";
                                salesRegion = "黑吉大区";
                                break;
                        }

                        /** 销售日期 */
                        LocalDate salesDate = yesterday;
                        /** 查询日期昨天 */
                        String queryDate = salesDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        /** 查询日期前天 */
                        String queryBeforeDate = beforeday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                        /** 日销量 */
                        BigDecimal dailySalesVolume = new BigDecimal(0);
                        DdiDaySale daySale = new DdiDaySale();
                        daySale.setFifthLevelDepartment(salesAddress);
                        daySale.setQueryDate(queryDate);
                        List<DdiDaySale> daySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(daySale);
                        if (daySaleList.size() > 0) {
                            dailySalesVolume = daySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 日排名 */
                        Integer dailyRanking = dailyRankingMap.get(salesAddress);

                        /** 日环比增长率 */
                        String dailyGrowthRate = "";

                        /** 前天销量 */
                        BigDecimal beforeDaySalesVolume = new BigDecimal(0);
                        DdiDaySale beforeDaySale = new DdiDaySale();
                        beforeDaySale.setQueryDate(queryBeforeDate);
                        beforeDaySale.setFifthLevelDepartment(salesAddress);
                        List<DdiDaySale> beforeDaySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(beforeDaySale);
                        if (beforeDaySaleList.size() > 0) {
                            beforeDaySalesVolume = beforeDaySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 日对比 */
                        BigDecimal dailyComparison = new BigDecimal(0);
                        if (beforeDaySalesVolume.compareTo(BigDecimal.ZERO) == 0) {

                        } else {
                            dailyComparison = (dailySalesVolume.subtract(beforeDaySalesVolume))
                                    .divide(beforeDaySalesVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        }
                        if (dailyComparison.compareTo(BigDecimal.ZERO) == 0) {
                            dailyGrowthRate = "0%";
                        } else {
                            dailyGrowthRate = dailyComparison.toString() + "%";
                        }

                        /** 周销量 */
                        BigDecimal weeklySalesVolume = new BigDecimal(0);
                        DdiDaySale weeklySales = new DdiDaySale();
                        Map<String, Object> weeklySalesMap = weeklySales.getParams();
                        weeklySalesMap.put("beginShipmentDate", monday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        weeklySalesMap.put("endShipmentDate", sunday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        weeklySales.setFifthLevelDepartment(salesAddress);
                        weeklySales.setParams(weeklySalesMap);
                        List<DdiDaySale> weeklySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList(weeklySales);
                        if (weeklySalesList.size() > 0) {
                            weeklySalesVolume = weeklySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 前周销量 */
                        BigDecimal beforeWeeklySalesVolume = new BigDecimal(0);
                        DdiDaySale beforeWeeklySales = new DdiDaySale();
                        Map<String, Object> beforeWeeklySalesMap = beforeWeeklySales.getParams();
                        beforeWeeklySalesMap.put("beginShipmentDate", lastMonday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        beforeWeeklySalesMap.put("endShipmentDate", lastSunday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        beforeWeeklySales.setFifthLevelDepartment(salesAddress);
                        beforeWeeklySales.setParams(beforeWeeklySalesMap);
                        List<DdiDaySale> beforeWeeklySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList(beforeWeeklySales);
                        if (beforeWeeklySalesList.size() > 0) {
                            beforeWeeklySalesVolume = beforeWeeklySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 周排名 */
                        Integer weeklyRanking = weeklyRankingMap.get(salesAddress);

                        /** 周环比增长率 */
                        String weeklyGrowthRate = "";
                        /** 周对比 */
                        BigDecimal weeklyComparison = new BigDecimal(0);
                        if (beforeWeeklySalesVolume.compareTo(BigDecimal.ZERO) == 0) {

                        } else {
                            weeklyComparison = (weeklySalesVolume.subtract(beforeWeeklySalesVolume))
                                    .divide(beforeWeeklySalesVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        }
                        if (weeklyComparison.compareTo(BigDecimal.ZERO) == 0) {
                            weeklyGrowthRate = "0%";
                        } else {
                            weeklyGrowthRate = weeklyComparison.toString() + "%";
                        }

                        /** 月销量 */
                        BigDecimal monthlySalesVolume = new BigDecimal(0);
                        DdiDaySale monthlySales = new DdiDaySale();
                        Map<String, Object> monthlySalesMap = monthlySales.getParams();
                        monthlySalesMap.put("beginShipmentDate", firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        monthlySalesMap.put("endShipmentDate", lastDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        monthlySales.setFifthLevelDepartment(salesAddress);
                        monthlySales.setParams(monthlySalesMap);
                        List<DdiDaySale> monthlySalesList = ddiDaySaleMapper.selectAreaDdiDaySaleList(monthlySales);
                        if (monthlySalesList.size() > 0) {
                            monthlySalesVolume = monthlySalesList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 前月销量 */
                        BigDecimal beforeMonthlySalesVolume = new BigDecimal(0);
                        DdiDaySale beforeMonthlySales = new DdiDaySale();
                        Map<String, Object> beforeMonthlySalesMap = beforeMonthlySales.getParams();
                        beforeMonthlySalesMap.put("beginShipmentDate", firstDayOfLastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        beforeMonthlySalesMap.put("endShipmentDate", lastDayOfLastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        beforeMonthlySales.setFifthLevelDepartment(salesAddress);
                        beforeMonthlySales.setParams(beforeMonthlySalesMap);
                        List<DdiDaySale> list = ddiDaySaleMapper.selectAreaDdiDaySaleList(beforeMonthlySales);
                        if (list.size() > 0) {
                            beforeMonthlySalesVolume = list.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 月排名 */
                        Integer monthlyRanking = monxthlyRankingMap.get(salesAddress);

                        /** 月环比增长率 */
                        String monthlyGrowthRate = "";

                        /** 月对比 */
                        BigDecimal monthlyComparison = new BigDecimal(0);
                        if (beforeMonthlySalesVolume.compareTo(BigDecimal.ZERO) == 0) {

                        } else {
                            monthlyComparison = (monthlySalesVolume.subtract(beforeMonthlySalesVolume))
                                    .divide(beforeMonthlySalesVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        }
                        if (monthlyComparison.compareTo(BigDecimal.ZERO) == 0) {
                            monthlyGrowthRate = "0%";
                        } else {
                            monthlyGrowthRate = monthlyComparison.toString() + "%";
                        }

                        /** 季度销量 */
                        BigDecimal quarterlySales = new BigDecimal(0);
                        DdiDaySale quarterSale = new DdiDaySale();
                        Map<String, Object> quarterSaleMap = daySale.getParams();
                        quarterSaleMap.put("beginShipmentDate", quarterStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        quarterSaleMap.put("endShipmentDate", quarterEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        quarterSale.setFifthLevelDepartment(salesAddress);
                        quarterSale.setParams(quarterSaleMap);
                        List<DdiDaySale> quarterSaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(quarterSale);
                        if (quarterSaleList.size() > 0) {
                            quarterlySales = quarterSaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 上季度销量 */
                        BigDecimal beforeQuarterlySales = new BigDecimal(0);
                        DdiDaySale beforeQuarterlySale = new DdiDaySale();
                        Map<String, Object> beforeQuarterlySaleMap = daySale.getParams();
                        beforeQuarterlySaleMap.put("beginShipmentDate", lastQuarterStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        beforeQuarterlySaleMap.put("endShipmentDate", lastQuarterEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        beforeQuarterlySale.setFifthLevelDepartment(salesAddress);
                        beforeQuarterlySale.setParams(beforeQuarterlySaleMap);
                        List<DdiDaySale> beforeQuarterlySaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(beforeQuarterlySale);
                        if (beforeQuarterlySaleList.size() > 0) {
                            beforeQuarterlySales = beforeQuarterlySaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }

                        /** 季度排名 */
                        Integer quarterlyRanking = quarterlyRankingMap.get(salesAddress);

                        /** 季度环比增长率 */
                        String quarterlyGrowthRate = "";

                        /** 季度对比 */
                        BigDecimal quarterlyComparison = new BigDecimal(0);
                        if (beforeQuarterlySales.compareTo(BigDecimal.ZERO) == 0) {

                        } else {
                            quarterlyComparison = (quarterlySales.subtract(beforeQuarterlySales))
                                    .divide(beforeQuarterlySales, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        }
                        if (quarterlyComparison.compareTo(BigDecimal.ZERO) == 0) {
                            quarterlyGrowthRate = "0%";
                        } else {
                            quarterlyGrowthRate = quarterlyComparison.toString() + "%";
                        }

                        /** 24年1-3月销量 */
                        BigDecimal januaryToMarchSalesVolume = new BigDecimal(0);
                        /** 24年4月后销量 */
                        BigDecimal afterAprilSalesVolume = new BigDecimal(0);
                        /** 24年累计销量 */
                        BigDecimal salesVolumeYear24 = new BigDecimal(0);
                        RegionPerformance r = new RegionPerformance();
                        r.setYear(year2024);
                        r.setSalesAddress(salesAddress);
                        List<RegionPerformance> regionPerformanceList = regionPerformanceMapper.selectRegionPerformanceList(r);
                        if (regionPerformanceList.size() > 0) {
                            /** 24年1-3月销量 医院购进 */
                            BigDecimal hospitalPurchases = regionPerformanceList.stream().filter(a -> a.getHospitalPurchases() != null)
                                    .map(RegionPerformance::getHospitalPurchases).reduce(BigDecimal.ZERO, BigDecimal::add);
                            /** 24年1-3月销量 DTP销售 */
                            BigDecimal dtpSales = regionPerformanceList.stream().filter(a -> a.getDtpSales() != null)
                                    .map(RegionPerformance::getDtpSales).reduce(BigDecimal.ZERO, BigDecimal::add);
                            januaryToMarchSalesVolume = hospitalPurchases.add(dtpSales);
                        }
                        DdiDaySale afterAprilSale = new DdiDaySale();
                        Map<String, Object> afterAprilSaleMap = daySale.getParams();
                        afterAprilSaleMap.put("beginShipmentDate", date);
                        afterAprilSaleMap.put("endShipmentDate", endDateYear2024);
                        //afterAprilSaleMap.put("endShipmentDate", yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        afterAprilSale.setFifthLevelDepartment(salesAddress);
                        afterAprilSale.setParams(afterAprilSaleMap);
                        List<DdiDaySale> afterAprilSaleList = ddiDaySaleMapper.selectAreaDdiDaySaleList(afterAprilSale);
                        if (afterAprilSaleList.size() > 0) {
                            afterAprilSalesVolume = afterAprilSaleList.stream().filter(a -> a.getStandardQuantity() != null)
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        salesVolumeYear24 = januaryToMarchSalesVolume.add(afterAprilSalesVolume);

                        /** 24年排名 */
                        Integer rankingYear24 = yearRankingMap.get(salesAddress);
                        /** 上市后累计销量 */
                        BigDecimal salesAfterListing = new BigDecimal(0);
                        /** 上市后累计销量排名 */
                        Integer salesRankingAfterListing = null;

                        terminalSalesOffice.setSalesArea(salesArea);
                        terminalSalesOffice.setSalesRegion(salesRegion);
                        terminalSalesOffice.setSalesAddress(salesAddress);
                        terminalSalesOffice.setSalesDate(Date.from(salesDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                        terminalSalesOffice.setDailySalesVolume(dailySalesVolume);
                        terminalSalesOffice.setDailyRanking(dailyRanking);
                        terminalSalesOffice.setDailyGrowthRate(dailyGrowthRate);
                        terminalSalesOffice.setWeeklySalesVolume(weeklySalesVolume);
                        terminalSalesOffice.setWeeklyRanking(weeklyRanking);
                        terminalSalesOffice.setWeeklyGrowthRate(weeklyGrowthRate);
                        terminalSalesOffice.setMonthlySalesVolume(monthlySalesVolume);
                        terminalSalesOffice.setMonthlyRanking(monthlyRanking);
                        terminalSalesOffice.setMonthlyGrowthRate(monthlyGrowthRate);
                        terminalSalesOffice.setQuarterlySales(quarterlySales);
                        terminalSalesOffice.setQuarterlyRanking(quarterlyRanking);
                        terminalSalesOffice.setQuarterlyGrowthRate(quarterlyGrowthRate);
                        terminalSalesOffice.setSalesVolumeYear24(salesVolumeYear24);
                        terminalSalesOffice.setRankingYear24(rankingYear24);
                        terminalSalesOffice.setSalesAfterListing(salesAfterListing);
                        terminalSalesOffice.setSalesRankingAfterListing(salesRankingAfterListing);
                        int result = officeService.insertTerminalSalesOffice(terminalSalesOffice);
                        if (result == 1) {
                            successNum++;
                            successMsg.append("<br/>" + successNum + "、执行日期" + yesterday + "、销售地区 " + salesAddress + " 添加成功");
                        }
                    } catch (Exception e) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、执行日期" + yesterday + "、销售地区 " + addressList.get(i) + " 添加失败：";
                        failureMsg.append(msg + e.getMessage());
                    }
                }
            }

            log.info("新增成功：" + successNum + "条--" + successMsg.toString());
            log.error("新增失败：--" + failureNum + "条--" + failureMsg.toString());
        } else {
            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--定时DDI终端销售统计表-办事处--不在**********服务器执行的定时任务--跳过");
        }
    }
}
