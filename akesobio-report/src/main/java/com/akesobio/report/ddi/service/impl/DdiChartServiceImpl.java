package com.akesobio.report.ddi.service.impl;

import com.akesobio.report.ddi.domain.*;
import com.akesobio.report.ddi.mapper.*;
import com.akesobio.report.ddi.service.IDdiChartService;
import com.akesobio.report.ddi.vo.*;
import com.akesobio.report.groupFinance.vo.Query;
//import com.sun.javafx.collections.MappingChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DDI图表Service业务层处理
 */
@Service
public class DdiChartServiceImpl implements IDdiChartService {

    @Resource
    private TerminalSalesDirectorMapper terminalSalesDirectorMapper;

    @Resource
    private TerminalSalesProvinceMapper provinceMapper;

    @Autowired
    private ProvincePerformanceMapper provincePerformanceMapper;

    @Autowired
    private DdiMonthSaleMapper ddiBusinessMonthSaleMapper;

    @Autowired
    private DdiPharmacyMonthSaleMapper ddiPharmacyMonthSaleMapper;

    @Autowired
    private DdiDaySaleMapper ddiBusinessDaySaleMapper;

    @Autowired
    private DdiPharmacyDaySaleMapper ddiPharmacyDaySaleMapper;


    /**
     * 总监区
     * 总监区销售大区分布图表
     */
    @Override
    public DirectorAreaDistributionChartVo selectDirectorAreaDistributionChartVo(Query query) {
        DirectorAreaDistributionChartVo vo = new DirectorAreaDistributionChartVo();
        String dateType = query.getDateType();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();

        List<String> areaList = new ArrayList<>();
        areaList.add("东中国区");
        areaList.add("南中国一区");
        areaList.add("南中国二区");
        areaList.add("西中国区");
        areaList.add("北中国区");
        areaList.add("中中国区");
        List<Map<String, Object>> mapListData = new ArrayList<>();
        Map<String, BigDecimal> mapData = new HashMap<>();
        /** 前天日期 */
        LocalDate yesterday = LocalDate.now().minusDays(2);
        TerminalSalesDirector t = new TerminalSalesDirector();
        t.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        t.setProductName(productName);
        t.setIsTargetTerminal(isTargetTerminal);
        List<TerminalSalesDirector> directorList = terminalSalesDirectorMapper.selectTerminalSalesDirectorList(t);
        for (int i = 0; i < areaList.size(); i++) {
            String area = areaList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            switch (dateType) {
                case "日":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream()
                                .filter(a -> a.getDailySalesVolume() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getDailySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "月":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getMonthlySalesVolume() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getMonthlySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "季":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getQuarterlySales() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getQuarterlySales).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "年":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getSalesVolumeYear25() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getSalesVolumeYear25).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
              /*  case "累计":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getSalesAfterListing() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getSalesAfterListing).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;*/
            }
            mapData.put(area, totalAmount);
        }
        mapListData = mapData.entrySet().stream().map(entry -> {
            HashMap<String, Object> map = new HashMap<>();
            map.put("name", entry.getKey());
            map.put("value", entry.getValue());
            return map;
        }).collect(Collectors.toList());
        vo.setMapListData(mapListData);
        System.out.println("总监区销售大区分布图表VO-----" + vo);
        return vo;
    }

    /**
     * 总监区
     * 总监区大区销量图表
     */
    @Override
    public DirectorAreaSalesVolumeChartVo selectDirectorAreaSalesVolumeChartVo(Query query) {
        DirectorAreaSalesVolumeChartVo vo = new DirectorAreaSalesVolumeChartVo();
        String dateType = query.getDateType();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();
        List<String> areaList = new ArrayList<>();
        areaList.add("东中国区");
        areaList.add("南中国一区");
        areaList.add("南中国二区");
        areaList.add("西中国区");
        areaList.add("北中国区");
        areaList.add("中中国区");

        List<Map<String, Object>> mapListData = new ArrayList<>();
        Map<String, BigDecimal> mapData = new HashMap<>();
        /** 前天日期 */
        LocalDate yesterday = LocalDate.now().minusDays(2);
        TerminalSalesDirector t = new TerminalSalesDirector();
        t.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        t.setProductName(productName);
        t.setIsTargetTerminal(isTargetTerminal);
        List<TerminalSalesDirector> directorList = terminalSalesDirectorMapper.selectTerminalSalesDirectorList(t);
        for (int i = 0; i < areaList.size(); i++) {
            String area = areaList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            switch (dateType) {
                case "日":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getDailySalesVolume() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getDailySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "月":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getMonthlySalesVolume() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getMonthlySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "季":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getQuarterlySales() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getQuarterlySales).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "年":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getSalesVolumeYear25() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getSalesVolumeYear25).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
               /* case "累计":
                    if (directorList.size() > 0) {
                        totalAmount = directorList.stream().filter(a -> a.getSalesAfterListing() != null)
                                .filter(a -> a.getSalesArea() != null)
                                .filter(a -> a.getSalesArea().equals(area))
                                .map(TerminalSalesDirector::getSalesAfterListing).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;*/
            }
            mapData.put(area, totalAmount);
        }
        for (Map.Entry entry : mapData.entrySet()) {
            String mapKey = (String) entry.getKey();
            BigDecimal mapValue = (BigDecimal) entry.getValue();
            Map<String, Object> map = new HashMap<>();
            map.put("name", mapKey);
            map.put("value", mapValue);
            mapListData.add(map);
        }
        vo.setAreaList(areaList);
        vo.setMapListData(mapListData);
        System.out.println("总监区大区销量图表VO-----" + vo);
        return vo;
    }

    /**
     * 总监区
     * 总监区大区销售增长率图表
     */
    @Override
    public DirectorAreaSalesGrowthRateChartVo selectDirectorAreaSalesGrowthRateChartVo(Query query) {
        DirectorAreaSalesGrowthRateChartVo vo = new DirectorAreaSalesGrowthRateChartVo();
        String dateType = query.getDateType();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();
        List<String> legendList = new ArrayList<>();
        legendList.add("同比");
        legendList.add("环比");
        List<String> areaList = new ArrayList<>();
        areaList.add("东中国区");
        areaList.add("南中国一区");
        areaList.add("南中国二区");
        areaList.add("西中国区");
        areaList.add("北中国区");
        areaList.add("中中国区");
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();
        /** 前天日期 */
        LocalDate yesterday = LocalDate.now().minusDays(2);
        TerminalSalesDirector t = new TerminalSalesDirector();
        t.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        t.setProductName(productName);
        t.setIsTargetTerminal(isTargetTerminal);
        List<TerminalSalesDirector> directorList = terminalSalesDirectorMapper.selectTerminalSalesDirectorList(t);
        for (int i = 0; i < legendList.size(); i++) {
            List<BigDecimal> listData = new ArrayList<>();
            String legend = legendList.get(i);
            for (int j = 0; j < areaList.size(); j++) {
                String area = areaList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                if (legend.equals("同比")) {
                    listData.add(totalAmount);
                } else {
                    switch (dateType) {
                        case "日":
                            if (directorList.size() > 0) {
                                totalAmount = directorList.stream().filter(a -> a.getDailyGrowthRate() != null)
                                        .filter(a -> a.getSalesArea() != null)
                                        .filter(a -> a.getSalesArea().equals(area))
                                        .map(a -> {
                                            String growthRateStr = a.getDailyGrowthRate().replace("%", "");
                                            return new BigDecimal(growthRateStr);
                                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            break;
                        case "月":
                            if (directorList.size() > 0) {
                                totalAmount = directorList.stream().filter(a -> a.getMonthlyGrowthRate() != null)
                                        .filter(a -> a.getSalesArea() != null)
                                        .filter(a -> a.getSalesArea().equals(area))
                                        .map(a -> {
                                            String growthRateStr = a.getMonthlyGrowthRate().replace("%", "");
                                            return new BigDecimal(growthRateStr);
                                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            break;
                        case "季":
                            if (directorList.size() > 0) {
                                totalAmount = directorList.stream().filter(a -> a.getQuarterlyGrowthRate() != null)
                                        .filter(a -> a.getSalesArea() != null)
                                        .filter(a -> a.getSalesArea().equals(area))
                                        .map(a -> {
                                            String growthRateStr = a.getQuarterlyGrowthRate().replace("%", "");
                                            return new BigDecimal(growthRateStr);
                                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            break;
                    }
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> mapData = new HashMap<>();
            mapData.put(legend, listData);
            mapListData.add(mapData);
        }
        vo.setLegendList(legendList);
        vo.setAreaList(areaList);
        vo.setMapListData(mapListData);
        System.out.println("总监区大区销售增长率图表VO-----" + vo);
        return vo;
    }

    /**
     * 总监区
     * 总监区月度销量走势图表
     */
    @Override
    public DirectorAreaMonthlySalesTrendChartVo selectDirectorAreaMonthlySalesTrendChartVo(Query query) {
        DirectorAreaMonthlySalesTrendChartVo vo = new DirectorAreaMonthlySalesTrendChartVo();
        Integer year = query.getYear();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();

        List<String> legendList = new ArrayList<>();
        legendList.add("东中国区");
        legendList.add("南中国一区");
        legendList.add("南中国二区");
        legendList.add("西中国区");
        legendList.add("北中国区");
        legendList.add("中中国区");

        List<String> monthlyList = new ArrayList<>();
        monthlyList.add("1月");
        monthlyList.add("2月");
        monthlyList.add("3月");
        monthlyList.add("4月");
        monthlyList.add("5月");
        monthlyList.add("6月");
        monthlyList.add("7月");
        monthlyList.add("8月");
        monthlyList.add("9月");
        monthlyList.add("10月");
        monthlyList.add("11月");
        monthlyList.add("12月");
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        /**
         * DDI日销售  商业数据
         */
        DdiDaySale businessDaySale = new DdiDaySale();
        //businessDaySale.setBusinessMonth(yearMonthNew);
        businessDaySale.setBusinessMonth(String.valueOf(year));
        businessDaySale.setStandardProductName(productName);
        businessDaySale.setIsTargetTerminal(isTargetTerminal);
        List<DdiDaySale> businessDaySaleList = ddiBusinessDaySaleMapper.selectBusinessDdiDaySaleList2025(businessDaySale);

        /**
         * DDI日销售  药房数据
         */
        List<DdiDaySale> pharmacyDaySaleList = ddiBusinessDaySaleMapper.selectPharmacyDdiDaySaleList2025(businessDaySale);

       /* ProvincePerformance p = new ProvincePerformance();
        p.setYear(year);
        p.setProductName(productName);
        List<ProvincePerformance> provincePerformanceList = provincePerformanceMapper.selectProvincePerformanceList(p);*/


        for (int i = 0; i < legendList.size(); i++) {
            List<BigDecimal> listData = new ArrayList<>();
            String salesArea = legendList.get(i);
            for (int j = 0; j < monthlyList.size(); j++) {
                String month = monthlyList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                /** 商业 */
                BigDecimal businessQuantity = new BigDecimal(0);
                /** 药房 */
                BigDecimal pharmacyQuantity = new BigDecimal(0);
                switch (month) {
                    case "1月":
                        String yearMonth1 = year + "01";

                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth1))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth1))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "2月":
                        String yearMonth2 = year + "02";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth2))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth2))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "3月":
                        String yearMonth3 = year + "03";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth3))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth3))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "4月":
                        String yearMonth4 = year + "04";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth4))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth4))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "5月":
                        String yearMonth5= year + "05";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth5))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth5))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "6月":
                        String yearMonth6= year + "06";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth6))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth6))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "7月":
                        String yearMonth7= year + "07";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth7))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth7))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "8月":
                        String yearMonth8= year + "08";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth8))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth8))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "9月":
                        String yearMonth9= year + "09";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth9))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth9))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "10月":
                        String yearMonth10= year + "10";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth10))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth10))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "11月":
                        String yearMonth11= year + "11";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth11))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth11))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "12月":
                        String yearMonth12= year + "12";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth12))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth12))
                                    .filter(a -> a.getThirdLevelDepartments().equals(salesArea))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(salesArea, listData);
            mapListData.add(map);
        }

        vo.setLegendList(legendList);
        vo.setMonthlyList(monthlyList);
        vo.setMapListData(mapListData);
        System.out.println("总监区月度销量走势图表VO-----" + vo);
        return vo;
    }

    /**
     * 省份
     * 省份销售分布图表
     */
    @Override
    public ProvinceDistributionChartVo selectProvinceDistributionChartVo(Query query) {
        ProvinceDistributionChartVo vo = new ProvinceDistributionChartVo();
        String dateType = query.getDateType();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();
        List<String> provinceList = query.getProvinceList();
        List<Map<String, Object>> mapListData = new ArrayList<>();
        /** 前天日期 */
        LocalDate yesterday = LocalDate.now().minusDays(2);
        TerminalSalesProvince t = new TerminalSalesProvince();
        t.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        t.setProductName(productName);
        t.setIsTargetTerminal(isTargetTerminal);
        List<TerminalSalesProvince> salesProvinceList = provinceMapper.selectTerminalSalesProvinceList(t);
        for (int i = 0; i < provinceList.size(); i++) {
            String province = provinceList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            switch (dateType) {
                case "日":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getDailySalesVolume() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getDailySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "月":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getMonthlySalesVolume() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getMonthlySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "季":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getQuarterlySales() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getQuarterlySales).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "年":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getSalesVolumeYear25() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getSalesVolumeYear25).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
               /* case "累计":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getSalesAfterListing() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getSalesAfterListing).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;*/
            }
            Map<String, Object> map = new HashMap<>();
            map.put("name", province);
            map.put("value", totalAmount);
            mapListData.add(map);
        }
        vo.setMapListData(mapListData);
        System.out.println("省份销售分布图表VO-----" + vo);
        return vo;
    }

    /**
     * 省份
     * 省份销量图表
     */
    @Override
    public ProvinceSalesVolumeChartVo selectProvinceSalesVolumeChartVo(Query query) {
        ProvinceSalesVolumeChartVo vo = new ProvinceSalesVolumeChartVo();
        String dateType = query.getDateType();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();
        List<String> provinceList = query.getProvinceList();

        List<Map<String, Object>> mapListData = new ArrayList<>();
        Map<String, BigDecimal> mapData = new HashMap<>();
        /** 前天日期 */
        LocalDate yesterday = LocalDate.now().minusDays(2);
        TerminalSalesProvince t = new TerminalSalesProvince();
        t.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        t.setProductName(productName);
        t.setIsTargetTerminal(isTargetTerminal);
        List<TerminalSalesProvince> salesProvinceList = provinceMapper.selectTerminalSalesProvinceList(t);
        for (int i = 0; i < provinceList.size(); i++) {
            String province = provinceList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            switch (dateType) {
                case "日":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getDailySalesVolume() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getDailySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "月":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getMonthlySalesVolume() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getMonthlySalesVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "季":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getQuarterlySales() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getQuarterlySales).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
                case "年":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getSalesVolumeYear25() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getSalesVolumeYear25).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;
               /* case "累计":
                    if (salesProvinceList.size() > 0) {
                        totalAmount = salesProvinceList.stream().filter(a -> a.getSalesAfterListing() != null)
                                .filter(a -> a.getSalesProvince() != null)
                                .filter(a -> a.getSalesProvince().equals(province))
                                .map(TerminalSalesProvince::getSalesAfterListing).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    break;*/
            }
            mapData.put(province, totalAmount);
        }
        for (Map.Entry entry : mapData.entrySet()) {
            String mapKey = (String) entry.getKey();
            BigDecimal mapValue = (BigDecimal) entry.getValue();
            Map<String, Object> map = new HashMap<>();
            map.put("name", mapKey);
            map.put("value", mapValue);
            mapListData.add(map);
        }
        vo.setProvinceList(provinceList);
        vo.setMapListData(mapListData);
        System.out.println("省份销量图表VO-----" + vo);
        return vo;
    }

    /**
     * 省份
     * 省份销售增长率图表
     */
    @Override
    public ProvinceSalesGrowthRateChartVo selectProvinceSalesGrowthRateChartVo(Query query) {
        ProvinceSalesGrowthRateChartVo vo = new ProvinceSalesGrowthRateChartVo();
        List<String> provinceList = query.getProvinceList();
        String dateType = query.getDateType();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();
        List<String> legendList = new ArrayList<>();
        legendList.add("同比");
        legendList.add("环比");
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();
        /** 前天日期 */
        LocalDate yesterday = LocalDate.now().minusDays(2);
        TerminalSalesProvince t = new TerminalSalesProvince();
        t.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        t.setProductName(productName);
        t.setIsTargetTerminal(isTargetTerminal);
        List<TerminalSalesProvince> salesProvinceList = provinceMapper.selectTerminalSalesProvinceList(t);
        for (int i = 0; i < legendList.size(); i++) {
            List<BigDecimal> listData = new ArrayList<>();
            String legend = legendList.get(i);
            for (int j = 0; j < provinceList.size(); j++) {
                String province = provinceList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                if (legend.equals("同比")) {
                    listData.add(totalAmount);
                } else {
                    switch (dateType) {
                        case "日":
                            if (salesProvinceList.size() > 0) {
                                totalAmount = salesProvinceList.stream().filter(a -> a.getDailyGrowthRate() != null)
                                        .filter(a -> a.getSalesProvince() != null)
                                        .filter(a -> a.getSalesProvince().equals(province))
                                        .map(a -> {
                                            String growthRateStr = a.getDailyGrowthRate().replace("%", "");
                                            return new BigDecimal(growthRateStr);
                                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            break;
                        case "月":
                            if (salesProvinceList.size() > 0) {
                                totalAmount = salesProvinceList.stream().filter(a -> a.getMonthlyGrowthRate() != null)
                                        .filter(a -> a.getSalesProvince() != null)
                                        .filter(a -> a.getSalesProvince().equals(province))
                                        .map(a -> {
                                            String growthRateStr = a.getMonthlyGrowthRate().replace("%", "");
                                            return new BigDecimal(growthRateStr);
                                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            break;
                        case "季":
                            if (salesProvinceList.size() > 0) {
                                totalAmount = salesProvinceList.stream().filter(a -> a.getQuarterlyGrowthRate() != null)
                                        .filter(a -> a.getSalesProvince() != null)
                                        .filter(a -> a.getSalesProvince().equals(province))
                                        .map(a -> {
                                            String growthRateStr = a.getQuarterlyGrowthRate().replace("%", "");
                                            return new BigDecimal(growthRateStr);
                                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            break;
                    }
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> mapData = new HashMap<>();
            mapData.put(legend, listData);
            mapListData.add(mapData);
        }
        vo.setLegendList(legendList);
        vo.setProvinceList(provinceList);
        vo.setMapListData(mapListData);
        System.out.println("省份销售增长率图表VO-----" + vo);
        return vo;
    }


    /**
     * 省份
     * 省份月度销量走势图表
     */
    @Override
    public ProvinceMonthlySalesTrendChartVo selectProvinceMonthlySalesTrendChartVo(Query query) {
        ProvinceMonthlySalesTrendChartVo vo = new ProvinceMonthlySalesTrendChartVo();
        Integer year = query.getYear();
        String productName = query.getProductName();
        String isTargetTerminal=query.getIsTargetTerminal();

        List<String> provinceList = query.getProvinceList();
        List<String> monthlyList = new ArrayList<>();
        monthlyList.add("1月");
        monthlyList.add("2月");
        monthlyList.add("3月");
        monthlyList.add("4月");
        monthlyList.add("5月");
        monthlyList.add("6月");
        monthlyList.add("7月");
        monthlyList.add("8月");
        monthlyList.add("9月");
        monthlyList.add("10月");
        monthlyList.add("11月");
        monthlyList.add("12月");
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

       /* ProvincePerformance p = new ProvincePerformance();
        p.setYear(year);
        p.setProductName(productName);
        List<ProvincePerformance> provincePerformanceList = provincePerformanceMapper.selectProvincePerformanceList(p);*/

        /**
         * DDI日销售  商业数据
         */
        DdiDaySale businessDaySale = new DdiDaySale();
        //businessDaySale.setBusinessMonth(yearMonthNew);
        businessDaySale.setBusinessMonth(String.valueOf(year));
        businessDaySale.setStandardProductName(productName);
        businessDaySale.setIsTargetTerminal(isTargetTerminal);
        List<DdiDaySale> businessDaySaleList = ddiBusinessDaySaleMapper.selectBusinessDdiDaySaleList2025(businessDaySale);

        /**
         * DDI日销售  药房数据
         */
        List<DdiDaySale> pharmacyDaySaleList = ddiBusinessDaySaleMapper.selectPharmacyDdiDaySaleList2025(businessDaySale);

        for (int i = 0; i < provinceList.size(); i++) {
            List<BigDecimal> listData = new ArrayList<>();
            String province = provinceList.get(i);
            for (int j = 0; j < monthlyList.size(); j++) {
                String month = monthlyList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                /** 商业 */
                BigDecimal businessQuantity = new BigDecimal(0);
                /** 药房 */
                BigDecimal pharmacyQuantity = new BigDecimal(0);
                switch (month) {
                    case "1月":
                        String yearMonth1 = year + "01";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth1))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth1))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "2月":
                        String yearMonth2 = year + "02";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth2))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth2))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "3月":
                        String yearMonth3 = year + "03";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth3))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth3))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "4月":
                        String yearMonth4 = year + "04";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth4))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth4))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "5月":
                        String yearMonth5= year + "05";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth5))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth5))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "6月":
                        String yearMonth6= year + "06";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getThirdLevelDepartments() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth6))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth6))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "7月":
                        String yearMonth7= year + "07";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth7))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth7))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "8月":
                        String yearMonth8= year + "08";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth8))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth8))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "9月":
                        String yearMonth9= year + "09";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth9))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth9))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "10月":
                        String yearMonth10= year + "10";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth10))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth10))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "11月":
                        String yearMonth11= year + "11";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth11))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth11))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                    case "12月":
                        String yearMonth12= year + "12";
                        /** 商业 */
                        if (businessDaySaleList.size() > 0) {
                            businessQuantity = businessDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth12))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        /** 药房 */
                        if (pharmacyDaySaleList.size() > 0) {
                            pharmacyQuantity = pharmacyDaySaleList.stream()
                                    .filter(a -> a.getStandardQuantity() != null)
                                    .filter(a -> a.getCommercialProvince() != null)
                                    .filter(a -> a.getBusinessMonth().equals(yearMonth12))
                                    .filter(a -> a.getCommercialProvince().equals(province))
                                    .map(DdiDaySale::getStandardQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        totalAmount = businessQuantity.add(pharmacyQuantity);
                        break;
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(province, listData);
            mapListData.add(map);
        }

        vo.setLegendList(provinceList);
        vo.setMonthlyList(monthlyList);
        vo.setMapListData(mapListData);
        System.out.println("省份月度销量走势图表VO-----" + vo);
        return vo;
    }
}
