package com.akesobio.report.ddi.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiDaySaleMapper;
import com.akesobio.report.ddi.domain.DdiDaySale;
import com.akesobio.report.ddi.service.IDdiDaySaleService;

/**
 * 日销售Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class DdiDaySaleServiceImpl implements IDdiDaySaleService 
{
    @Autowired
    private DdiDaySaleMapper ddiDaySaleMapper;



    /**
     * 查询日销售列表
     * 
     * @param ddiDaySale 日销售
     * @return 日销售
     */
    @Override
    public List<DdiDaySale> selectDdiDaySaleList(DdiDaySale ddiDaySale)
    {
        return ddiDaySaleMapper.selectDdiDaySaleList(ddiDaySale);
    }

}
