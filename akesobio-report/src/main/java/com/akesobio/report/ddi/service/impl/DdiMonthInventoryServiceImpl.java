package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiMonthInventoryMapper;
import com.akesobio.report.ddi.domain.DdiMonthInventory;
import com.akesobio.report.ddi.service.IDdiMonthInventoryService;

/**
 * 月库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class DdiMonthInventoryServiceImpl implements IDdiMonthInventoryService {
    @Autowired
    private DdiMonthInventoryMapper ddiMonthInventoryMapper;


    /**
     * 查询月库存列表
     *
     * @param ddiMonthInventory 月库存
     * @return 月库存
     */
    @Override
    public List<DdiMonthInventory> selectDdiMonthInventoryList(DdiMonthInventory ddiMonthInventory) {
        return ddiMonthInventoryMapper.selectDdiMonthInventoryList(ddiMonthInventory);
    }


    /**
     * 查询所有月库存列表
     *
     * @param ddiMonthInventory 月库存
     * @return 月库存
     */
    @Override
    public List<DdiMonthInventory> selectAllDdiMonthInventoryList(DdiMonthInventory ddiMonthInventory) {
        return ddiMonthInventoryMapper.selectAllDdiMonthInventoryList(ddiMonthInventory);
    }
}
