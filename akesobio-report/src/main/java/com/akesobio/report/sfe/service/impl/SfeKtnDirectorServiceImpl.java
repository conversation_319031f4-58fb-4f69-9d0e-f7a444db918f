package com.akesobio.report.sfe.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import com.akesobio.report.ddi.util.GetComputerName;
import com.akesobio.report.ddi.util.MapRanking;
import com.akesobio.report.sfe.domain.*;
import com.akesobio.report.sfe.mapper.SfeKtnEntityMapper;
import com.akesobio.report.sfe.mapper.SfeRegionIndexMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sfe.mapper.SfeKtnDirectorMapper;
import com.akesobio.report.sfe.service.ISfeKtnDirectorService;

/**
 * 开坦尼报-总监Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class SfeKtnDirectorServiceImpl implements ISfeKtnDirectorService {
    @Autowired
    private SfeKtnDirectorMapper sfeKtnDirectorMapper;


    @Autowired
    private SfeKtnEntityMapper sfeKtnEntityMapper;

    @Autowired
    private SfeRegionIndexMapper sfeRegionIndexMapper;

    /**
     * 查询开坦尼报-总监
     *
     * @param id 开坦尼报-总监主键
     * @return 开坦尼报-总监
     */
    @Override
    public SfeKtnDirector selectSfeKtnDirectorById(Integer id) {
        return sfeKtnDirectorMapper.selectSfeKtnDirectorById(id);
    }

    /**
     * 查询开坦尼报-总监列表
     *
     * @param sfeKtnDirector 开坦尼报-总监
     * @return 开坦尼报-总监
     */
    @Override
    public List<SfeKtnDirector> selectSfeKtnDirectorList(SfeKtnDirector sfeKtnDirector) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date date = sfeKtnDirector.getExecutionDate();
        String executionDate = simpleDateFormat.format(date);
        String yearMonth = sdf.format(date);

        // 提取年份部分的字符串
        String yearStr = yearMonth.substring(0, yearMonth.indexOf('-'));
        // 转换为整数
        int year = Integer.parseInt(yearStr);

        String monthStr = yearMonth.substring(yearMonth.indexOf("-") + 1);
        // 转换为整数
        int month = Integer.parseInt(monthStr);


        YearMonth yearMonthNew = YearMonth.parse(yearMonth);
        // 使用 yearMonthNew 获取季度
        int quarter = (yearMonthNew.getMonthValue() - 1) / 3 + 1;


        // 转换为 LocalDate 对象
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        Month currentQuarterStartMonth = GetComputerName.getQuarterStartMonth(localDate.getMonthValue());
        /**  当前季度开始日期 */
        LocalDate quarterStartDate = localDate.withMonth(currentQuarterStartMonth.getValue())
                .with(TemporalAdjusters.firstDayOfMonth());
        /**  当前季度结束日期 */
        LocalDate quarterEndDate = quarterStartDate.plusMonths(2)
                .with(TemporalAdjusters.lastDayOfMonth());

        List<String> regionList = new ArrayList<>();
        regionList.add("江苏大区");
        regionList.add("浙江大区");
        regionList.add("上海大区");
        regionList.add("广东1大区");
        regionList.add("湖南大区");
        regionList.add("闽赣大区");
        regionList.add("广东2大区");
        regionList.add("广东3大区");
        regionList.add("广西大区");
        regionList.add("云贵大区");
        regionList.add("西北1大区");
        regionList.add("西北2大区");
        regionList.add("四川大区");
        regionList.add("重庆大区");
        regionList.add("京蒙大区");
        regionList.add("津晋冀大区");
        regionList.add("东北大区");
        regionList.add("山东大区");
        regionList.add("河南大区");
        regionList.add("湖北大区");
        regionList.add("安徽大区");

        List<SfeKtnDirector> list = new ArrayList<>();

        /* 查询开坦尼 日数据列表 */
        SfeKtnEntity sfeKtn = new SfeKtnEntity();
        sfeKtn.setExecutionDate(executionDate);
        List<SfeKtnEntity> sfeKtnList = sfeKtnEntityMapper.selectSfeKtnList(sfeKtn);

        /* 查询开坦尼 月数据列表 */
        SfeKtnEntity sfeKtnMonth = new SfeKtnEntity();
        sfeKtnMonth.setExecutionDate(yearMonth);
        List<SfeKtnEntity> sfeKtnMonthList = sfeKtnEntityMapper.selectSfeKtnMonthList(sfeKtnMonth);

        /* 查询开坦尼 季度数据列表 */
        SfeKtnEntity sfeKtnQuarter = new SfeKtnEntity();
        Map<String, Object> map = sfeKtnQuarter.getParams();
        map.put("beginQuarterDate", quarterStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        map.put("endQuarterDate", quarterEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sfeKtnQuarter.setParams(map);
        List<SfeKtnEntity> sfeKtnQuarterList = sfeKtnEntityMapper.selectSfeKtnQuarterList(sfeKtnQuarter);

        /** 查询大区指标信息数据 */
        SfeRegionIndex sfeRegionIndex = new SfeRegionIndex();
        sfeRegionIndex.setYear(year);
        sfeRegionIndex.setQuarter(quarter);
        sfeRegionIndex.setMonth(month);
        sfeRegionIndex.setProductName("开坦尼");
        List<SfeRegionIndex> sfeRegionIndexList = sfeRegionIndexMapper.selectSfeRegionIndexList(sfeRegionIndex);

        Map<String, BigDecimal> dayMap = new HashMap<>();
        Map<String, BigDecimal> monthMap = new HashMap<>();
        Map<String, BigDecimal> quarterMap = new HashMap<>();


        for (int i = 0; i < regionList.size(); i++) {
            String region = regionList.get(i);
            /** 当日处方合计支数 */
            BigDecimal totalNumber = new BigDecimal(0);

            /** 当日支数排名 */
            if (sfeKtnList != null && sfeKtnList.size() > 0) {
                totalNumber = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalNumber() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            /**月 月处方支数 */
            BigDecimal prescriptionCountMonth = new BigDecimal(0);
            if (sfeKtnMonthList != null && sfeKtnMonthList.size() > 0) {
                prescriptionCountMonth = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalNumber() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            /**月 月指标 */
            BigDecimal monthTotalNumber = new BigDecimal(0);
            if (sfeRegionIndexList != null && sfeRegionIndexList.size() > 0) {
                SfeRegionIndex firstItem = sfeRegionIndexList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getMonthlyIndicatorCount() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .findFirst().orElse(null);
                if (firstItem != null) {
                    monthTotalNumber = firstItem.getMonthlyIndicatorCount();
                }
            }

            /** 月支数指标进度 */
            BigDecimal monthlyIndicators = BigDecimal.ZERO;
            if (monthTotalNumber.compareTo(BigDecimal.ZERO) == 0) {

            } else {
                monthlyIndicators = prescriptionCountMonth.divide(monthTotalNumber,1,BigDecimal.ROUND_HALF_UP);

            }

            /** 季度处方支数 */
            BigDecimal prescriptionCountQuarter = new BigDecimal(0);
            if (sfeKtnQuarterList != null && sfeKtnQuarterList.size() > 0) {
                prescriptionCountQuarter = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalNumber() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            /** 季度指标 */
            BigDecimal quarterTotalNumber = new BigDecimal(0);
            if (sfeRegionIndexList != null && sfeRegionIndexList.size() > 0) {
                quarterTotalNumber = sfeRegionIndexList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getMonthlyIndicatorCount() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeRegionIndex::getMonthlyIndicatorCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            /** 季度指标进度 */
            BigDecimal quarterIndicators = BigDecimal.ZERO;
            if (quarterTotalNumber.compareTo(BigDecimal.ZERO) == 0) {

            } else {
                quarterIndicators = prescriptionCountQuarter.divide(quarterTotalNumber,1,BigDecimal.ROUND_HALF_UP);
            }


            dayMap.put(region, totalNumber);
            monthMap.put(region, monthlyIndicators);
            quarterMap.put(region, quarterIndicators);
        }

        Map<String, Integer> dayRankingMap = MapRanking.getRankings(dayMap);
        Map<String, Integer> monthRankingMap = MapRanking.getRankings(monthMap);
        Map<String, Integer> quarterRankingMap = MapRanking.getRankings(quarterMap);

        for (int i = 0; i < regionList.size(); i++) {
            String directorArea = "";
            String region = regionList.get(i);
            switch (region) {
                case "江苏大区":
                    directorArea = "东区";
                    break;
                case "浙江大区":
                    directorArea = "东区";
                    break;
                case "上海大区":
                    directorArea = "东区";
                    break;
                case "广东1大区":
                    directorArea = "南一区";
                    break;
                case "湖南大区":
                    directorArea = "南一区";
                    break;
                case "闽赣大区":
                    directorArea = "南一区";
                    break;
                case "广东2大区":
                    directorArea = "南二区";
                    break;
                case "广东3大区":
                    directorArea = "南二区";
                    break;
                case "广西大区":
                    directorArea = "南二区";
                    break;
                case "云贵大区":
                    directorArea = "南二区";
                    break;
                case "西北1大区":
                    directorArea = "西区";
                    break;
                case "西北2大区":
                    directorArea = "西区";
                    break;
                case "四川大区":
                    directorArea = "西区";
                    break;
                case "重庆大区":
                    directorArea = "西区";
                    break;
                case "京蒙大区":
                    directorArea = "北区";
                    break;
                case "津晋冀大区":
                    directorArea = "北区";
                    break;
                case "东北大区":
                    directorArea = "北区";
                    break;
                case "山东大区":
                    directorArea = "中区";
                    break;
                case "河南大区":
                    directorArea = "中区";
                    break;
                case "湖北大区":
                    directorArea = "中区";
                    break;
                case "安徽大区":
                    directorArea = "中区";
                    break;
            }

            /** 宫颈癌1L_NP */
            BigDecimal ggDay1 = new BigDecimal(0);

            /** 宫颈癌2L_NP */
            BigDecimal ggDay2 = new BigDecimal(0);

            /** 胃癌1L_NP */
            BigDecimal waDay1 = new BigDecimal(0);

            /** 胃癌2L+_NP */
            BigDecimal waDay2 = new BigDecimal(0);

            /** 肝癌HCC_NP */
            BigDecimal gaDay1 = new BigDecimal(0);

            /** 其他瘤种_NP */
            BigDecimal otherDay = new BigDecimal(0);

            /** 当日NP总数 */
            BigDecimal totalDayNp = new BigDecimal(0);

            /** 当日OP总数 */
            BigDecimal totalDayOp = new BigDecimal(0);

            /** 当日处方合计支数 */
            BigDecimal prescriptionCountDay = new BigDecimal(0);

            /** 当日支数排名 */
            Integer dailyCountRanking = dayRankingMap.get(region);


            /**月 宫颈癌1L_NP */
            BigDecimal ggMonth1 = new BigDecimal(0);

            /**月 宫颈癌2L_NP */
            BigDecimal ggMonth2 = new BigDecimal(0);

            /**月 胃癌1L_NP */
            BigDecimal waMonth1 = new BigDecimal(0);

            /**月 胃癌2L+_NP */
            BigDecimal waMonth2 = new BigDecimal(0);

            /**月 肝癌HCC_NP */
            BigDecimal gaMonth1 = new BigDecimal(0);

            /**月 其他瘤种_NP */
            BigDecimal otherMonth = new BigDecimal(0);

            /**月 NP总数 */
            BigDecimal npMonth = new BigDecimal(0);

            /**月 月OP数 */
            BigDecimal opMonth = new BigDecimal(0);

            /**月 月处方支数 */
            BigDecimal prescriptionCountMonth = new BigDecimal(0);

            /**月 月度指标达成率 */
            String monthlyAchievementRate = "";

            /**月 月指标达成率排名 */
            Integer monthlyAchievementRateRanking = monthRankingMap.get(region);


            /**季 宫颈癌1L_NP */
            BigDecimal ggQuarter1 = new BigDecimal(0);

            /**季  宫颈癌2L_NP */
            BigDecimal ggQuarter2 = new BigDecimal(0);

            /**季  胃癌1L_NP */
            BigDecimal waQuarter1 = new BigDecimal(0);

            /**季 胃癌2L+_NP */
            BigDecimal waQuarter2 = new BigDecimal(0);

            /**季  肝癌HCC_NP */
            BigDecimal gaQuarter1 = new BigDecimal(0);

            /**季  其他瘤种_NP */
            BigDecimal otherQuarter = new BigDecimal(0);


            /** 季度NP数 */
            BigDecimal npQuarter = new BigDecimal(0);

            /** 季度OP数 */
            BigDecimal opQuarter = new BigDecimal(0);

            /** 季度处方支数 */
            BigDecimal prescriptionCountQuarter = new BigDecimal(0);

            /** 季度指标达成率 */
            String quarterAchievementRate = "";

            /** 季度指标达成率排名 */
            Integer quarterAchievementRateRanking = quarterRankingMap.get(region);


            if (sfeKtnList != null && sfeKtnList.size() > 0) {
                ggDay1 = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGgDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGgDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                ggDay2 = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGgDay2() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGgDay2).reduce(BigDecimal.ZERO, BigDecimal::add);

                waDay1 = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getWaDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getWaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                waDay2 = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getWaDay2() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getWaDay2).reduce(BigDecimal.ZERO, BigDecimal::add);

                gaDay1 = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGaDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                otherDay = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getOtherDay() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getOtherDay).reduce(BigDecimal.ZERO, BigDecimal::add);

                totalDayNp = ggDay1.add(ggDay2).add(waDay1).add(waDay2).add(gaDay1).add(otherDay);

                totalDayOp = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalOp() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalOp).reduce(BigDecimal.ZERO, BigDecimal::add);

                prescriptionCountDay = sfeKtnList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalNumber() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalNumber).reduce(BigDecimal.ZERO, BigDecimal::add);

            }

            if (sfeKtnMonthList != null && sfeKtnMonthList.size() > 0) {
                ggMonth1 = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGgDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                ggMonth2 = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGgDay2() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGgDay2).reduce(BigDecimal.ZERO, BigDecimal::add);

                waMonth1 = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getWaDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getWaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                waMonth2 = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getWaDay2() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getWaDay2).reduce(BigDecimal.ZERO, BigDecimal::add);


                gaMonth1 = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGaDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                otherMonth = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getOtherDay() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getOtherDay).reduce(BigDecimal.ZERO, BigDecimal::add);

                npMonth = ggMonth1.add(ggMonth2).add(waMonth1).add(waMonth2).add(gaMonth1).add(otherMonth);

                opMonth = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalOp() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalOp).reduce(BigDecimal.ZERO, BigDecimal::add);

                prescriptionCountMonth = sfeKtnMonthList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalNumber() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalNumber).reduce(BigDecimal.ZERO, BigDecimal::add);

            }

            if (sfeKtnQuarterList != null && sfeKtnQuarterList.size() > 0) {
                ggQuarter1 = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGgDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                ggQuarter2 = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGgDay2() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGgDay2).reduce(BigDecimal.ZERO, BigDecimal::add);

                waQuarter1 = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getWaDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getWaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                waQuarter2 = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getWaDay2() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getWaDay2).reduce(BigDecimal.ZERO, BigDecimal::add);


                gaQuarter1 = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getGaDay1() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getGaDay1).reduce(BigDecimal.ZERO, BigDecimal::add);

                otherQuarter = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getOtherDay() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getOtherDay).reduce(BigDecimal.ZERO, BigDecimal::add);

                npQuarter = ggQuarter1.add(ggQuarter2).add(waQuarter1).add(waQuarter2).add(gaQuarter1).add(otherQuarter);

                opQuarter = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalOp() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalOp).reduce(BigDecimal.ZERO, BigDecimal::add);

                prescriptionCountQuarter = sfeKtnQuarterList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getTotalNumber() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeKtnEntity::getTotalNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            /**月 月指标 */
            BigDecimal monthTotalNumber = new BigDecimal(0);
            if (sfeRegionIndexList != null && sfeRegionIndexList.size() > 0) {
                SfeRegionIndex firstItem = sfeRegionIndexList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getMonthlyIndicatorCount() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .findFirst().orElse(null);
                if (firstItem != null) {
                    monthTotalNumber = firstItem.getMonthlyIndicatorCount();
                }
            }

            /** 月支数指标进度 */
            if (monthTotalNumber.compareTo(BigDecimal.ZERO) == 0) {
                monthlyAchievementRate = "0%";
            } else {
                BigDecimal indicators = prescriptionCountMonth.divide(monthTotalNumber,1,BigDecimal.ROUND_HALF_UP);
                monthlyAchievementRate = indicators + "%";
            }

            /** 季度指标 */
            BigDecimal quarterTotalNumber = new BigDecimal(0);
            if (sfeRegionIndexList != null && sfeRegionIndexList.size() > 0) {
                quarterTotalNumber = sfeRegionIndexList.stream()
                        .filter(a -> a.getRegion() != null)
                        .filter(a -> a.getMonthlyIndicatorCount() != null)
                        .filter(a -> a.getRegion().equals(region))
                        .map(SfeRegionIndex::getMonthlyIndicatorCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            /** 季度指标进度 */
            if (quarterTotalNumber.compareTo(BigDecimal.ZERO) == 0) {
                quarterAchievementRate = "0%";
            } else {
                BigDecimal indicators = prescriptionCountQuarter.divide(quarterTotalNumber,1,BigDecimal.ROUND_HALF_UP);
                quarterAchievementRate = indicators + "%";
            }


            SfeKtnDirector sfe = new SfeKtnDirector();
            sfe.setDirectorArea(directorArea);
            sfe.setRegion(region);
            sfe.setGjDay1(ggDay1);
            sfe.setGjDay2(ggDay2);
            sfe.setWaDay1(waDay1);
            sfe.setWaDay2(waDay2);
            sfe.setGaDay(gaDay1);
            sfe.setOtherDay(otherDay);
            sfe.setTotalDayNp(totalDayNp);
            sfe.setTotalDayOp(totalDayOp);
            sfe.setPrescriptionCountDay(prescriptionCountDay);
            sfe.setDailyCountRanking(dailyCountRanking);

            sfe.setGjMonth1(ggMonth1);
            sfe.setGjMonth2(ggMonth2);
            sfe.setWaMonth1(waMonth1);
            sfe.setWaMonth2(waMonth2);
            sfe.setGaMonth(gaMonth1);
            sfe.setNpMonth(npMonth);
            sfe.setOpMonth(opMonth);
            sfe.setPrescriptionCountMonth(prescriptionCountMonth);
            sfe.setMonthlyAchievementRate(monthlyAchievementRate);
            sfe.setMonthlyAchievementRateRanking(monthlyAchievementRateRanking);

            sfe.setGjQuarter1(ggQuarter1);
            sfe.setGjQuarter2(ggQuarter2);
            sfe.setWaQuarter1(waQuarter1);
            sfe.setWaQuarter2(waQuarter2);
            sfe.setGaQuarter(gaQuarter1);
            sfe.setNpQuarter(npQuarter);
            sfe.setOpQuarter(opQuarter);
            sfe.setPrescriptionCountQuarter(prescriptionCountQuarter);
            sfe.setQuarterAchievementRate(quarterAchievementRate);
            sfe.setQuarterAchievementRateRanking(quarterAchievementRateRanking);
            list.add(sfe);
        }
        return list;

        // return sfeKtnDirectorMapper.selectSfeKtnDirectorList(sfeKtnDirector);
    }

    /**
     * 新增开坦尼报-总监
     *
     * @param sfeKtnDirector 开坦尼报-总监
     * @return 结果
     */
    @Override
    public int insertSfeKtnDirector(SfeKtnDirector sfeKtnDirector) {
        return sfeKtnDirectorMapper.insertSfeKtnDirector(sfeKtnDirector);
    }

    /**
     * 修改开坦尼报-总监
     *
     * @param sfeKtnDirector 开坦尼报-总监
     * @return 结果
     */
    @Override
    public int updateSfeKtnDirector(SfeKtnDirector sfeKtnDirector) {
        return sfeKtnDirectorMapper.updateSfeKtnDirector(sfeKtnDirector);
    }

    /**
     * 批量删除开坦尼报-总监
     *
     * @param ids 需要删除的开坦尼报-总监主键
     * @return 结果
     */
    @Override
    public int deleteSfeKtnDirectorByIds(Integer[] ids) {
        return sfeKtnDirectorMapper.deleteSfeKtnDirectorByIds(ids);
    }

    /**
     * 删除开坦尼报-总监信息
     *
     * @param id 开坦尼报-总监主键
     * @return 结果
     */
    @Override
    public int deleteSfeKtnDirectorById(Integer id) {
        return sfeKtnDirectorMapper.deleteSfeKtnDirectorById(id);
    }
}
