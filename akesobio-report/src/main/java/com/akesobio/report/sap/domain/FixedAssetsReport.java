package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;

/**
 * 固定资产清单报表对象 FixedAssetsReport
 * 
 * <AUTHOR>
 * @date 2023-11-06
 */
public class FixedAssetsReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long sid;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String bukrs;

    /** 会计年度 */
    @Excel(name = "会计年度")
    private String gjahr;

    /** 过账期间 */
    @Excel(name = "过账期间")
    private String monat;

    /** 公司描述 */
    @Excel(name = "公司描述")
    private String butxt;

    /** 资产分类 */
    @Excel(name = "资产分类")
    private String anlkl;

    /** 资产分类描述 */
    @Excel(name = "资产分类描述")
    private String txk20;

    /** 资产编号 */
    @Excel(name = "资产编号")
    private String anln1;

    /** 资产主号文本 */
    @Excel(name = "资产主号文本")
    private String anlhtxt;

    /** 资产子编号 */
    @Excel(name = "资产子编号")
    private String anln2;

    /** 资产描述1 */
    @Excel(name = "资产描述1")
    private String txt50;

    /** 序列号 */
    @Excel(name = "序列号")
    private String sernr;

    /** 库存编号 */
    @Excel(name = "库存编号")
    private String invnr;

    /** 库存注释 */
    @Excel(name = "库存注释")
    private String invzu;

    /** 资产描述2 */
    @Excel(name = "资产描述2")
    private String txa50;

    /** 数量 */
    @Excel(name = "数量")
    private Long menge;

    /** 单位 */
    @Excel(name = "单位")
    private String meins;

    /** 成本中心 */
    @Excel(name = "成本中心")
    private String kostl;

    /** 成本中心描述 */
    @Excel(name = "成本中心描述")
    private String ltext;

    /** 资本化日期 */
    @Excel(name = "资本化日期")
    private String aktiv;

    /** 对象创建人姓名 */
    @Excel(name = "对象创建人姓名")
    private String ernam;

    /** 记录建立日期 */
    @Excel(name = "记录建立日期")
    private String erdat;

    /** 折旧码 */
    @Excel(name = "折旧码")
    private String afasl;

    /** 折旧范围描述 */
    @Excel(name = "折旧范围描述")
    private String afbktx;

    /** 资产状态 */
    @Excel(name = "资产状态")
    private String ordtx2;

    /** 变动方式 */
    @Excel(name = "变动方式")
    private String ordtx1;

    /** 开始折旧时间 */
    @Excel(name = "开始折旧时间")
    private String afabg;

    /** 使用寿命 */
    @Excel(name = "使用寿命")
    private String zndjar;

    /** 已折旧期间数 */
    @Excel(name = "已折旧期间数")
    private String zyzjqjs;

    /** 剩余使用期 */
    @Excel(name = "剩余使用期")
    private String zsysyq;

    /** 月折旧率(%) */
    @Excel(name = "月折旧率(%)")
    private BigDecimal zyzjl;

    /** 净残值率(%) */
    @Excel(name = "净残值率(%)")
    private BigDecimal schrwProz;

    /** 净残值 */
    @Excel(name = "净残值")
    private BigDecimal schrw;

    /** 账面净值 */
    @Excel(name = "账面净值")
    private BigDecimal zjz;

    /** 年初原值 */
    @Excel(name = "年初原值")
    private BigDecimal zncyz;

    /** 期末原值 */
    @Excel(name = "期末原值")
    private BigDecimal zqmyz;

    /** 本月计提折旧额 */
    @Excel(name = "本月计提折旧额")
    private BigDecimal zbyljzj;

    /** 本年累计折旧 */
    @Excel(name = "本年累计折旧")
    private BigDecimal zbnljzj;

    /** 年初累计折旧 */
    @Excel(name = "年初累计折旧")
    private BigDecimal zncljzj;

    /** 期末数累计折旧 */
    @Excel(name = "期末数累计折旧")
    private BigDecimal zqmljzj;

    /** 期末数减值准备 */
    @Excel(name = "期末数减值准备")
    private BigDecimal zqmjzzb;

    /** 冻结标识 */
    @Excel(name = "冻结标识")
    private String xspeb;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private String xloev;

    /** 不活动日期 */
    @Excel(name = "不活动日期")
    private String deakt;

    /** 注销人 */
    @Excel(name = "注销人")
    private String aenam;

    /** 对应折旧科目 */
    @Excel(name = "对应折旧科目")
    private String zzjkm;

    /** 对应折旧科目描述 */
    @Excel(name = "对应折旧科目描述")
    private String zzjkmms;

    /** 停用 */
    @Excel(name = "停用")
    private String xstil;

    /** 开始停用时间 */
    @Excel(name = "开始停用时间")
    private String adatu;

    /** 对应资产科目 */
    @Excel(name = "对应资产科目")
    private String zzckm;

    /** 对应资产科目描述 */
    @Excel(name = "对应资产科目描述")
    private String zzckmms;

    /** 对应费用科目 */
    @Excel(name = "对应费用科目")
    private String zfykm;

    /** 费用科目描述 */
    @Excel(name = "费用科目描述")
    private String zfykmms;

    /** 供应商 */
    @Excel(name = "供应商")
    private String lifnr;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String name1;

    /** 制造商 */
    @Excel(name = "制造商")
    private String herst;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setBukrs(String bukrs) 
    {
        this.bukrs = bukrs;
    }

    public String getBukrs() 
    {
        return bukrs;
    }
    public void setGjahr(String gjahr) 
    {
        this.gjahr = gjahr;
    }

    public String getGjahr() 
    {
        return gjahr;
    }
    public void setMonat(String monat) 
    {
        this.monat = monat;
    }

    public String getMonat() 
    {
        return monat;
    }
    public void setButxt(String butxt) 
    {
        this.butxt = butxt;
    }

    public String getButxt() 
    {
        return butxt;
    }
    public void setAnlkl(String anlkl) 
    {
        this.anlkl = anlkl;
    }

    public String getAnlkl() 
    {
        return anlkl;
    }
    public void setTxk20(String txk20) 
    {
        this.txk20 = txk20;
    }

    public String getTxk20() 
    {
        return txk20;
    }
    public void setAnln1(String anln1) 
    {
        this.anln1 = anln1;
    }

    public String getAnln1() 
    {
        return anln1;
    }
    public void setAnlhtxt(String anlhtxt) 
    {
        this.anlhtxt = anlhtxt;
    }

    public String getAnlhtxt() 
    {
        return anlhtxt;
    }
    public void setAnln2(String anln2) 
    {
        this.anln2 = anln2;
    }

    public String getAnln2() 
    {
        return anln2;
    }
    public void setTxt50(String txt50) 
    {
        this.txt50 = txt50;
    }

    public String getTxt50() 
    {
        return txt50;
    }
    public void setSernr(String sernr) 
    {
        this.sernr = sernr;
    }

    public String getSernr() 
    {
        return sernr;
    }
    public void setInvnr(String invnr) 
    {
        this.invnr = invnr;
    }

    public String getInvnr() 
    {
        return invnr;
    }
    public void setInvzu(String invzu) 
    {
        this.invzu = invzu;
    }

    public String getInvzu() 
    {
        return invzu;
    }
    public void setTxa50(String txa50) 
    {
        this.txa50 = txa50;
    }

    public String getTxa50() 
    {
        return txa50;
    }
    public void setMenge(Long menge) 
    {
        this.menge = menge;
    }

    public Long getMenge() 
    {
        return menge;
    }
    public void setMeins(String meins) 
    {
        this.meins = meins;
    }

    public String getMeins() 
    {
        return meins;
    }
    public void setKostl(String kostl) 
    {
        this.kostl = kostl;
    }

    public String getKostl() 
    {
        return kostl;
    }
    public void setLtext(String ltext) 
    {
        this.ltext = ltext;
    }

    public String getLtext() 
    {
        return ltext;
    }
    public void setAktiv(String aktiv) 
    {
        this.aktiv = aktiv;
    }

    public String getAktiv() 
    {
        return aktiv;
    }
    public void setErnam(String ernam) 
    {
        this.ernam = ernam;
    }

    public String getErnam() 
    {
        return ernam;
    }
    public void setErdat(String erdat) 
    {
        this.erdat = erdat;
    }

    public String getErdat() 
    {
        return erdat;
    }
    public void setAfasl(String afasl) 
    {
        this.afasl = afasl;
    }

    public String getAfasl() 
    {
        return afasl;
    }
    public void setAfbktx(String afbktx) 
    {
        this.afbktx = afbktx;
    }

    public String getAfbktx() 
    {
        return afbktx;
    }
    public void setOrdtx2(String ordtx2) 
    {
        this.ordtx2 = ordtx2;
    }

    public String getOrdtx2() 
    {
        return ordtx2;
    }
    public void setOrdtx1(String ordtx1) 
    {
        this.ordtx1 = ordtx1;
    }

    public String getOrdtx1() 
    {
        return ordtx1;
    }
    public void setAfabg(String afabg) 
    {
        this.afabg = afabg;
    }

    public String getAfabg() 
    {
        return afabg;
    }
    public void setZndjar(String zndjar) 
    {
        this.zndjar = zndjar;
    }

    public String getZndjar() 
    {
        return zndjar;
    }
    public void setZyzjqjs(String zyzjqjs) 
    {
        this.zyzjqjs = zyzjqjs;
    }

    public String getZyzjqjs() 
    {
        return zyzjqjs;
    }
    public void setZsysyq(String zsysyq) 
    {
        this.zsysyq = zsysyq;
    }

    public String getZsysyq() 
    {
        return zsysyq;
    }
    public void setZyzjl(BigDecimal zyzjl)
    {
        this.zyzjl = zyzjl;
    }

    public BigDecimal getZyzjl()
    {
        return zyzjl;
    }
    public void setSchrwProz(BigDecimal schrwProz)
    {
        this.schrwProz = schrwProz;
    }

    public BigDecimal getSchrwProz()
    {
        return schrwProz;
    }
    public void setSchrw(BigDecimal schrw)
    {
        this.schrw = schrw;
    }

    public BigDecimal getSchrw()
    {
        return schrw;
    }
    public void setZjz(BigDecimal zjz)
    {
        this.zjz = zjz;
    }

    public BigDecimal getZjz()
    {
        return zjz;
    }
    public void setZncyz(BigDecimal zncyz)
    {
        this.zncyz = zncyz;
    }

    public BigDecimal getZncyz()
    {
        return zncyz;
    }
    public void setZqmyz(BigDecimal zqmyz)
    {
        this.zqmyz = zqmyz;
    }

    public BigDecimal getZqmyz()
    {
        return zqmyz;
    }
    public void setZbyljzj(BigDecimal zbyljzj)
    {
        this.zbyljzj = zbyljzj;
    }

    public BigDecimal getZbyljzj()
    {
        return zbyljzj;
    }
    public void setZbnljzj(BigDecimal zbnljzj)
    {
        this.zbnljzj = zbnljzj;
    }

    public BigDecimal getZbnljzj()
    {
        return zbnljzj;
    }
    public void setZncljzj(BigDecimal zncljzj)
    {
        this.zncljzj = zncljzj;
    }

    public BigDecimal getZncljzj()
    {
        return zncljzj;
    }
    public void setZqmljzj(BigDecimal zqmljzj)
    {
        this.zqmljzj = zqmljzj;
    }

    public BigDecimal getZqmljzj()
    {
        return zqmljzj;
    }
    public void setZqmjzzb(BigDecimal zqmjzzb)
    {
        this.zqmjzzb = zqmjzzb;
    }

    public BigDecimal getZqmjzzb()
    {
        return zqmjzzb;
    }
    public void setXspeb(String xspeb) 
    {
        this.xspeb = xspeb;
    }

    public String getXspeb() 
    {
        return xspeb;
    }
    public void setXloev(String xloev) 
    {
        this.xloev = xloev;
    }

    public String getXloev() 
    {
        return xloev;
    }
    public void setDeakt(String deakt) 
    {
        this.deakt = deakt;
    }

    public String getDeakt() 
    {
        return deakt;
    }
    public void setAenam(String aenam) 
    {
        this.aenam = aenam;
    }

    public String getAenam() 
    {
        return aenam;
    }
    public void setZzjkm(String zzjkm) 
    {
        this.zzjkm = zzjkm;
    }

    public String getZzjkm() 
    {
        return zzjkm;
    }
    public void setZzjkmms(String zzjkmms) 
    {
        this.zzjkmms = zzjkmms;
    }

    public String getZzjkmms() 
    {
        return zzjkmms;
    }
    public void setXstil(String xstil) 
    {
        this.xstil = xstil;
    }

    public String getXstil() 
    {
        return xstil;
    }
    public void setAdatu(String adatu) 
    {
        this.adatu = adatu;
    }

    public String getAdatu() 
    {
        return adatu;
    }
    public void setZzckm(String zzckm) 
    {
        this.zzckm = zzckm;
    }

    public String getZzckm() 
    {
        return zzckm;
    }
    public void setZzckmms(String zzckmms) 
    {
        this.zzckmms = zzckmms;
    }

    public String getZzckmms() 
    {
        return zzckmms;
    }
    public void setZfykm(String zfykm) 
    {
        this.zfykm = zfykm;
    }

    public String getZfykm() 
    {
        return zfykm;
    }
    public void setZfykmms(String zfykmms) 
    {
        this.zfykmms = zfykmms;
    }

    public String getZfykmms() 
    {
        return zfykmms;
    }
    public void setLifnr(String lifnr) 
    {
        this.lifnr = lifnr;
    }

    public String getLifnr() 
    {
        return lifnr;
    }
    public void setName1(String name1) 
    {
        this.name1 = name1;
    }

    public String getName1() 
    {
        return name1;
    }
    public void setHerst(String herst) 
    {
        this.herst = herst;
    }

    public String getHerst() 
    {
        return herst;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("bukrs", getBukrs())
            .append("gjahr", getGjahr())
            .append("monat", getMonat())
            .append("butxt", getButxt())
            .append("anlkl", getAnlkl())
            .append("txk20", getTxk20())
            .append("anln1", getAnln1())
            .append("anlhtxt", getAnlhtxt())
            .append("anln2", getAnln2())
            .append("txt50", getTxt50())
            .append("sernr", getSernr())
            .append("invnr", getInvnr())
            .append("invzu", getInvzu())
            .append("txa50", getTxa50())
            .append("menge", getMenge())
            .append("meins", getMeins())
            .append("kostl", getKostl())
            .append("ltext", getLtext())
            .append("aktiv", getAktiv())
            .append("ernam", getErnam())
            .append("erdat", getErdat())
            .append("afasl", getAfasl())
            .append("afbktx", getAfbktx())
            .append("ordtx2", getOrdtx2())
            .append("ordtx1", getOrdtx1())
            .append("afabg", getAfabg())
            .append("zndjar", getZndjar())
            .append("zyzjqjs", getZyzjqjs())
            .append("zsysyq", getZsysyq())
            .append("zyzjl", getZyzjl())
            .append("schrwProz", getSchrwProz())
            .append("schrw", getSchrw())
            .append("zjz", getZjz())
            .append("zncyz", getZncyz())
            .append("zqmyz", getZqmyz())
            .append("zbyljzj", getZbyljzj())
            .append("zbnljzj", getZbnljzj())
            .append("zncljzj", getZncljzj())
            .append("zqmljzj", getZqmljzj())
            .append("zqmjzzb", getZqmjzzb())
            .append("xspeb", getXspeb())
            .append("xloev", getXloev())
            .append("deakt", getDeakt())
            .append("aenam", getAenam())
            .append("zzjkm", getZzjkm())
            .append("zzjkmms", getZzjkmms())
            .append("xstil", getXstil())
            .append("adatu", getAdatu())
            .append("zzckm", getZzckm())
            .append("zzckmms", getZzckmms())
            .append("zfykm", getZfykm())
            .append("zfykmms", getZfykmms())
            .append("lifnr", getLifnr())
            .append("name1", getName1())
            .append("herst", getHerst())
            .toString();
    }
}
