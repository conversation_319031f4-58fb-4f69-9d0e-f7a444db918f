package com.akesobio.report.clinical.controller.phaseOne;

import cn.hutool.core.util.StrUtil;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.common.utils.time.QueryTimeProcessing;
import com.akesobio.report.clinical.domain.*;
import com.akesobio.report.clinical.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Controller 临床第一批交付报表
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/ClinicalPersonnel")
public class ClinicalPersonnelController extends BaseController {

    @Autowired
    private ClinicalDepartService clinicalDepartService;

    @Autowired
    private ClinicalEntryService clinicalEntryService;

    @Autowired
    private ClinicalTrialBatchService clinicalTrialBatchService;

    @Autowired
    private ClinicalTransferService clinicalTransferService;

    @Autowired
    private ClinicalPostService clinicalPostService;

    @Autowired
    private ClinicalMaterialDesignService clinicalMaterialDesignService;

    @Autowired
    private ClinicalStampService clinicalStampService;

    @Autowired
    private ClinicalBorrowingService clinicalBorrowingService;

    @Autowired
    private ClinicalITWorkOrderService clinicalITWorkOrderService;

    @Autowired
    private ClinicalSystemPermissionService clinicalSystemPermissionService;

    @Autowired
    private ClinicalSealApplicationService clinicalSealApplicationService;

    /**
     * 临床 离职申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:depart:list')")
    @GetMapping("/depart/list")
    public TableDataInfo list(ClinicalDepart o) {
        startPage();
        List<ClinicalDepart> list = clinicalDepartService.queryClinicalDepartList(o);
        return getDataTable(list);
    }

    /**
     * 导出 临床 离职申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:depart:export')")
    @Log(title = "离职申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/depart/export")
    public void export(HttpServletResponse response, ClinicalDepart o) {
        List<ClinicalDepart> list = clinicalDepartService.queryClinicalDepartList(o);
        ExcelUtil<ClinicalDepart> util = new ExcelUtil<ClinicalDepart>(ClinicalDepart.class);
        util.exportExcel(response, list, "离职申请单");
    }

    /**
     * 临床 新入职（含实习生）入职单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:entry:list')")
    @GetMapping("/entry/list")
    public TableDataInfo list(ClinicalEntry o) {
        startPage();
        List<ClinicalEntry> list = clinicalEntryService.queryClinicalEntryList(o);
        return getDataTable(list);
    }

    /**
     * 导出 临床 新入职（含实习生）入职单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:entry:export')")
    @Log(title = "新入职（含实习生）入职单", businessType = BusinessType.EXPORT)
    @PostMapping("/entry/export")
    public void export(HttpServletResponse response, ClinicalEntry o) {
        List<ClinicalEntry> list = clinicalEntryService.queryClinicalEntryList(o);
        ExcelUtil<ClinicalEntry> util = new ExcelUtil<ClinicalEntry>(ClinicalEntry.class);
        util.exportExcel(response, list, "新入职（含实习生）入职单");
    }

    /**
     * 临床OA审批量-指定部分人员
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:ClinicalTrialBatch:list')")
    @GetMapping("/ClinicalTrialBatch/list")
    public TableDataInfo list(ClinicalTrialBatch o) {
        startPage();
        List<ClinicalTrialBatch> list = clinicalTrialBatchService.queryClinicalTrialBatchList(o);
        return getDataTable(list);
    }

    /**
     * 导出 临床OA审批量-指定部分人员
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:ClinicalTrialBatch:export')")
    @Log(title = "临床OA审批量", businessType = BusinessType.EXPORT)
    @PostMapping("/ClinicalTrialBatch/export")
    public void export(HttpServletResponse response, ClinicalTrialBatch o) {
        List<ClinicalTrialBatch> list = clinicalTrialBatchService.queryClinicalTrialBatchList(o);
        ExcelUtil<ClinicalTrialBatch> util = new ExcelUtil<ClinicalTrialBatch>(ClinicalTrialBatch.class);
        util.exportExcel(response, list, "临床OA审批量");
    }

    /**
     * 人员调动申请表
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:transfer:list')")
    @GetMapping("/transfer/list")
    public TableDataInfo list(ClinicalTransfer o) {
        startPage();
        List<ClinicalTransfer> list = clinicalTransferService.queryClinicalTransferList(o);
        return getDataTable(list);
    }

    /**
     * 导出 人员调动申请表
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:transfer:export')")
    @Log(title = "人员调动申请表", businessType = BusinessType.EXPORT)
    @PostMapping("/transfer/export")
    public void export(HttpServletResponse response, ClinicalTransfer o) throws Exception {
        List<ClinicalTransfer> list = clinicalTransferService.queryClinicalTransferList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "人员调动申请表");
    }

    /**
     * 快递邮寄申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:post:list')")
    @GetMapping("/post/list")
    public TableDataInfo list(ClinicalPost o) {
        startPage();
        List<ClinicalPost> list = clinicalPostService.queryClinicalPostList(o);
        return getDataTable(list);
    }

    /**
     * 导出 快递邮寄申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:post:export')")
    @Log(title = "快递邮寄申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/post/export")
    public void export(HttpServletResponse response, ClinicalPost o) throws Exception {
        List<ClinicalPost> list = clinicalPostService.queryClinicalPostList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "快递邮寄申请单");
    }

    /**
     * 临床实验文件盖章申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:stamp:list')")
    @GetMapping("/stamp/list")
    public TableDataInfo list(ClinicalStamp o) {
        startPage();
        List<ClinicalStamp> list = clinicalStampService.queryClinicalStampList(o);
        return getDataTable(list);
    }

    /**
     * 导出 临床实验文件盖章申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:stamp:export')")
    @Log(title = "临床实验文件盖章申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/stamp/export")
    public void export(HttpServletResponse response, ClinicalStamp o) throws Exception {
        if (StrUtil.isEmpty(o.getStartDate()) || StrUtil.isEmpty(o.getEndDate())) {
            Map<String, String> map = QueryTimeProcessing.returnTime();
            o.setStartDate(map.get("startDate"));
            o.setEndDate(map.get("endDate"));
        }
        QueryTimeProcessing.timeProcessing(o.getStartDate(), o.getEndDate(),"请选择提交时间间隔半年内的数据进行导出",180);
        List<ClinicalStamp> list = clinicalStampService.queryClinicalStampList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "临床实验文件盖章申请单");
    }

    /**
     * 导出 临床实验文件盖章申请单 全部
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:stamp:exportAll')")
    @Log(title = "临床实验文件盖章申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/stamp/exportAll")
    public void exportAll(HttpServletResponse response, ClinicalStamp o) throws Exception {
        List<ClinicalStamp> list = clinicalStampService.queryClinicalStampList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "临床实验文件盖章申请单");
    }

    /**
     * 盖章申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:sealApplication:list')")
    @GetMapping("/sealApplication/list")
    public TableDataInfo list(ClinicalSealApplication o) {
        startPage();
        List<ClinicalSealApplication> list = clinicalSealApplicationService.queryClinicalSealApplicationList(o);
        return getDataTable(list);
    }

    /**
     * 导出 盖章申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:sealApplication:export')")
    @Log(title = "盖章申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/sealApplication/export")
    public void export(HttpServletResponse response, ClinicalSealApplication o) throws Exception {
        List<ClinicalSealApplication> list = clinicalSealApplicationService.queryClinicalSealApplicationList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "盖章申请单");
    }

    /**
     * 物料设计申请表
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:materialDesign:list')")
    @GetMapping("/materialDesign/list")
    public TableDataInfo list(ClinicalMaterialDesign o) {
        startPage();
        List<ClinicalMaterialDesign> list = clinicalMaterialDesignService.queryClinicalMaterialDesignList(o);
        return getDataTable(list);
    }

    /**
     * 导出 物料设计申请表
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:materialDesign:export')")
    @Log(title = "物料设计申请表", businessType = BusinessType.EXPORT)
    @PostMapping("/materialDesign/export")
    public void export(HttpServletResponse response, ClinicalMaterialDesign o) throws Exception {
        List<ClinicalMaterialDesign> list = clinicalMaterialDesignService.queryClinicalMaterialDesignList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "物料设计申请表");
    }

    /**
     * 文件借阅申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:borrowing:list')")
    @GetMapping("/borrowing/list")
    public TableDataInfo list(ClinicalBorrowing o) {
        startPage();
        List<ClinicalBorrowing> list = clinicalBorrowingService.queryClinicalBorrowingList(o);
        return getDataTable(list);
    }

    /**
     * 导出 文件借阅申请单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:borrowing:export')")
    @Log(title = "文件借阅申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/borrowing/export")
    public void export(HttpServletResponse response, ClinicalBorrowing o) throws Exception {
        List<ClinicalBorrowing> list = clinicalBorrowingService.queryClinicalBorrowingList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "文件借阅申请单");
    }

    /**
     * IT服务工单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:ITWorkOrder:list')")
    @GetMapping("/ITWorkOrder/list")
    public TableDataInfo list(ClinicalITWorkOrder o) {
        startPage();
        List<ClinicalITWorkOrder> list = clinicalITWorkOrderService.queryClinicalITWorkOrderList(o);
        return getDataTable(list);
    }

    /**
     * 导出 IT服务工单
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:ITWorkOrder:export')")
    @Log(title = "IT服务工单", businessType = BusinessType.EXPORT)
    @PostMapping("/ITWorkOrder/export")
    public void export(HttpServletResponse response, ClinicalITWorkOrder o) throws Exception {
        List<ClinicalITWorkOrder> list = clinicalITWorkOrderService.queryClinicalITWorkOrderList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "IT服务工单");
    }

    /**
     * 太美&Veeva系统权限申请表
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:systemPermission:list')")
    @GetMapping("/systemPermission/list")
    public TableDataInfo list(ClinicalSystemPermission o) {
        startPage();
        List<ClinicalSystemPermission> list = clinicalSystemPermissionService.queryClinicalSystemPermissionList(o);
        return getDataTable(list);
    }

    /**
     * 导出 太美&Veeva系统权限申请表
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnel:systemPermission:export')")
    @Log(title = "太美&Veeva系统权限申请表", businessType = BusinessType.EXPORT)
    @PostMapping("/systemPermission/export")
    public void export(HttpServletResponse response, ClinicalSystemPermission o) throws Exception {
        List<ClinicalSystemPermission> list = clinicalSystemPermissionService.queryClinicalSystemPermissionList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "太美&Veeva系统权限申请表");
    }


}