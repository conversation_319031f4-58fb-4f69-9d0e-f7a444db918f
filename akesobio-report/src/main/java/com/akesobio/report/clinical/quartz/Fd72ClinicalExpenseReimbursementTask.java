package com.akesobio.report.clinical.quartz;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.akesobio.report.clinical.domain.*;
import com.akesobio.report.clinical.mapper.*;
import com.akesobio.report.ddi.util.GetComputerName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Component("Fd72ClinicalExpenseReimbursementTask")
@Slf4j
public class Fd72ClinicalExpenseReimbursementTask {

    @Autowired
    private Fd72ClinicalExpenseReimbursementMapper mapper;

    @Autowired
    private Fd72ClinicalExpenseReimbursementOldMapper oldMapper;

    @Autowired
    private ProcessManagementMapper processManagementMapper;

    @Autowired
    private CO04ExpressDeliveryFeeMapper co04ExpressDeliveryFeeMapper;

    @Autowired
    private FD06BusinessHospitalityMapper fd06BusinessHospitalityMapper;

    @Autowired
    private FD09OtherExpenseApplicationMapper fd09OtherExpenseApplicationMapper;


    /**
     * 定时更新FD72临床报销表
     */
    public void fd72ClinicalExpenseReimbursementTask() {
        String computerName = GetComputerName.getComputerName();
        if (!computerName.equals("SERVER22")) {
            mapper.deleteAllFd72ClinicalExpenseReimbursement();
            List<Fd72ClinicalExpenseReimbursement> listNew = new ArrayList<>();
            List<Fd72ClinicalExpenseReimbursementOld> listOld = oldMapper.selectFd72(new Fd72ClinicalExpenseReimbursementOld());
            log.info("查询FD72临床报销数据为" + listOld.size() + "条");
            int successNum = 0;
            if (listOld.size() > 0 && listOld != null) {
                for (int i = 0; i < listOld.size(); i++) {
                    Fd72ClinicalExpenseReimbursement c = new Fd72ClinicalExpenseReimbursement();
                    Fd72ClinicalExpenseReimbursementOld cOld = listOld.get(i);
                    //OA单据ID
                    c.setFdId(cOld.getFdId());
                    //OA报销编号
                    c.setOaReimbursementNumber(cOld.getOaReimbursementNumber());
                    //创建日期
                    c.setDateCreated(cOld.getDateCreated());
                    //报销类别
                    c.setReimbursementCategory(cOld.getReimbursementCategory());
                    //公司代码
                    c.setCompanyCode(cOld.getCompanyCode());
                    //公司名称
                    c.setCompanyName(cOld.getCompanyName());
                    //报销人
                    c.setReimbursementPerson(cOld.getReimbursementPerson());
                    //报销人工号
                    c.setReimbursementPersonNumber(cOld.getReimbursementPersonNumber());
                    //申请人
                    c.setApplicant(cOld.getApplicant());
                    //申请人工号
                    c.setApplicantNumber(cOld.getApplicantNumber());
                    //申请人职务
                    c.setApplicantPosition(cOld.getApplicantPosition());
                    //所属部门
                    c.setDepartment(cOld.getDepartment());
                    //成本中心代码
                    c.setCostCenterCode(cOld.getCostCenterCode());
                    //成本中心名称
                    c.setCostCenterName(cOld.getCostCenterName());
                    //关联审批单
                    c.setRelatedApprovalForm(cOld.getRelatedApprovalForm());
                    //业务发生期间
                    c.setBusinessPeriod(cOld.getBusinessPeriod());
                    //业务类型
                    c.setBusinessType(cOld.getBusinessType());
                    //类型细分
                    c.setTypeSegmentation(cOld.getTypeSegmentation());
                    //总裁办业务
                    c.setCeoBusiness(cOld.getCeoBusiness());
                    //借款余额
                    c.setLoanBalance(cOld.getLoanBalance());
                    //是否冲抵借款
                    c.setIsWhetherLoan(cOld.getIsWhetherLoan());
                    //合计金额
                    c.setTotalAmount(cOld.getTotalAmount());
                    //关联审批单金额
                    c.setRelatedApprovalAmount(cOld.getRelatedApprovalAmount());
                    //差异金额
                    c.setDifferenceAmount(cOld.getDifferenceAmount());
                    //业务描述
                    c.setServiceDescription(cOld.getServiceDescription());
                    //发票号码
                    c.setInvoiceNumber(cOld.getInvoiceNumber());
                    //不含税金额
                    c.setExcludingTaxAmount(cOld.getExcludingTaxAmount());
                    //专用发票税额
                    c.setSpecialInvoiceTaxAmount(cOld.getSpecialInvoiceTaxAmount());
                    //报销金额
                    c.setReimbursementAmount(cOld.getReimbursementAmount());
                    //币别
                    c.setCurrency(cOld.getCurrency());
                    //项目号
                    c.setProjectNo(cOld.getProjectNo());
                    c.setProjectNos(cOld.getProjectNos());
                    //中心名称
                    c.setCenterName(cOld.getCenterName());
                    //流程状态
                    c.setDocumentStatus(cOld.getDocumentStatus());
                    //当前环节
                    c.setCurrentSession(cOld.getCurrentSession());
                    //SAP应付凭证状态
                    c.setSapPayNumber(cOld.getSapPayNumber());
                    //凭证推送日期
                    c.setVoucherPushDate(cOld.getVoucherPushDate());
                    //申请主题
                    c.setApplicationSubject(cOld.getApplicationSubject());

                    //OA关联单号
                    String oaOddNumbers = cOld.getRelatedApprovalForm();
                    if (oaOddNumbers != null && oaOddNumbers != null) {
                        // 使用 org.json 库解析 JSON 字符串
                        JSONArray jsonArray = new JSONArray(oaOddNumbers);
                        // 获取数组中的第一个元素
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        // 获取 docId 的值
                        String docId = jsonObject.getStr("docId");
                        // 获取 subject 的值
                        String subject = jsonObject.getStr("subject");
                        c.setRelatedApprovalForm(subject);
                        if (docId != null && docId != "") {
                            ProcessManagement processManagement = processManagementMapper.selectProcessManagement(docId);
                            if (processManagement != null) {
                                //流程管理单号
                                String oddNumbers = processManagement.getOddNumbers();
                                if (oddNumbers != null && oddNumbers != null) {
                                    //CO-04快递费
                                    if (oddNumbers.contains("KD")) {
                                        CO04ExpressDeliveryFee co = new CO04ExpressDeliveryFee();
                                        co.setCo04Number(oddNumbers);
                                        List<CO04ExpressDeliveryFee> list = co04ExpressDeliveryFeeMapper.selectCO04(co);
                                        if (list.size() > 0 && list != null) {
                                            CO04ExpressDeliveryFee co04 = list.get(0);
                                            c.setCo04ID(co04.getCo04ID());
                                            c.setCo04Number(co04.getCo04Number());
                                            c.setCo04Name(co04.getCo04Name());
                                            c.setCo04CustomerName(co04.getCo04CustomerName());
                                            c.setCo04ServiceDescription(co04.getCo04ServiceDescription());
                                            c.setCo04EstimatedAmount(co04.getCo04EstimatedAmount());
                                            c.setApplicationFormId(co04.getCo04ID());
                                            c.setApplicationFormNumber(co04.getCo04Number());
                                            c.setApplicationFormName(co04.getCo04Name());
                                            c.setCustomerName(co04.getCo04CustomerName());
                                            c.setApplicationFormDescribe(co04.getCo04ServiceDescription());
                                            c.setApplicationFormAmount(co04.getCo04EstimatedAmount());
                                        }
                                    }
                                    //Fd06 业务招待费
                                    if (oddNumbers.contains("FD06")) {
                                        FD06BusinessHospitality fd06BusinessHospitality = new FD06BusinessHospitality();
                                        fd06BusinessHospitality.setFd06Number(oddNumbers);
                                        List<FD06BusinessHospitality> list = fd06BusinessHospitalityMapper.selectFD06(fd06BusinessHospitality);
                                        if (list.size() > 0 && list != null) {
                                            FD06BusinessHospitality fd06 = list.get(0);
                                            c.setFd06ID(fd06.getFd06ID());
                                            c.setFd06Number(fd06.getFd06Number());
                                            c.setFd06Name(fd06.getFd06Name());
                                            c.setFd06HospitalityType(fd06.getFd06HospitalityType());
                                            c.setFd06ServiceDescription(fd06.getFd06ServiceDescription());
                                            c.setFd06EstimatedAmount(fd06.getFd06EstimatedAmount());
                                            c.setApplicationFormId(fd06.getFd06ID());
                                            c.setApplicationFormNumber(fd06.getFd06Number());
                                            c.setApplicationFormName(fd06.getFd06Name());
                                            c.setApplicationFormType(fd06.getFd06HospitalityType());
                                            c.setApplicationFormDescribe(fd06.getFd06ServiceDescription());
                                            c.setApplicationFormAmount(fd06.getFd06EstimatedAmount());
                                        }
                                    }
                                    //FD09-其他费用申请
                                    if (oddNumbers.contains("FD09")) {
                                        FD09OtherExpenseApplication fd09OtherExpenseApplication = new FD09OtherExpenseApplication();
                                        fd09OtherExpenseApplication.setFd09Number(oddNumbers);
                                        List<FD09OtherExpenseApplication> list = fd09OtherExpenseApplicationMapper.selectFD09(fd09OtherExpenseApplication);
                                        if (list.size() > 0 && list != null) {
                                            FD09OtherExpenseApplication fd09 = list.get(0);
                                            c.setFd09ID(fd09.getFd09ID());
                                            c.setFd09Number(fd09.getFd09Number());
                                            c.setFd09Name(fd09.getFd09Name());
                                            c.setFd09ExpenseType(fd09.getFd09ExpenseType());
                                            c.setFd09CustomerName(fd09.getFd09CustomerName());
                                            c.setFd09SubjectID(fd09.getFd09SubjectID());
                                            c.setFd09VisitNumber(fd09.getFd09VisitNumber());
                                            c.setFd09AdvancePayment(fd09.getFd09AdvancePayment());
                                            c.setFd09QuanrongPayment(fd09.getFd09QuanrongPayment());
                                            c.setFd09ProtocolPayment(fd09.getFd09ProtocolPayment());
                                            c.setFd09CostDescription(fd09.getFd09CostDescription());
                                            c.setFd09ApplicationsAmount(fd09.getFd09ApplicationsAmount());
                                            c.setApplicationFormId(fd09.getFd09ID());
                                            c.setApplicationFormNumber(fd09.getFd09Number());
                                            c.setApplicationFormName(fd09.getFd09Name());
                                            c.setApplicationFormType(fd09.getFd09ExpenseType());
                                            c.setCustomerName(fd09.getFd09CustomerName());
                                            c.setSubjectId(fd09.getFd09SubjectID());
                                            c.setVisitNumber(fd09.getFd09VisitNumber());
                                            c.setIsAdvancePayment(fd09.getFd09AdvancePayment());
                                            c.setIsQuanrongPayment(fd09.getFd09QuanrongPayment());
                                            c.setIsProtocolPayment(fd09.getFd09ProtocolPayment());
                                            c.setApplicationFormDescribe(fd09.getFd09CostDescription());
                                            c.setApplicationFormAmount(fd09.getFd09ApplicationsAmount());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // 获取当前日期
                    LocalDate today = LocalDate.now();
                    //执行日期
                    String executionDate = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    // 将格式化的字符串转换为 java.sql.Date 对象
                    Date sqlDate = Date.valueOf(executionDate);
                    c.setExecutionDate(sqlDate);
                    int result = mapper.insertFd72ClinicalExpenseReimbursement(c);
                    if (result == 1) {
                        successNum++;
                    }
                }
            }
            log.info("定时更新FD72临床报销表数量为" + successNum + "条");
        }
    }
}
