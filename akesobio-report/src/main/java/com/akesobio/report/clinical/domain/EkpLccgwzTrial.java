package com.akesobio.report.clinical.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 临床试验物资2024对象 ekp_lccgwz_trial
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@TableName("ekp_lccgwz_trial")
public class EkpLccgwzTrial extends BaseEntity
{
    private static final long serialVersionUID = 1L;



    /** 表单名称 */
    @Excel(name = "表单名称")
    private String formName;

    /** 基本信息 */
    @Excel(name = "基本信息")
    private String basicInfo;

    /** 单号 */
    @Excel(name = "单号")
    private String serialNumber;

    /** 项目号 */
    @Excel(name = "项目号")
    private String projectNumber;

    /** 中心编号 */
    @Excel(name = "中心编号")
    private String centerCode;

    /** 中心名称 */
    @Excel(name = "中心名称")
    private String centerName;

    /** 科室 */
    @Excel(name = "科室")
    private String technicalOffices;

    /** 主要研究者 */
    @Excel(name = "主要研究者")
    private String principalInvestigator;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicant;

    /** 申请日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applicationDate;

    /** 试验物资发放记录表电子签署人姓名 */
    @Excel(name = "试验物资发放记录表电子签署人姓名")
    private String signatory;

    /** 所在城市 */
    @Excel(name = "所在城市")
    private String city;

    /** 接收人信息 */
    @Excel(name = "接收人")
    private String recipient;

    @Excel(name = "接收人电话")
    private String jsPhone;

    @Excel(name = "接收人地址")
    private String recipientInfo;

    /** 物资名称 */
    @Excel(name = "物资名称")
    private String materialName;

    /** 属性 */
    @Excel(name = "属性")
    private String attribute;

    /** 品牌 */
    @Excel(name = "品牌")
    private String clinicalMaterialBrand;

    /** 型号 */
    @Excel(name = "型号")
    private String model;

    /** 规格 */
    @Excel(name = "规格")
    private String specifications;

    /** 设备编号/批号 */
    @Excel(name = "设备编号/批号")
    private String equipmentNo;

    /** 生产日期 */
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产日期/校准日期", dateFormat = "yyyy-MM-dd")
    private Date productionDate;

    /** 数量 */
    @Excel(name = "数量")
    private String number;

    /** 期望交付日期 */
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期望交付日期", dateFormat = "yyyy-MM-dd")
    private Date expectedDeliveryDate;

    /** 供应方式 */
    @Excel(name = "供应方式")
    private String modesOfSupply;

    /** 物流信息 */
    @Excel(name = "物流信息")
    private String logisticsInformation;

    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当前节点到达日期", dateFormat = "yyyy-MM-dd")
    private Date arrivalTime;

    /** 预算单价(元) */
    @Excel(name = "预算单价(元)")
    private String budgetUnitPrice;

    /** 总金额 */
    @Excel(name = "总金额")
    private String totalAmount;

    @Excel(name = "上一节点名称")
    private String nodeName;

    @Excel(name = "上一节点处理人")
    private String nodePerson;

    /** ID */
    private String id;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 表单状态 */
    @Excel(name = "表单状态")
    private String docStatus;

    /** 结束日期 */
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", dateFormat = "yyyy-MM-dd")
    private Date flowEndDate;

    /** 当前节点 */
    @Excel(name = "当前节点")
    private String currentNode;

    /** 当前处理人 */
    @Excel(name = "当前处理人")
    private String currentHandler;
    //modes_of_supply_handler

    @Excel(name = "物资组申请采购处理人")
    private String modesOfSupplyHandler;
    @Excel(name = "物资组申请采购邮寄时间")
    private Date mailingTime;

    @Excel(name = "中山仓库发货处理人")
    private String modesOfSupplyHandler2;
    @Excel(name = "中山仓库发货邮寄时间")
    private Date mailingTime2;

    @Excel(name = "IT采购处理人")
    private String modesOfSupplyHandler3;
    @Excel(name = "IT采购邮寄时间")
    private Date mailingTime3;

    @Excel(name = "物资组转调处理人")
    private String modesOfSupplyHandler4;
    @Excel(name = "物资组转调邮寄时间")
    private Date mailingTime4;

}
