package com.akesobio.report.groupFinance.service.impl;

import com.akesobio.report.basicData.domain.ProjectCostSummary;
import com.akesobio.report.basicData.domain.ProjectProgressSummary;
import com.akesobio.report.basicData.mapper.ProjectCostSummaryMapper;
import com.akesobio.report.basicData.mapper.ProjectProgressSummaryMapper;
import com.akesobio.report.groupFinance.domain.*;
import com.akesobio.report.groupFinance.mapper.*;
import com.akesobio.report.groupFinance.service.IResearchDevelopmentGroupChartService;
import com.akesobio.report.groupFinance.util.GetLastYearMonth;
import com.akesobio.report.groupFinance.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 集团财务
 * 研发组  Service实现
 */
@Service
public class ResearchDevelopmentGroupChartServiceImpl implements IResearchDevelopmentGroupChartService {

    @Autowired
    private ExistingInventoryMapper existingInventoryMapper;

    @Autowired
    private PhysicalInventoryMapper physicalInventoryMapper;

    @Autowired
    private ProjectProgressSummaryMapper projectProgressSummaryMapper;

    @Autowired
    private ProjectCostSummaryMapper projectCostSummaryMapper;

    @Autowired
    private ResearchDevelopmentExpenseSummaryMapper researchDevelopmentExpenseSummaryMapper;

    @Autowired
    private ResearchDevelopmenteExpenseYearMapper researchDevelopmenteExpenseYearMapper;

    @Autowired
    private ResearchDevelopmentExpenseCostMapper researchDevelopmentExpenseCostMapper;

    @Autowired
    private ResearchDevelopmentExpensePipelineMapper researchDevelopmentExpensePipelineMapper;

    @Autowired
    private ResearchDevelopmentExpenseProjectMapper researchDevelopmentExpenseProjectMapper;

    /**
     * 物料库存 图表
     */
    @Override
    public MaterialInventoryChartVo selectMaterialInventoryChartVo(Query query) {
        String materialCode = "5103";
        String inventoryDate = "";
        if (query.getMaterialCode() != null && query.getMaterialCode() != "") {
            materialCode = query.getMaterialCode();
        }
        if (query.getDate() != null && query.getDate() != "") {
            inventoryDate = query.getDate().replace("-", "");
        }
        MaterialInventoryChartVo vo = new MaterialInventoryChartVo();
        List<String> companyList = new ArrayList<>();
        companyList.add("中山康方");
        companyList.add("康方天成");
        companyList.add("康方赛诺");
        companyList.add("康方药业");
        companyList.add("康融广东");
        companyList.add("康融广州");

        /**
         * 库存数量
         * key为库存数量，value为数量集合
         */
        Map<String, List<BigDecimal>> inventoryQuantityMapList = new HashMap<>();
        List<BigDecimal> inventoryQuantityList = new ArrayList<>();

        /**
         * 库存金额
         * key为库存金额，value为金额集合
         */
        Map<String, List<BigDecimal>> inventoryAmountMapList = new HashMap<>();
        List<BigDecimal> inventoryAmountList = new ArrayList<>();
        /**
         * 实际数量
         * key为实际数量，value为数量集合
         */
        Map<String, List<BigDecimal>> transitQuantityMapList = new HashMap<>();
        List<BigDecimal> transitQuantityList = new ArrayList<>();

        /**
         * 实际金额
         * key为实际金额，value为金额集合
         */
        Map<String, List<BigDecimal>> transitAmountMapList = new HashMap<>();
        List<BigDecimal> transitAmountList = new ArrayList<>();

        /**
         * 3个月到期的数量
         * key为3个月到期的数量，value为数量集合
         */
        Map<String, List<BigDecimal>> expiredQuantityMapList = new HashMap<>();
        List<BigDecimal> expiredQuantityList = new ArrayList<>();


        /**
         * 3个月到期的金额
         * key为3个月到期的金额，value为金额集合
         */
        Map<String, List<BigDecimal>> expiredAmountMapList = new HashMap<>();
        List<BigDecimal> expiredAmountList = new ArrayList<>();
        for (int i = 0; i < companyList.size(); i++) {
            String code = "";
            PhysicalInventory physicalInventory = new PhysicalInventory();
            physicalInventory.setMaterialNo(materialCode);
            physicalInventory.setInventoryDate(inventoryDate);
            ExistingInventory existingInventory = new ExistingInventory();
            existingInventory.setMaterialNo(materialCode);
            existingInventory.setInventoryDate(inventoryDate);
            switch (companyList.get(i)) {
                case "中山康方":
                    code = "1000";
                    physicalInventory.setFactory(code);
                    existingInventory.setFactory(code);
                    List<PhysicalInventory> physicalInventoryList = physicalInventoryMapper.selectPhysicalInventoryList(physicalInventory);
                    /** 库存总数量  */
                    BigDecimal inventoryTotalQuantity = physicalInventoryList.stream().map(PhysicalInventory::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryQuantityList.add(inventoryTotalQuantity);
                    /** 库存总金额 */
                    BigDecimal inventoryTotalAmount = physicalInventoryList.stream().map(PhysicalInventory::getInventoryAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    inventoryAmountList.add(inventoryTotalAmount);
                    /** 实际总数量 */
                    BigDecimal transitTotalQuantity = physicalInventoryList.stream().map(PhysicalInventory::getTransitQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    transitQuantityList.add(transitTotalQuantity);
                    /** 实际总金额 */
                    BigDecimal transitTotalAmount = physicalInventoryList.stream().map(PhysicalInventory::getTransitAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    transitAmountList.add(transitTotalAmount);

                    List<ExistingInventory> existingInventoryList = existingInventoryMapper.selectExistingInventoryList(existingInventory);
                    /** 3个月到期的总数量 */
                    BigDecimal expiredTotalQuantity = existingInventoryList.stream().map(ExistingInventory::getExpiredQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expiredQuantityList.add(expiredTotalQuantity);
                    /** 3个月到期的总金额  Total*/
                    BigDecimal expiredTotalAmount = existingInventoryList.stream().filter(a -> a.getExpiredAmount() != null).map(ExistingInventory::getExpiredAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    expiredAmountList.add(expiredTotalAmount);
                    break;
                case "康方天成":
                    code = "1020";
                    physicalInventory.setFactory(code);
                    existingInventory.setFactory(code);
                    List<PhysicalInventory> physicalInventoryList1 = physicalInventoryMapper.selectPhysicalInventoryList(physicalInventory);
                    /** 库存总数量  */
                    BigDecimal inventoryTotalQuantity1 = physicalInventoryList1.stream().map(PhysicalInventory::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryQuantityList.add(inventoryTotalQuantity1);
                    /** 库存总金额 */
                    BigDecimal inventoryTotalAmount1 = physicalInventoryList1.stream().map(PhysicalInventory::getInventoryAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    inventoryAmountList.add(inventoryTotalAmount1);
                    /** 实际总数量 */
                    BigDecimal transitTotalQuantity1 = physicalInventoryList1.stream().map(PhysicalInventory::getTransitQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    transitQuantityList.add(transitTotalQuantity1);
                    /** 实际总金额 */
                    BigDecimal transitTotalAmount1 = physicalInventoryList1.stream().map(PhysicalInventory::getTransitAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    transitAmountList.add(transitTotalAmount1);

                    List<ExistingInventory> existingInventoryList1 = existingInventoryMapper.selectExistingInventoryList(existingInventory);
                    /** 3个月到期的总数量 */
                    BigDecimal expiredTotalQuantity1 = existingInventoryList1.stream().map(ExistingInventory::getExpiredQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expiredQuantityList.add(expiredTotalQuantity1);
                    /** 3个月到期的总金额  Total*/
                    BigDecimal expiredTotalAmount1 = existingInventoryList1.stream().filter(a -> a.getExpiredAmount() != null).map(ExistingInventory::getExpiredAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    expiredAmountList.add(expiredTotalAmount1);
                    break;
                case "康方赛诺":
                    code = "1030";
                    physicalInventory.setFactory(code);
                    existingInventory.setFactory(code);
                    List<PhysicalInventory> physicalInventoryList2 = physicalInventoryMapper.selectPhysicalInventoryList(physicalInventory);
                    /** 库存总数量  */
                    BigDecimal inventoryTotalQuantity2 = physicalInventoryList2.stream().map(PhysicalInventory::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryQuantityList.add(inventoryTotalQuantity2);
                    /** 库存总金额 */
                    BigDecimal inventoryTotalAmount2 = physicalInventoryList2.stream().map(PhysicalInventory::getInventoryAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    inventoryAmountList.add(inventoryTotalAmount2);
                    /** 实际总数量 */
                    BigDecimal transitTotalQuantity2 = physicalInventoryList2.stream().map(PhysicalInventory::getTransitQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    transitQuantityList.add(transitTotalQuantity2);
                    /** 实际总金额 */
                    BigDecimal transitTotalAmount2 = physicalInventoryList2.stream().map(PhysicalInventory::getTransitAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    transitAmountList.add(transitTotalAmount2);

                    List<ExistingInventory> existingInventoryList2 = existingInventoryMapper.selectExistingInventoryList(existingInventory);
                    /** 3个月到期的总数量 */
                    BigDecimal expiredTotalQuantity2 = existingInventoryList2.stream().map(ExistingInventory::getExpiredQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expiredQuantityList.add(expiredTotalQuantity2);
                    /** 3个月到期的总金额  Total*/
                    BigDecimal expiredTotalAmount2 = existingInventoryList2.stream().filter(a -> a.getExpiredAmount() != null).map(ExistingInventory::getExpiredAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    expiredAmountList.add(expiredTotalAmount2);
                    break;
                case "康方药业":
                    code = "1050";
                    physicalInventory.setFactory(code);
                    existingInventory.setFactory(code);
                    List<PhysicalInventory> physicalInventoryList3 = physicalInventoryMapper.selectPhysicalInventoryList(physicalInventory);
                    /** 库存总数量  */
                    BigDecimal inventoryTotalQuantity3 = physicalInventoryList3.stream().map(PhysicalInventory::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryQuantityList.add(inventoryTotalQuantity3);
                    /** 库存总金额 */
                    BigDecimal inventoryTotalAmount3 = physicalInventoryList3.stream().map(PhysicalInventory::getInventoryAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    inventoryAmountList.add(inventoryTotalAmount3);
                    /** 实际总数量 */
                    BigDecimal transitTotalQuantity3 = physicalInventoryList3.stream().map(PhysicalInventory::getTransitQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    transitQuantityList.add(transitTotalQuantity3);
                    /** 实际总金额 */
                    BigDecimal transitTotalAmount3 = physicalInventoryList3.stream().map(PhysicalInventory::getTransitAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    transitAmountList.add(transitTotalAmount3);

                    List<ExistingInventory> existingInventoryList3 = existingInventoryMapper.selectExistingInventoryList(existingInventory);
                    /** 3个月到期的总数量 */
                    BigDecimal expiredTotalQuantity3 = existingInventoryList3.stream().map(ExistingInventory::getExpiredQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expiredQuantityList.add(expiredTotalQuantity3);
                    /** 3个月到期的总金额  Total*/
                    BigDecimal expiredTotalAmount3 = existingInventoryList3.stream().filter(a -> a.getExpiredAmount() != null).map(ExistingInventory::getExpiredAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    expiredAmountList.add(expiredTotalAmount3);
                    break;
                case "康融广东":
                    code = "1060";
                    physicalInventory.setFactory(code);
                    existingInventory.setFactory(code);
                    List<PhysicalInventory> physicalInventoryList4 = physicalInventoryMapper.selectPhysicalInventoryList(physicalInventory);
                    /** 库存总数量  */
                    BigDecimal inventoryTotalQuantity4 = physicalInventoryList4.stream().map(PhysicalInventory::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryQuantityList.add(inventoryTotalQuantity4);
                    /** 库存总金额 */
                    BigDecimal inventoryTotalAmount4 = physicalInventoryList4.stream().map(PhysicalInventory::getInventoryAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    inventoryAmountList.add(inventoryTotalAmount4);
                    /** 实际总数量 */
                    BigDecimal transitTotalQuantity4 = physicalInventoryList4.stream().map(PhysicalInventory::getTransitQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    transitQuantityList.add(transitTotalQuantity4);
                    /** 实际总金额 */
                    BigDecimal transitTotalAmount4 = physicalInventoryList4.stream().map(PhysicalInventory::getTransitAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    transitAmountList.add(transitTotalAmount4);

                    List<ExistingInventory> existingInventoryList4 = existingInventoryMapper.selectExistingInventoryList(existingInventory);
                    /** 3个月到期的总数量 */
                    BigDecimal expiredTotalQuantity4 = existingInventoryList4.stream().map(ExistingInventory::getExpiredQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expiredQuantityList.add(expiredTotalQuantity4);
                    /** 3个月到期的总金额  Total*/
                    BigDecimal expiredTotalAmount4 = existingInventoryList4.stream().filter(a -> a.getExpiredAmount() != null).map(ExistingInventory::getExpiredAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    expiredAmountList.add(expiredTotalAmount4);
                    break;
                case "康融广州":
                    code = "1070";
                    physicalInventory.setFactory(code);
                    existingInventory.setFactory(code);
                    List<PhysicalInventory> physicalInventoryList5 = physicalInventoryMapper.selectPhysicalInventoryList(physicalInventory);
                    /** 库存总数量  */
                    BigDecimal inventoryTotalQuantity5 = physicalInventoryList5.stream().map(PhysicalInventory::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    inventoryQuantityList.add(inventoryTotalQuantity5);
                    /** 库存总金额 */
                    BigDecimal inventoryTotalAmount5 = physicalInventoryList5.stream().map(PhysicalInventory::getInventoryAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    inventoryAmountList.add(inventoryTotalAmount5);
                    /** 实际总数量 */
                    BigDecimal transitTotalQuantity5 = physicalInventoryList5.stream().map(PhysicalInventory::getTransitQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    transitQuantityList.add(transitTotalQuantity5);
                    /** 实际总金额 */
                    BigDecimal transitTotalAmount5 = physicalInventoryList5.stream().map(PhysicalInventory::getTransitAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    transitAmountList.add(transitTotalAmount5);

                    List<ExistingInventory> existingInventoryList5 = existingInventoryMapper.selectExistingInventoryList(existingInventory);
                    /** 3个月到期的总数量 */
                    BigDecimal expiredTotalQuantity5 = existingInventoryList5.stream().map(ExistingInventory::getExpiredQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expiredQuantityList.add(expiredTotalQuantity5);
                    /** 3个月到期的总金额  Total*/
                    BigDecimal expiredTotalAmount5 = existingInventoryList5.stream().filter(a -> a.getExpiredAmount() != null).map(ExistingInventory::getExpiredAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 0, BigDecimal.ROUND_HALF_UP);
                    expiredAmountList.add(expiredTotalAmount5);
                    break;
            }
        }
        inventoryQuantityMapList.put("现有库存数量", inventoryQuantityList);
        inventoryAmountMapList.put("现有库存金额（万元）", inventoryAmountList);
        transitQuantityMapList.put("实际数量", transitQuantityList);
        transitAmountMapList.put("实际金额（万元）", transitAmountList);
        expiredQuantityMapList.put("到期数量", expiredQuantityList);
        expiredAmountMapList.put("到期金额（万元）", expiredAmountList);

        vo.setCompanyList(companyList);
        vo.setInventoryQuantityMapList(inventoryQuantityMapList);
        vo.setInventoryAmountMapList(inventoryAmountMapList);
        vo.setTransitQuantityMapList(transitQuantityMapList);
        vo.setTransitAmountMapList(transitAmountMapList);
        vo.setExpiredQuantityMapList(expiredQuantityMapList);
        vo.setExpiredAmountMapList(expiredAmountMapList);
        System.out.println("物料库存VO----" + vo);
        return vo;
    }

    /**
     * 合同结算单价
     * 项目研究内容  图表
     */
    @Override
    public ProjectResearchContentChartVo selectProjectResearchContentChartVo(Query query) {
        String project = query.getProject();
        System.out.println("项目号--" + project);
        ProjectResearchContentChartVo vo = new ProjectResearchContentChartVo();
        List<String> list = new ArrayList<>();
        list.add("临床中心");
        list.add("CRO");
        list.add("SMO");
        list.add("中心实验室");
        list.add("招募费");
        list.add("临床保险");
        list.add("临床数统");
        list.add("受试者管理");
        list.add("中心影像");
        list.add("药物警戒");
        list.add("临床会务");
        list.add("系统");
        list.add("劳务费");
        list.add("临床稽查");
        list.add("联合化药");
        vo.setList(list);
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, BigDecimal> mapData = new HashMap<>();
        ProjectProgressSummary p = new ProjectProgressSummary();
        p.setProject(project);
        List<ProjectProgressSummary> summaryList = projectProgressSummaryMapper.selectProjectProgressSummaryList(p);

        for (int i = 0; i < list.size(); i++) {
            String researchContents = list.get(i);
            BigDecimal money = new BigDecimal(0);
            if (summaryList.size() > 0) {
                money = summaryList.stream()
                        .filter(a -> a.getResearchContents() != null)
                        .filter(a -> a.getResearchContents().equals(researchContents))
                        .map(ProjectProgressSummary::getSettlementUnitPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            mapData.put(list.get(i), money);
        }
        for (Map.Entry entry : mapData.entrySet()) {
            String mapKey = (String) entry.getKey();
            BigDecimal mapValue = (BigDecimal) entry.getValue();
            Map<String, Object> map = new HashMap<>();
            map.put("name", mapKey);
            map.put("value", mapValue);
            mapList.add(map);
        }
        vo.setList(list);
        vo.setMapList(mapList);
        System.out.println("项目研究内容图表VO-----" + vo);
        return vo;
    }

    /**
     * 合同结算单价
     * 管线对比 图表
     */
    @Override
    public PipelineComparisonChartVo selectPipelineComparisonChartVo(Query query) {
        PipelineComparisonChartVo vo = new PipelineComparisonChartVo();
        List<ProjectCostSummary> projectCostSummaryList = projectCostSummaryMapper.selectProjectCostSummaryList(new ProjectCostSummary());
        List<String> xAxisList = projectCostSummaryList.stream().map(ProjectCostSummary::getDrugPipeline).distinct().collect(Collectors.toList());
        System.out.println(xAxisList);
        List<String> listLegend = new ArrayList<>();
        listLegend.add("平均单例成本");
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();
        Map<String, List<BigDecimal>> map = new HashMap<>();

        /** 平均单例成本 集合 */
        List<BigDecimal> list = new ArrayList<>();

        ProjectCostSummary p = new ProjectCostSummary();
        List<ProjectCostSummary> costSummaryList = projectCostSummaryMapper.selectProjectCostSummaryList(p);

        for (int i = 0; i < xAxisList.size(); i++) {
            BigDecimal totalAmount = new BigDecimal(0);
            String drugPipeline = xAxisList.get(i);
            if (costSummaryList.size() > 0) {
                List<ProjectCostSummary> filteredCostSummaryList = costSummaryList.stream()
                        .filter(a -> a.getDrugPipeline() != null)
                        .filter(a -> a.getDrugPipeline().equals(drugPipeline))
                        .collect(Collectors.toList());
                BigDecimal number = BigDecimal.valueOf(filteredCostSummaryList.size());
                totalAmount = filteredCostSummaryList.stream().filter(b -> b.getMeanSingletonCosts() != null)
                        .map(ProjectCostSummary::getMeanSingletonCosts)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).divide(number.multiply(new BigDecimal(10000)), 2, BigDecimal.ROUND_HALF_UP);
            }
            list.add(totalAmount);

        }
        map.put("平均单例成本", list);
        mapListData.add(map);
        vo.setListLegend(listLegend);
        vo.setXAxisList(xAxisList);
        vo.setMapListData(mapListData);
        System.out.println("管线对比VO----" + vo);
        return vo;
    }

    /**
     * 合同结算单价
     * 项目对比 图表
     */
    @Override
    public ProjectComparisonChartVo selectProjectComparisonChartVo(Query query) {
        ProjectComparisonChartVo vo = new ProjectComparisonChartVo();
        List<ProjectCostSummary> projectCostSummaryList = projectCostSummaryMapper.selectProjectCostSummaryList(new ProjectCostSummary());
        List<String> xAxisList = projectCostSummaryList.stream().map(ProjectCostSummary::getItemNumber).distinct().collect(Collectors.toList());
        System.out.println(xAxisList);
        List<String> listLegend = new ArrayList<>();
        listLegend.add("平均单例成本");
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();
        Map<String, List<BigDecimal>> map = new HashMap<>();

        /** 平均单例成本 集合 */
        List<BigDecimal> list = new ArrayList<>();

        ProjectCostSummary p = new ProjectCostSummary();
        List<ProjectCostSummary> costSummaryList = projectCostSummaryMapper.selectProjectCostSummaryList(p);

        for (int i = 0; i < xAxisList.size(); i++) {
            BigDecimal totalAmount = new BigDecimal(0);
            String itemNumber = xAxisList.get(i);
            if (costSummaryList.size() > 0) {
                List<ProjectCostSummary> filteredCostSummaryList = costSummaryList.stream()
                        .filter(a -> a.getItemNumber() != null)
                        .filter(a -> a.getItemNumber().equals(itemNumber))
                        .collect(Collectors.toList());
                BigDecimal number = BigDecimal.valueOf(filteredCostSummaryList.size());
                totalAmount = filteredCostSummaryList.stream().filter(b -> b.getMeanSingletonCosts() != null)
                        .map(ProjectCostSummary::getMeanSingletonCosts)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).divide(number.multiply(new BigDecimal(10000)), 2, BigDecimal.ROUND_HALF_UP);
            }
            list.add(totalAmount);

        }
        map.put("平均单例成本", list);
        mapListData.add(map);
        vo.setListLegend(listLegend);
        vo.setXAxisList(xAxisList);
        vo.setMapListData(mapListData);
        System.out.println("项目对比VO----" + vo);
        return vo;
    }

    /**
     * 合同结算单价
     * 不同项目同一类型对比 图表
     */
    @Override
    public ProjectTypeComparisonChartVo selectProjectTypeComparisonChartVo(Query query) {
        String projectCostType = query.getProjectCostType();
        ProjectTypeComparisonChartVo vo = new ProjectTypeComparisonChartVo();
        List<String> listLegend = new ArrayList<>();
        listLegend.add("结算单价");
        List<ProjectProgressSummary> list = projectProgressSummaryMapper.selectProjectProgressSummaryList(new ProjectProgressSummary());
        List<String> xAxisList = list.stream().filter(c -> c.getProject() != null && !c.getProject().equals(""))
                .map(ProjectProgressSummary::getProject).distinct().collect(Collectors.toList());
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();
        Map<String, List<BigDecimal>> map = new HashMap<>();
        /** 结算单价 集合 */
        List<BigDecimal> bigDecimalList = new ArrayList<>();
        ProjectProgressSummary p = new ProjectProgressSummary();
        p.setResearchContents(projectCostType);
        List<ProjectProgressSummary> projectProgressSummaryList = projectProgressSummaryMapper.selectProjectProgressSummaryList(p);

        for (int i = 0; i < xAxisList.size(); i++) {
            String project = xAxisList.get(i);
            BigDecimal amount = new BigDecimal(0);
            if (projectProgressSummaryList.size() > 0) {
                amount = projectProgressSummaryList.stream()
                        .filter(a -> a.getProject() != null)
                        .filter(a -> a.getProject().equals(project))
                        .map(ProjectProgressSummary::getSettlementUnitPrice).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
            }
            bigDecimalList.add(amount);
        }
        map.put("结算单价", bigDecimalList);
        mapListData.add(map);
        vo.setListLegend(listLegend);
        vo.setXAxisList(xAxisList);
        vo.setMapListData(mapListData);
        System.out.println("不同项目同一类型对比VO----" + vo);
        return vo;
    }

    /**
     * 研发费用
     * 各年度研发费用分类汇总图表
     */
    @Override
    public ResearchDevelopmentExpenseYearClassificationSummaryChartVo selectResearchDevelopmentExpenseYearClassificationSummaryChartVo(Query query) {
        ResearchDevelopmentExpenseYearClassificationSummaryChartVo vo = new ResearchDevelopmentExpenseYearClassificationSummaryChartVo();
        Integer year = query.getYear();
        String[] companyNameList = query.getCompanyName();
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyNameList));
        List<Integer> yearList = new ArrayList<>();
        for (int i = 2017; i <= year; i++) {
            yearList.add(i);
        }
        List<String> costList = new ArrayList<>();
        costList.add("材料成本");
        costList.add("人工成本");
        costList.add("运行成本");
        costList.add("折旧及摊销");
        costList.add("直接成本");
        costList.add("年度汇总");

        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();
        ResearchDevelopmenteExpenseYear r = new ResearchDevelopmenteExpenseYear();
        r.setCompanyList(companyList);
        List<ResearchDevelopmenteExpenseYear> list = researchDevelopmenteExpenseYearMapper.selectResearchDevelopmenteExpenseYearList(r);
        for (int i = 0; i < costList.size(); i++) {
            String cost = costList.get(i);
            List<BigDecimal> listData = new ArrayList<>();
            for (int j = 0; j < yearList.size(); j++) {
                Integer newYear = yearList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                BigDecimal data = new BigDecimal(10000);
                if (cost.equals("年度汇总")) {
                    if (list.size() > 0) {
                        totalAmount = list.stream()
                                .filter(a -> a.getAmount() != null)
                                .filter(a -> a.getYear() != null)
                                .filter(a -> a.getYear().equals(newYear))
                                .map(ResearchDevelopmenteExpenseYear::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add).divide(data, 2, BigDecimal.ROUND_HALF_UP);
                    }
                } else {
                    if (list.size() > 0) {
                        totalAmount = list.stream()
                                .filter(a -> a.getAmount() != null)
                                .filter(a -> a.getYear() != null)
                                .filter(a -> a.getCostType() != null)
                                .filter(a -> a.getYear().equals(newYear))
                                .filter(a -> a.getCostType().equals(cost))
                                .map(ResearchDevelopmenteExpenseYear::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add).divide(data, 2, BigDecimal.ROUND_HALF_UP);
                    }
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(cost, listData);
            mapListData.add(map);
        }

        vo.setYearList(yearList);
        vo.setCostList(costList);
        vo.setMapListData(mapListData);
        System.out.println("各年度研发费用分类汇总图表VO----" + vo);
        return vo;
    }

    /**
     * 研发费用
     * 成本分类汇总占比图表
     */
    @Override
    public CostClassificationSummaryProportionChartVo selectCostClassificationSummaryProportionChartVo(Query query) {
        CostClassificationSummaryProportionChartVo vo = new CostClassificationSummaryProportionChartVo();
        Integer year = query.getYear();
        String[] companyNameList = query.getCompanyName();
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyNameList));
        List<String> costList = new ArrayList<>();
        costList.add("材料成本");
        costList.add("人工成本");
        costList.add("运行成本");
        costList.add("折旧及摊销");
        costList.add("直接成本");
        ResearchDevelopmenteExpenseYear r = new ResearchDevelopmenteExpenseYear();
        r.setCompanyList(companyList);
        r.setYear(year);
        List<ResearchDevelopmenteExpenseYear> list = researchDevelopmenteExpenseYearMapper.selectResearchDevelopmenteExpenseYearList(r);
        List<Map<String, Object>> mapListData = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < costList.size(); i++) {
            String cost = costList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            if (list.size() > 0) {
                totalAmount = list.stream()
                        .filter(a -> a.getAmount() != null)
                        .filter(a -> a.getCostType() != null)
                        .filter(a -> a.getCostType().equals(cost))
                        .map(ResearchDevelopmenteExpenseYear::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(data, 2, BigDecimal.ROUND_HALF_UP);
            }
            map.put(cost, totalAmount);
        }
        mapListData = map.entrySet().stream().map(entry -> {
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put("name", entry.getKey());
            hashMap.put("value", entry.getValue());
            return hashMap;
        }).collect(Collectors.toList());

        vo.setCostList(costList);
        vo.setMapListData(mapListData);
        System.out.println("成本分类汇总占比图表VO-----" + vo);
        return vo;
    }

    /**
     * 研发费用
     * 各公司研发费用汇总图表
     */
    @Override
    public CompanyResearchDevelopmentExpenseSummaryChartVo selectCompanyResearchDevelopmentExpenseSummaryChartVo(Query query) {
        CompanyResearchDevelopmentExpenseSummaryChartVo vo = new CompanyResearchDevelopmentExpenseSummaryChartVo();
        Integer year = query.getYear();
        String[] companyNameList = query.getCompanyName();
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyNameList));
        ResearchDevelopmenteExpenseYear r = new ResearchDevelopmenteExpenseYear();
        r.setYear(year);
        List<ResearchDevelopmenteExpenseYear> list = researchDevelopmenteExpenseYearMapper.selectResearchDevelopmenteExpenseYearList(r);

        List<String> companyAbbreviationList = new ArrayList<>();
        List<BigDecimal> moneyList = new ArrayList<>();
        for (int i = 0; i < companyList.size(); i++) {
            String companyName = companyList.get(i);
            switch (companyName) {
                case "中山康方生物医药有限公司":
                    companyAbbreviationList.add("中山康方");
                    break;
                case "中山康方生物医药有限公司北京分公司":
                    companyAbbreviationList.add("康方上海");
                    break;
                case "中山康方生物医药有限公司上海分公司":
                    companyAbbreviationList.add("康方北京");
                    break;
                case "康方天成（广东）制药有限公司":
                    companyAbbreviationList.add("康方天成");
                    break;
                case "康方赛诺医药有限公司":
                    companyAbbreviationList.add("康方赛诺");
                    break;
                case "康方药业有限公司":
                    companyAbbreviationList.add("康方药业");
                    break;
                case "康融东方（广东）医药有限公司":
                    companyAbbreviationList.add("康融广东");
                    break;
                case "康融东方（广州）生物医药有限公司":
                    companyAbbreviationList.add("康融广州");
                    break;
                case "正大天晴康方（上海）生物医药科技有限公司":
                    companyAbbreviationList.add("康方天晴");
                    break;
                case "康方汇科（上海）生物有限公司":
                    companyAbbreviationList.add("上海汇科");
                    break;
                case "康方汇科（广州）生物有限公司":
                    companyAbbreviationList.add("广州汇科");
                    break;
                case "康方隆跃（广东）科技有限公司":
                    companyAbbreviationList.add("康方隆跃");
                    break;
                case "泽昇医药 (广东)有限公司":
                    companyAbbreviationList.add("泽昇医药");
                    break;
                case "康方中国有限公司":
                    companyAbbreviationList.add("康方中国");
                    break;
                case "康方生物医药（美国）有限公司":
                    companyAbbreviationList.add("康方美国");
                    break;
                case "康方生物医药（澳大利亚）有限公司":
                    companyAbbreviationList.add("康方澳洲");
                    break;
                case "康方生物科技（开曼）有限公司":
                    companyAbbreviationList.add("康方开曼");
                    break;
                case "AKESO (BVI), INC.":
                    companyAbbreviationList.add("BVI");
                    break;
            }
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            if (list.size() > 0) {
                totalAmount = list.stream()
                        .filter(a -> a.getCompanyName() != null)
                        .filter(a -> a.getAmount() != null)
                        .filter(a -> a.getCompanyName().equals(companyName))
                        .map(ResearchDevelopmenteExpenseYear::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(data, 2, BigDecimal.ROUND_HALF_UP);
            }
            moneyList.add(totalAmount);
        }
        vo.setCompanyList(companyAbbreviationList);
        vo.setMoneyList(moneyList);
        System.out.println("各公司研发费用汇总图表VO-----" + vo);
        return vo;
    }

    /**
     * 研发费用
     * 成本分类汇总同比图表
     */
    @Override
    public CostClassificationSummaryYOYChartVo selectCostClassificationSummaryYOYChartVo(Query query) {
        CostClassificationSummaryYOYChartVo vo = new CostClassificationSummaryYOYChartVo();
        String yearMonth = query.getYearMonth();
        String lastYearMonth = GetLastYearMonth.getLastYearSameMonth(yearMonth);

        List<String> yearMonthList = new ArrayList<>();
        yearMonthList.add(lastYearMonth);
        yearMonthList.add(yearMonth);

        String[] companyNameList = query.getCompanyName();
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyNameList));

        List<String> costList = new ArrayList<>();
        costList.add("材料成本");
        costList.add("人工成本");
        costList.add("运行成本");
        costList.add("折旧及摊销");
        costList.add("直接成本");

        ResearchDevelopmentExpenseCost r = new ResearchDevelopmentExpenseCost();
        r.setCompanyList(companyList);
        List<ResearchDevelopmentExpenseCost> list = researchDevelopmentExpenseCostMapper.selectResearchDevelopmentExpenseCostList(r);

        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        for (int i = 0; i < yearMonthList.size(); i++) {
            String yearmonth = yearMonthList.get(i);
            List<BigDecimal> listData = new ArrayList<>();
            for (int j = 0; j < costList.size(); j++) {
                String cost = costList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                BigDecimal data = new BigDecimal(10000);
                if (list.size() > 0) {
                    totalAmount = list.stream()
                            .filter(a -> a.getAmount() != null)
                            .filter(a -> a.getYearMonth() != null)
                            .filter(a -> a.getCostType() != null)
                            .filter(a -> a.getYearMonth().equals(yearmonth))
                            .filter(a -> a.getCostType().equals(cost))
                            .map(ResearchDevelopmentExpenseCost::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(yearmonth, listData);
            mapListData.add(map);
        }
        vo.setCostList(costList);
        vo.setMapListData(mapListData);
        System.out.println("成本分类汇总同比图表VO-----" + vo);
        return vo;
    }

    /**
     * 研发费用
     * 单管线、项目研发费用汇总同比 图表
     */
    @Override
    public SinglePipelineProjectSummaryYOYChartVo selectSinglePipelineProjectSummaryYOYChartVo(Query query) {
        SinglePipelineProjectSummaryYOYChartVo vo = new SinglePipelineProjectSummaryYOYChartVo();
        String yearMonth = query.getYearMonth();
        String lastYearMonth = GetLastYearMonth.getLastYearSameMonth(yearMonth);
        List<String> yearMonthList = new ArrayList<>();
        yearMonthList.add(lastYearMonth);
        yearMonthList.add(yearMonth);

        String singlePipeLine = query.getSinglePipeLine();
        String[] projectNumberList = query.getProjectNumberList();
        ArrayList<String> projectList = new ArrayList<>(Arrays.asList(projectNumberList));

        ResearchDevelopmentExpensePipeline r = new ResearchDevelopmentExpensePipeline();
        r.setDrugPipeline(singlePipeLine);
        List<ResearchDevelopmentExpensePipeline> list = researchDevelopmentExpensePipelineMapper.selectResearchDevelopmentExpensePipelineList(r);
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        for (int i = 0; i < yearMonthList.size(); i++) {
            String yearmonth = yearMonthList.get(i);
            List<BigDecimal> listData = new ArrayList<>();
            for (int j = 0; j < projectList.size(); j++) {
                String project = projectList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                BigDecimal data = new BigDecimal(10000);
                if (list.size() > 0) {
                    totalAmount = list.stream()
                            .filter(a -> a.getAmount() != null)
                            .filter(a -> a.getYearMonth() != null)
                            .filter(a -> a.getProjectNumber() != null)
                            .filter(a -> a.getYearMonth().equals(yearmonth))
                            .filter(a -> a.getProjectNumber().equals(project))
                            .map(ResearchDevelopmentExpensePipeline::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(yearmonth, listData);
            mapListData.add(map);
        }
        vo.setProjectList(projectList);
        vo.setMapListData(mapListData);
        System.out.println("单管线、项目研发费用汇总同比图表VO-----" + vo);
        return vo;
    }


    /**
     * 研发费用
     * 多管线、研发费用汇总同比 图表
     */
    @Override
    public MultiplePipelineResearchDevelopmentExpenseSummaryYOYChartVo selectMultiplePipelineResearchDevelopmentExpenseSummaryYOYChartVo(Query query) {
        MultiplePipelineResearchDevelopmentExpenseSummaryYOYChartVo vo = new MultiplePipelineResearchDevelopmentExpenseSummaryYOYChartVo();
        String yearMonth = query.getYearMonth();
        String lastYearMonth = GetLastYearMonth.getLastYearSameMonth(yearMonth);
        List<String> yearMonthList = new ArrayList<>();
        yearMonthList.add(lastYearMonth);
        yearMonthList.add(yearMonth);

        String[] multiplePipeline = query.getMultiplePipeline();

        ArrayList<String> pipelineList = new ArrayList<>(Arrays.asList(multiplePipeline));

        ResearchDevelopmentExpensePipeline r = new ResearchDevelopmentExpensePipeline();
        r.setPipelineList(pipelineList);
        List<ResearchDevelopmentExpensePipeline> list = researchDevelopmentExpensePipelineMapper.selectResearchDevelopmentExpensePipelineList(r);
        System.out.println("条数" + list.size());
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        for (int i = 0; i < yearMonthList.size(); i++) {
            String yearmonth = yearMonthList.get(i);
            List<BigDecimal> listData = new ArrayList<>();
            for (int j = 0; j < pipelineList.size(); j++) {
                String pipeline = pipelineList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                BigDecimal data = new BigDecimal(10000);
                if (list.size() > 0) {
                    totalAmount = list.stream()
                            .filter(a -> a.getAmount() != null)
                            .filter(a -> a.getYearMonth() != null)
                            .filter(a -> a.getDrugPipeline() != null)
                            .filter(a -> a.getYearMonth().equals(yearmonth))
                            .filter(a -> a.getDrugPipeline().equals(pipeline))
                            .map(ResearchDevelopmentExpensePipeline::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(yearmonth, listData);
            mapListData.add(map);
        }
        vo.setPipelineList(pipelineList);
        vo.setMapListData(mapListData);
        System.out.println("多管线、研发费用汇总同比图表VO-----" + vo);
        return vo;
    }

    /**
     * 研发费用
     * 单项目各年度成本类别 图表
     */
    @Override
    public SingleProjectYearCostChartVo selectSingleProjectYearCostChartVo(Query query) {
        SingleProjectYearCostChartVo vo = new SingleProjectYearCostChartVo();
        Integer year = query.getYear();
        List<Integer> yearList = new ArrayList<>();
        for (int i = 2017; i <= year; i++) {
            yearList.add(i);
        }
        String[] costType = query.getCostType();
        ArrayList<String> costList = new ArrayList<>(Arrays.asList(costType));
        String singleProjectNumber = query.getSingleProjectNumber();

        ResearchDevelopmentExpenseProject r = new ResearchDevelopmentExpenseProject();
        r.setProjectNumber(singleProjectNumber);
        List<ResearchDevelopmentExpenseProject> list = researchDevelopmentExpenseProjectMapper.selectResearchDevelopmentExpenseProjectList(r);

        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        for (int i = 0; i < costList.size(); i++) {
            String cost = costList.get(i);
            List<BigDecimal> listData = new ArrayList<>();
            for (int j = 0; j < yearList.size(); j++) {
                Integer newYear = yearList.get(j);
                BigDecimal totalAmount = new BigDecimal(0);
                BigDecimal data = new BigDecimal(10000);
                if (list.size() > 0) {
                    totalAmount = list.stream()
                            .filter(a -> a.getAmount() != null)
                            .filter(a -> a.getYear() != null)
                            .filter(a -> a.getCostType() != null)
                            .filter(a -> a.getYear().equals(newYear))
                            .filter(a -> a.getCostType().equals(cost))
                            .map(ResearchDevelopmentExpenseProject::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                }
                listData.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(cost, listData);
            mapListData.add(map);
        }
        vo.setYearList(yearList);
        vo.setMapListData(mapListData);
        System.out.println("单项目各年度成本类别图表VO----" + vo);
        return vo;
    }
}
