package com.akesobio.report.groupFinance.service.impl;

import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;
import com.akesobio.report.basicData.mapper.CompanyOperatingExpensesMapper;
import com.akesobio.report.groupFinance.domain.MonthlyEndingInventorySummary;
import com.akesobio.report.groupFinance.domain.MonthlyInventoryRingRatioChange;
import com.akesobio.report.groupFinance.mapper.MonthlyEndingInventorySummaryMapper;
import com.akesobio.report.groupFinance.mapper.MonthlyInventoryRingRatioChangeMapper;
import com.akesobio.report.groupFinance.service.ISupplyChainGroupChartService;
import com.akesobio.report.groupFinance.util.GetLastYearMonth;
import com.akesobio.report.groupFinance.vo.*;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 集团财务 供应链组  图表
 * Service实现层
 */
@Service
public class SupplyChainGroupChartServiceImpl implements ISupplyChainGroupChartService {

    @Autowired
    private CompanyOperatingExpensesMapper companyOperatingExpensesMapper;

    @Autowired
    private MonthlyEndingInventorySummaryMapper monthlyEndingInventorySummaryMapper;

    @Autowired
    private MonthlyInventoryRingRatioChangeMapper monthlyInventoryRingRatioChangeMapper;

    /**
     * 公司运营费用
     * 查询康方集团运营费用月度详情 图表
     */
    @Override
    public CompanyOperatingExpensesMonthChartVo selectCompanyOperatingExpensesMonthChartVo(Query query) {
        CompanyOperatingExpensesMonthChartVo vo = new CompanyOperatingExpensesMonthChartVo();
        String[] companyName = query.getCompanyName();
        String[] projectType = query.getProjectType();
        String[] monthType = query.getMonth();
        Integer year = query.getYear();
        ;
        /**companyList 公司集合 */
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyName));

        /**projectTypeList 项目类型集合 */
        ArrayList<String> projectTypeList = new ArrayList<>(Arrays.asList(projectType));
        List<String> projectList = projectTypeList;

        projectTypeList.add("合计");

        /**listMonth 月份集合 */
        ArrayList<String> listMonth = new ArrayList<>(Arrays.asList(monthType));

        /**
         * 项目类型对应金额集合
         */
        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        CompanyOperatingExpenses c = new CompanyOperatingExpenses();
        c.setYearTime(year);
        c.setProjectList(projectList);
        c.setCompanyList(companyList);
        List<CompanyOperatingExpenses> list = companyOperatingExpensesMapper.selectCompanyOperatingExpensesList(c);

        CompanyOperatingExpenses companyOperatingExpenses = new CompanyOperatingExpenses();
        companyOperatingExpenses.setYearTime(year);
        companyOperatingExpenses.setCompanyList(companyList);
        List<CompanyOperatingExpenses> expensesList = companyOperatingExpensesMapper.selectCompanyOperatingExpensesList(companyOperatingExpenses);

        for (int i = 0; i < projectTypeList.size(); i++) {
            String project = projectTypeList.get(i);
            List<BigDecimal> listMonthData = new ArrayList<>();
            for (int j = 0; j < listMonth.size(); j++) {
                String month = listMonth.get(j);
                if (project.equals("合计")) {
                    BigDecimal totalAmount = new BigDecimal(0);
                    switch (listMonth.get(j)) {
                        case "1月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getJanuary() != null).map(CompanyOperatingExpenses::getJanuary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "2月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getFebruary() != null).map(CompanyOperatingExpenses::getFebruary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "3月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getMarch() != null).map(CompanyOperatingExpenses::getMarch)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "4月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getApril() != null).map(CompanyOperatingExpenses::getApril)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "5月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getMay() != null).map(CompanyOperatingExpenses::getMay)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "6月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getJune() != null).map(CompanyOperatingExpenses::getJune)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "7月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getJuly() != null).map(CompanyOperatingExpenses::getJuly)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "8月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getAugust() != null).map(CompanyOperatingExpenses::getAugust)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "9月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getSeptember() != null).map(CompanyOperatingExpenses::getSeptember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "10月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getOctober() != null).map(CompanyOperatingExpenses::getOctober)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "11月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getNovember() != null).map(CompanyOperatingExpenses::getNovember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "12月":
                            if (list.size() > 0) {
                                totalAmount = list.stream().filter(a -> a.getDecember() != null).map(CompanyOperatingExpenses::getDecember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                    }
                } else {
                    BigDecimal totalAmount = new BigDecimal(0);
                    switch (listMonth.get(j)) {
                        case "1月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getJanuary() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJanuary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "2月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getFebruary() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getFebruary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "3月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getMarch() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getMarch)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "4月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getApril() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getApril)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "5月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getMay() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getMay)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "6月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getJune() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJune)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "7月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getJuly() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJuly)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "8月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getAugust() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getAugust)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "9月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getSeptember() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getSeptember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "10月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getOctober() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getOctober)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "11月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getNovember() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getNovember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                        case "12月":
                            if (expensesList.size() > 0) {
                                totalAmount = expensesList.stream()
                                        .filter(a -> a.getDecember() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getDecember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            listMonthData.add(totalAmount);
                            break;
                    }
                }

            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(project, listMonthData);
            mapListData.add(map);
        }


        vo.setProjectTypeList(projectTypeList);
        vo.setListMonth(listMonth);
        vo.setMapListData(mapListData);
        System.out.println("公司运营费用各月情况图表VO----" + vo);
        return vo;
    }

    /**
     * 公司运营费用
     * 查询康方集团运营费用总览  图表
     */
    @Override
    public GroupOperatingExpensesChartVo selectGroupOperatingExpensesChartVo(Query query) {
        GroupOperatingExpensesChartVo vo = new GroupOperatingExpensesChartVo();
        String[] companyName = query.getCompanyName();
        /**companyList 公司集合 */
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyName));
        String[] monthType = query.getMonth();
        /**listMonth 月份集合 */
        ArrayList<String> listMonth = new ArrayList<>(Arrays.asList(monthType));
        Integer newYear = query.getYear();
        Integer oldYear = newYear - 1;
        List<Integer> listYear = new ArrayList<>();
        listYear.add(oldYear);
        listYear.add(newYear);
        List<String> projectTypeList = new ArrayList<>();
        projectTypeList.add("天然气");
        projectTypeList.add("蒸汽");
        projectTypeList.add("电费");
        projectTypeList.add("水费");
        projectTypeList.add("饭堂费用");
        projectTypeList.add("房租及对应的物业管理费");
        projectTypeList.add("网络电信");
        List<Map<Integer, List<BigDecimal>>> mapListData = new ArrayList<>();

        CompanyOperatingExpenses c = new CompanyOperatingExpenses();
        c.setCompanyList(companyList);
        List<CompanyOperatingExpenses> list = companyOperatingExpensesMapper.selectCompanyOperatingExpensesList(c);

        for (int x = 0; x < listYear.size(); x++) {
            Integer year = listYear.get(x);
            List<BigDecimal> listData = new ArrayList<>();
            for (int i = 0; i < projectTypeList.size(); i++) {
                BigDecimal totalAmount = new BigDecimal(0);
                String project = projectTypeList.get(i);
                for (int j = 0; j < listMonth.size(); j++) {
                    String month = listMonth.get(j);
                    BigDecimal monthAmount = new BigDecimal(0);
                    switch (month) {
                        case "1月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getJanuary() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJanuary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "2月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getFebruary() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getFebruary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "3月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getMarch() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getMarch)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "4月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getApril() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getApril)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "5月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getMay() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getMay)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "6月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getJune() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJune)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "7月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getJuly() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJuly)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "8月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getAugust() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getAugust)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "9月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getSeptember() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getSeptember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "10月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getOctober() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getOctober)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "11月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getNovember() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getNovember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "12月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getDecember() != null)
                                        .filter(a -> a.getYearTime() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getYearTime().equals(year))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getDecember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                    }
                }
                listData.add(totalAmount.setScale(0, BigDecimal.ROUND_HALF_UP));
            }
            Map<Integer, List<BigDecimal>> map = new HashMap<>();
            map.put(year, listData);
            mapListData.add(map);
        }
        vo.setListData(mapListData);
        vo.setProjectTypeList(projectTypeList);
        System.out.println("集团运营费用图表VO----" + vo);
        return vo;
    }

    /**
     * 公司运营费用
     * 查询康方集团各主体运营费用  图表
     */
    @Override
    public GroupEntityOperatingExpensesChartVo selectGroupEntityOperatingExpensesChartVo(Query query) {
        GroupEntityOperatingExpensesChartVo vo = new GroupEntityOperatingExpensesChartVo();
        String companyName[] = query.getCompanyName();
        String projectType[] = query.getProjectType();
        String[] monthType = query.getMonth();
        Integer year = query.getYear();
        ArrayList<String> companyNameList = new ArrayList<>(Arrays.asList(companyName));
        ArrayList<String> projectList = new ArrayList<>(Arrays.asList(projectType));
        /**listMonth 月份集合 */
        ArrayList<String> listMonth = new ArrayList<>(Arrays.asList(monthType));
        List<String> companyList = new ArrayList<>();
        List<Map<String, List<BigDecimal>>> mapList = new ArrayList<>();
        for (int i = 0; i < companyNameList.size(); i++) {
            switch (companyNameList.get(i)) {
                case "中山康方生物医药有限公司":
                    companyList.add("中山康方");
                    break;
                case "康方天成（广东）制药有限公司":
                    companyList.add("康方天成");
                    break;
                case "康方赛诺医药有限公司":
                    companyList.add("康方赛诺");
                    break;
                case "康方药业有限公司":
                    companyList.add("康方药业");
                    break;
                case "康融东方（广东）医药有限公司":
                    companyList.add("康融广东");
                    break;
                case "康融东方（广州）生物医药有限公司":
                    companyList.add("康融广州");
                    break;
                case "康方隆跃（广东）科技有限公司":
                    companyList.add("康方隆跃");
                    break;
                case "康方添成科技（上海）有限公司":
                    companyList.add("康方添成");
                    break;
                case "康方汇科（上海）生物有限公司":
                    companyList.add("上海汇科");
                    break;
                case "康方汇科（广州）生物有限公司":
                    companyList.add("广州汇科");
                    break;
                case "康方隆跃（广东）科技有限公司-生物岛":
                    companyList.add("康方隆跃-生物岛");
                    break;
                case "康融东方（广东）医药有限公司-生物岛":
                    companyList.add("康融广东-生物岛");
                    break;
                case "康方中国有限公司":
                    companyList.add("康方中国");
                    break;
                case "泽昇医药 (广东)有限公司":
                    companyList.add("泽昇医药");
                    break;
                case "康方生物医药（澳大利亚）有限公司":
                    companyList.add("康方澳洲");
                    break;
                case "康方生物科技（开曼）有限公司":
                    companyList.add("康方开曼");
                    break;
                case "AKESO (BVI), INC":
                    companyList.add("BVI");
                    break;
            }
        }
        CompanyOperatingExpenses c = new CompanyOperatingExpenses();
        c.setYearTime(year);
        List<CompanyOperatingExpenses> list = companyOperatingExpensesMapper.selectCompanyOperatingExpensesList(c);

        for (int b = 0; b < projectList.size(); b++) {
            Map<String, List<BigDecimal>> map = new HashMap<>();
            String project = projectList.get(b);
            List<BigDecimal> listData = new ArrayList<>();
            for (int i = 0; i < companyList.size(); i++) {
                BigDecimal totalAmount = new BigDecimal(0);
                for (int j = 0; j < listMonth.size(); j++) {
                    String month = listMonth.get(j);
                    String company = companyList.get(i);

                    BigDecimal monthAmount = new BigDecimal(0);
                    switch (month) {
                        case "1月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getJanuary() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJanuary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "2月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getFebruary() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getFebruary)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "3月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getMarch() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getMarch)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "4月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getApril() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getApril)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "5月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getMay() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getMay)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "6月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getJune() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJune)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "7月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getJuly() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getJuly)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "8月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getAugust() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getAugust)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "9月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getSeptember() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getSeptember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "10月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getOctober() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getOctober)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "11月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getNovember() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getNovember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                        case "12月":
                            if (list.size() > 0) {
                                monthAmount = list.stream()
                                        .filter(a -> a.getDecember() != null)
                                        .filter(a -> a.getCompanyAbbreviation() != null)
                                        .filter(a -> a.getProject() != null)
                                        .filter(a -> a.getCompanyAbbreviation().equals(company))
                                        .filter(a -> a.getProject().equals(project))
                                        .map(CompanyOperatingExpenses::getDecember)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            totalAmount = totalAmount.add(monthAmount);
                            break;
                    }
                }
                listData.add(totalAmount.setScale(0, BigDecimal.ROUND_HALF_UP));
            }
            map.put(project, listData);
            mapList.add(map);
        }
        vo.setCompanyList(companyList);
        vo.setListMap(mapList);
        System.out.println("康方集团各主体运营费用图表VO---" + vo);
        return vo;
    }

    /**
     * 月末库存及环比变化
     * 公司月度期末库存 图形报表
     */
    @Override
    public CompanyMonthlyEndingInventoryChartVo selectCompanyMonthlyEndingInventoryChartVo(Query query) {
        CompanyMonthlyEndingInventoryChartVo vo = new CompanyMonthlyEndingInventoryChartVo();
        Integer year = query.getYear();
        String companyName[] = query.getCompanyName();
        String monthlyType[] = query.getMonthlyType();

        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyName));
        ArrayList<String> monthlyList = new ArrayList<>(Arrays.asList(monthlyType));

        List<String> subjectList = new ArrayList<>();
        subjectList.add("在途物资");
        subjectList.add("原材料-原料");
        subjectList.add("原材料-辅材");
        subjectList.add("原材料-包材");
        subjectList.add("低值耗材");
        subjectList.add("生产耗材");
        subjectList.add("外购临床药品");
        subjectList.add("临床物料");
        subjectList.add("研发物料");
        subjectList.add("备品备件");
        subjectList.add("循环使用原材料");
        subjectList.add("在制品");
        subjectList.add("半成品");
        subjectList.add("产成品");

        MonthlyEndingInventorySummary m = new MonthlyEndingInventorySummary();
        m.setCompanyList(companyList);
        m.setYear(year);
        List<MonthlyEndingInventorySummary> list = monthlyEndingInventorySummaryMapper.selectMonthlyEndingInventorySummaryList(m);

        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, BigDecimal> mapData = new HashMap<>();
        for (int i = 0; i < subjectList.size(); i++) {
            String subject = subjectList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            for (int j = 0; j < monthlyList.size(); j++) {
                String monthly = monthlyList.get(j);
                BigDecimal monthAmount = new BigDecimal(0);
                switch (monthly) {
                    case "1月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth1() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth1)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "2月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth2() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth2)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "3月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth3() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth3)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "4月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth4() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth4)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "5月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth5() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth5)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "6月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth6() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth6)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "7月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth7() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth7)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "8月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth8() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth8)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "9月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth9() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth9)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "10月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth10() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth10)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "11月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth11() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth11)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "12月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth12() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth12)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                }
            }
            mapData.put(subject, totalAmount);
        }
        mapList = mapData.entrySet().stream().map(
                entry -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("name", entry.getKey());
                    map.put("value", entry.getValue());
                    return map;
                }
        ).collect(Collectors.toList());
        vo.setMapList(mapList);
        System.out.println("公司月度期末库存图表VO---" + vo);
        return vo;
    }

    /**
     * 月末库存及环比变化
     * 公司期末库存金额汇总 图形报表
     */
    @Override
    public CompanyEndPeriodInventoryAmountSummaryChartVo selectCompanyEndPeriodInventoryAmountSummaryChartVo(Query query) {
        CompanyEndPeriodInventoryAmountSummaryChartVo vo = new CompanyEndPeriodInventoryAmountSummaryChartVo();
        Integer year = query.getYear();
        String companyName[] = query.getCompanyName();
        ArrayList<String> companyNameList = new ArrayList<>(Arrays.asList(companyName));
        List<String> companyList = new ArrayList<>();
        List<String> monthlyList = new ArrayList<>();
        monthlyList.add("1月");
        monthlyList.add("2月");
        monthlyList.add("3月");
        monthlyList.add("4月");
        monthlyList.add("5月");
        monthlyList.add("6月");
        monthlyList.add("7月");
        monthlyList.add("8月");
        monthlyList.add("9月");
        monthlyList.add("10月");
        monthlyList.add("11月");
        monthlyList.add("12月");

      /*  for (int i = 0; i < companyNameList.size(); i++) {
            switch (companyNameList.get(i)) {
                case "中山康方生物医药有限公司":
                    companyList.add("中山康方");
                    break;
                case "康方天成（广东）制药有限公司":
                    companyList.add("康方天成");
                    break;
                case "康方赛诺医药有限公司":
                    companyList.add("康方赛诺");
                    break;
                case "康方药业有限公司":
                    companyList.add("康方药业");
                    break;
                case "康融东方（广东）医药有限公司":
                    companyList.add("康融广东");
                    break;
                case "康融东方（广州）生物医药有限公司":
                    companyList.add("康融广州");
                    break;
                case "康方隆跃（广东）科技有限公司":
                    companyList.add("康方隆跃");
                    break;
                case "正大天晴康方（上海）生物医药科技有限公司":
                    companyList.add("康方天晴");
                    break;
                case "中山康方生物医药有限公司北京分公司":
                    companyList.add("康方北京");
                    break;
                case "中山康方生物医药有限公司上海分公司":
                    companyList.add("康方上海");
                    break;
                case "康方添成科技（上海）有限公司":
                    companyList.add("康方添成");
                    break;
                case "康方汇科（上海）生物有限公司":
                    companyList.add("上海汇科");
                    break;
                case "康方汇科（广州）生物有限公司":
                    companyList.add("广州汇科");
                    break;
                case "泽昇医药 (广东)有限公司":
                    companyList.add("泽昇医药");
                    break;
                case "康方中国有限公司":
                    companyList.add("康方中国");
                    break;
                case "康方生物医药（美国）有限公司":
                    companyList.add("康方美国");
                    break;
                case "康方生物医药（澳大利亚）有限公司":
                    companyList.add("康方澳洲");
                    break;
                case "康方生物科技（开曼）有限公司":
                    companyList.add("康方开曼");
                    break;
                case "AKESO (BVI), INC":
                    companyList.add("BVI");
                    break;
            }
        }*/
        MonthlyEndingInventorySummary m = new MonthlyEndingInventorySummary();
        m.setCompanyList(companyNameList);
        m.setYear(year);
        List<MonthlyEndingInventorySummary> list = monthlyEndingInventorySummaryMapper.selectMonthlyEndingInventorySummaryList(m);
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, BigDecimal> mapData = new HashMap<>();
        for (int i = 0; i < companyNameList.size(); i++) {
            String company = companyNameList.get(i);
            String companyname = "";
            switch (company) {
                case "中山康方生物医药有限公司":
                    companyname = "中山康方";
                    break;
                case "康方天成（广东）制药有限公司":
                    companyname = "康方天成";
                    break;
                case "康方赛诺医药有限公司":
                    companyname = "康方赛诺";
                    break;
                case "康方药业有限公司":
                    companyname = "康方药业";
                    break;
                case "康融东方（广东）医药有限公司":
                    companyname = "康融广东";
                    break;
                case "康融东方（广州）生物医药有限公司":
                    companyname = "康融广州";
                    break;
                case "康方隆跃（广东）科技有限公司":
                    companyname = "康方隆跃";
                    break;
                case "正大天晴康方（上海）生物医药科技有限公司":
                    companyname = "康方天晴";
                    break;
                case "中山康方生物医药有限公司北京分公司":
                    companyname = "康方北京";
                    break;
                case "中山康方生物医药有限公司上海分公司":
                    companyname = "康方上海";
                    break;
                case "康方添成科技（上海）有限公司":
                    companyname = "康方添成";
                    break;
                case "康方汇科（上海）生物有限公司":
                    companyname = "上海汇科";
                    break;
                case "康方汇科（广州）生物有限公司":
                    companyname = "广州汇科";
                    break;
                case "泽昇医药 (广东)有限公司":
                    companyname = "泽昇医药";
                    break;
                case "康方中国有限公司":
                    companyname = "康方中国";
                    break;
                case "康方生物医药（美国）有限公司":
                    companyname = "康方美国";
                    break;
                case "康方生物医药（澳大利亚）有限公司":
                    companyname = "康方澳洲";
                    break;
                case "康方生物科技（开曼）有限公司":
                    companyname = "康方开曼";
                    break;
                case "AKESO (BVI), INC":
                    companyname = "BVI";
                    break;
            }
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            for (int j = 0; j < monthlyList.size(); j++) {
                String monthly = monthlyList.get(j);
                BigDecimal monthAmount = new BigDecimal(0);
                switch (monthly) {
                    case "1月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth1() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth1)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "2月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth2() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth2)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "3月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth3() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth3)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "4月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth4() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth4)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "5月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth5() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth5)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "6月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth6() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth6)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "7月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth7() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth7)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "8月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth8() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth8)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "9月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth9() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth9)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "10月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth10() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth10)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "11月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth11() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth11)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "12月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getMonth12() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyEndingInventorySummary::getMonth12)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                }
            }
            mapData.put(companyname, totalAmount);
        }
        mapList = mapData.entrySet().stream().map(
                entry -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("name", entry.getKey());
                    map.put("value", entry.getValue());
                    return map;
                }
        ).collect(Collectors.toList());
        vo.setMapList(mapList);
        System.out.println("公司期末库存金额汇总图表VO---" + vo);
        return vo;
    }

    /**
     * 月末库存及环比变化
     * 公司前五大期末库存 图形报表
     */
    @Override
    public Top5CompanyEndPeriodInventoryChartVo selectTop5CompanyEndPeriodInventoryChartVo(Query query) {
        Top5CompanyEndPeriodInventoryChartVo vo = new Top5CompanyEndPeriodInventoryChartVo();
        Integer year = query.getYear();
        String companyName[] = query.getCompanyName();
        String monthlyType[] = query.getMonthlyType();

        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyName));
        ArrayList<String> monthlyList = new ArrayList<>(Arrays.asList(monthlyType));

        MonthlyEndingInventorySummary m = new MonthlyEndingInventorySummary();
        m.setCompanyList(companyList);
        m.setYear(year);
        List<MonthlyEndingInventorySummary> list = monthlyEndingInventorySummaryMapper.selectMonthlyEndingInventorySummaryList(m);

       /* List<Map<String, Object>> companyListMap = new ArrayList<>();
        Map<String, BigDecimal> companyMap = new HashMap<>();
        companyListMap = companyMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .limit(5)
                .map(entry -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("name", entry.getKey());
                    map.put("value", entry.getValue());
                    return map;
                }).collect(Collectors.toList());
        List<String> legendList = companyListMap.stream().map(a -> (String) a.get("name")).collect(Collectors.toList());
        legendList.removeIf(item -> item == null || item.isEmpty());

        legendList.add("在途物资");
        legendList.add("原材料-原料");
        legendList.add("原材料-辅材");
        legendList.add("原材料-包材");
        legendList.add("低值耗材");
        legendList.add("生产耗材");
        legendList.add("外购临床药品");
        legendList.add("临床物料");
        legendList.add("研发物料");
        legendList.add("备品备件");
        legendList.add("循环使用原材料");
        legendList.add("在制品");
        legendList.add("半成品");
        legendList.add("产成品");*/

        List<String> subjectList = new ArrayList<>();
        subjectList.add("在途物资");
        subjectList.add("原材料-原料");
        subjectList.add("原材料-辅材");
        subjectList.add("原材料-包材");
        subjectList.add("低值耗材");
        subjectList.add("生产耗材");
        subjectList.add("外购临床药品");
        subjectList.add("临床物料");
        subjectList.add("研发物料");
        subjectList.add("备品备件");
        subjectList.add("循环使用原材料");
        subjectList.add("在制品");
        subjectList.add("半成品");
        subjectList.add("产成品");

        List<Map<String, Object>> subjectListMap = new ArrayList<>();

        Map<String, BigDecimal> subjectMap = new HashMap<>();

        for (int i = 0; i < subjectList.size(); i++) {
            String subject = subjectList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            for (int j = 0; j < monthlyList.size(); j++) {
                String monthly = monthlyList.get(j);
                BigDecimal monthAmount = new BigDecimal(0);
                switch (monthly) {
                    case "1月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth1() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth1)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "2月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth2() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth2)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "3月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth3() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth3)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "4月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth4() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth4)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "5月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth5() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth5)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "6月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth6() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth6)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "7月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth7() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth7)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "8月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth8() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth8)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "9月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth9() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth9)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "10月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth10() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth10)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "11月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth11() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth11)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                    case "12月":
                        if (list.size() > 0) {
                            monthAmount = list.stream()
                                    .filter(a -> a.getSubject() != null)
                                    .filter(a -> a.getMonth12() != null)
                                    .filter(a -> a.getSubject().equals(subject))
                                    .map(MonthlyEndingInventorySummary::getMonth12)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(monthAmount);
                        }
                        break;
                }
            }
            subjectMap.put(subject, totalAmount);
        }

        subjectListMap = subjectMap.entrySet().stream().map(
                entry -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("name", entry.getKey());
                    map.put("value", entry.getValue());
                    return map;
                }
        ).collect(Collectors.toList());

        List<Map<String, Object>> dataListMap = new ArrayList<>();
        Map<String, Object> dataMap = new HashMap<>();

        dataListMap = subjectMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .limit(5)
                .map(entry -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("name", entry.getKey());
                    map.put("value", entry.getValue());
                    return map;
                }).collect(Collectors.toList());

        BigDecimal otherSum = subjectListMap.stream()
                .skip(5)
                .map(item -> (BigDecimal) item.get("value"))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        dataMap.put("name", "其他");
        dataMap.put("value", otherSum);
        dataListMap.add(dataMap);

        List<Map<String, Object>> otherListMap = subjectMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .skip(5) // 跳过前5个元素
                .map(entry -> {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put(entry.getKey(), entry.getValue());
                    return map;
                })
                .collect(Collectors.toList());

       /* // 将list中的maps合并到一个map中
        Map<String, Object> legendFalseMap = legendFalseList.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (value1, value2) -> value1 // 如果有重复的键，保留第一个值，但这应该不会发生
                ));
*/
        vo.setSubjectListMap(dataListMap);
        vo.setOtherListMap(otherListMap);
        System.out.println("公司前五大期末库存图表VO---" + vo);
        return vo;
    }


    /**
     * 月末库存及环比变化
     * 公司月度期末库存环比变动 图形报表
     */
    @Override
    public CompanyMonthlyEndingInventoryQOQChartVo selectCompanyMonthlyEndingInventoryQOQChartVo(Query query) {
        CompanyMonthlyEndingInventoryQOQChartVo vo = new CompanyMonthlyEndingInventoryQOQChartVo();
        Integer year = query.getYear();
        String companyName[] = query.getCompanyName();
        String subjectType[] = query.getSubjectType();
        ArrayList<String> companyNameList = new ArrayList<>(Arrays.asList(companyName));
        ArrayList<String> subjectList = new ArrayList<>(Arrays.asList(subjectType));
        List<String> companyList = new ArrayList<>();

        List<String> monthlyList = new ArrayList<>();
        monthlyList.add("12月");
        monthlyList.add("11月");
        monthlyList.add("10月");
        monthlyList.add("9月");
        monthlyList.add("8月");
        monthlyList.add("7月");
        monthlyList.add("6月");
        monthlyList.add("5月");
        monthlyList.add("4月");
        monthlyList.add("3月");
        monthlyList.add("2月");
        monthlyList.add("1月");

        MonthlyInventoryRingRatioChange m = new MonthlyInventoryRingRatioChange();
        m.setYear(year);
        m.setCompanyList(companyNameList);
        m.setSubjectList(subjectList);
        List<MonthlyInventoryRingRatioChange> list = monthlyInventoryRingRatioChangeMapper.selectMonthlyInventoryRingRatioChangeList(m);

        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        for (int i = 0; i < companyNameList.size(); i++) {
            String company = companyNameList.get(i);
            String companyname = "";
            switch (company) {
                case "中山康方生物医药有限公司":
                    companyname = "中山康方";
                    companyList.add(companyname);
                    break;
                case "康方天成（广东）制药有限公司":
                    companyname = "康方天成";
                    companyList.add(companyname);
                    break;
                case "康方赛诺医药有限公司":
                    companyname = "康方赛诺";
                    companyList.add(companyname);
                    break;
                case "康方药业有限公司":
                    companyname = "康方药业";
                    companyList.add(companyname);
                    break;
                case "康融东方（广东）医药有限公司":
                    companyname = "康融广东";
                    companyList.add(companyname);
                    break;
                case "康融东方（广州）生物医药有限公司":
                    companyname = "康融广州";
                    companyList.add(companyname);
                    break;
                case "康方隆跃（广东）科技有限公司":
                    companyname = "康方隆跃";
                    companyList.add(companyname);
                    break;
                case "正大天晴康方（上海）生物医药科技有限公司":
                    companyname = "康方天晴";
                    companyList.add(companyname);
                    break;
                case "中山康方生物医药有限公司北京分公司":
                    companyname = "康方北京";
                    companyList.add(companyname);
                    break;
                case "中山康方生物医药有限公司上海分公司":
                    companyname = "康方上海";
                    companyList.add(companyname);
                    break;
                case "康方添成科技（上海）有限公司":
                    companyname = "康方添成";
                    companyList.add(companyname);
                    break;
                case "康方汇科（上海）生物有限公司":
                    companyname = "上海汇科";
                    companyList.add(companyname);
                    break;
                case "康方汇科（广州）生物有限公司":
                    companyname = "广州汇科";
                    companyList.add(companyname);
                    break;
                case "泽昇医药 (广东)有限公司":
                    companyname = "泽昇医药";
                    companyList.add(companyname);
                    break;
                case "康方中国有限公司":
                    companyname = "康方中国";
                    companyList.add(companyname);
                    break;
                case "康方生物医药（美国）有限公司":
                    companyname = "康方美国";
                    companyList.add(companyname);
                    break;
                case "康方生物医药（澳大利亚）有限公司":
                    companyname = "康方澳洲";
                    companyList.add(companyname);
                    break;
                case "康方生物科技（开曼）有限公司":
                    companyname = "康方开曼";
                    companyList.add(companyname);
                    break;
                case "AKESO (BVI), INC":
                    companyname = "BVI";
                    companyList.add(companyname);
                    break;
            }
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            List<BigDecimal> decimalList = new ArrayList<>();
            for (int j = 0; j < monthlyList.size(); j++) {
                String monthly = monthlyList.get(j);
                switch (monthly) {
                    case "1月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth1() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth1)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "2月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth2() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth2)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "3月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth3() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth3)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "4月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth4() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth4)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "5月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth5() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth5)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "6月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth6() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth6)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "7月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth7() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth7)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "8月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth8() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth8)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "9月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth9() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth9)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "10月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth10() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth10)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "11月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth11() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth11)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "12月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth12() != null)
                                    .filter(a -> a.getCompanyName().equals(company))
                                    .map(MonthlyInventoryRingRatioChange::getMonth12)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                }
                decimalList.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(companyname, decimalList);
            mapListData.add(map);
        }
        vo.setListLegend(companyList);
        vo.setMonthlyList(monthlyList);
        vo.setMapListData(mapListData);
        System.out.println("公司月度期末库存环比变动图表VO---" + vo);
        return vo;
    }

    /**
     * 月末库存及环比变化
     * 库存年度对比 图形报表
     */
    @Override
    public InventoryYearContrastChartVo selectInventoryYearContrastChartVo(Query query) {
        InventoryYearContrastChartVo vo = new InventoryYearContrastChartVo();
        List<Integer> yearList = new ArrayList<>();
        Integer yearNew = query.getYear();
        Integer yearOld = yearNew - 1;
        yearList.add(yearOld);
        yearList.add(yearNew);

        String[] companyName = query.getCompanyName();
        /**companyList 公司集合 */
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyName));

        List<String> monthlyList = new ArrayList<>();
        monthlyList.add("1月");
        monthlyList.add("2月");
        monthlyList.add("3月");
        monthlyList.add("4月");
        monthlyList.add("5月");
        monthlyList.add("6月");
        monthlyList.add("7月");
        monthlyList.add("8月");
        monthlyList.add("9月");
        monthlyList.add("10月");
        monthlyList.add("11月");
        monthlyList.add("12月");

        MonthlyEndingInventorySummary m = new MonthlyEndingInventorySummary();
        m.setCompanyList(companyList);
        List<MonthlyEndingInventorySummary> list = monthlyEndingInventorySummaryMapper.selectMonthlyEndingInventorySummaryList(m);

        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        for (int i = 0; i < yearList.size(); i++) {
            Integer year = yearList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            List<BigDecimal> decimalList = new ArrayList<>();
            for (int j = 0; j < monthlyList.size(); j++) {
                String monthly = monthlyList.get(j);
                switch (monthly) {
                    case "1月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getMonth1() != null)
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth1)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "2月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth2() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth2)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "3月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth3() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth3)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "4月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth4() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth4)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "5月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth5() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth5)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "6月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth6() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth6)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "7月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth7() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth7)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "8月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth8() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth8)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "9月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth9() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth9)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "10月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth10() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth10)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "11月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth11() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth11)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                    case "12月":
                        if (list.size() > 0) {
                            totalAmount = list.stream()
                                    .filter(a -> a.getYear() != null)
                                    .filter(a -> a.getMonth12() != null)
                                    .filter(a -> a.getYear().equals(year))
                                    .map(MonthlyEndingInventorySummary::getMonth12)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                        }
                        break;
                }
                decimalList.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(year.toString(), decimalList);
            mapListData.add(map);
        }
        vo.setMonthlyList(monthlyList);
        vo.setMapListData(mapListData);
        System.out.println("库存年度对比图表VO---" + vo);
        return vo;
    }


    /**
     * 月末库存及环比变化
     * 康方集团近12个月期末库存  图形报表
     */
    @Override
    public NearTwelveMonthEndingInventoryChartVo selectNearTwelveMonthEndingInventoryChartVo(Query query) {
        NearTwelveMonthEndingInventoryChartVo vo = new NearTwelveMonthEndingInventoryChartVo();
        String yearMonth = query.getYearMonth();
        List<String> yearMonthList = GetLastYearMonth.getMonthList(yearMonth);

        String[] companyName = query.getCompanyName();
        /**companyList 公司集合 */
        ArrayList<String> companyList = new ArrayList<>(Arrays.asList(companyName));

        List<String> subjectTypeList = new ArrayList<>();
        subjectTypeList.add("外购");
        subjectTypeList.add("自制成品半成品");
        subjectTypeList.add("期末库存");

        MonthlyEndingInventorySummary m = new MonthlyEndingInventorySummary();
        m.setCompanyList(companyList);
        List<MonthlyEndingInventorySummary> list = monthlyEndingInventorySummaryMapper.selectMonthlyEndingInventorySummaryList(m);

        List<Map<String, List<BigDecimal>>> mapListData = new ArrayList<>();

        List<String> list1 = new ArrayList<>();
        list1.add("在途物资");
        list1.add("原材料-原料");
        list1.add("原材料-辅材");
        list1.add("原材料-包材");
        list1.add("低值耗材");
        list1.add("生产耗材");
        list1.add("外购临床药品");
        list1.add("临床物料");
        list1.add("研发物料");
        list1.add("备品备件");
        list1.add("循环使用原材料");

        List<String> list2 = new ArrayList<>();
        list2.add("在制品");
        list2.add("半成品");
        list2.add("产成品");

        for (int i = 0; i < subjectTypeList.size(); i++) {
            String subjectType = subjectTypeList.get(i);
            BigDecimal totalAmount = new BigDecimal(0);
            BigDecimal data = new BigDecimal(10000);
            List<BigDecimal> decimalList = new ArrayList<>();
            for (int j = 0; j < yearMonthList.size(); j++) {
                String monthly = yearMonthList.get(j);
                int year = Integer.parseInt(monthly.substring(0, 4)); // 提取年份
                int month = Integer.parseInt(monthly.substring(5, 7)); // 提取月份
                switch (month) {
                    case 1:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth1() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth1)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth1() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth1)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth1() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth1)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 2:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth2() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth2)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth2() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth2)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth2() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth2)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 3:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth3() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth3)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth3() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth3)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth3() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth3)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 4:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth4() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth4)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth4() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth4)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth4() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth4)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 5:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth5() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth5)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth5() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth5)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth5() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth5)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 6:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth6() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth6)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth6() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth6)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth6() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth6)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 7:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth7() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth7)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth7() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth7)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth7() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth7)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 8:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth8() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth8)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth8() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth8)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth8() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth8)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 9:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth9() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth9)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth9() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth9)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth9() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth9)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 10:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth10() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth10)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth10() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth10)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth10() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth10)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 11:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth11() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth11)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth11() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth11)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth11() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth11)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                    case 12:
                        if (list.size() > 0) {
                            if (subjectType.equals("外购")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth12() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list1.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth12)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("自制成品半成品")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getMonth12() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .filter(a -> list2.contains(a.getSubject()))
                                        .map(MonthlyEndingInventorySummary::getMonth12)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);

                            }
                            if (subjectType.equals("期末库存")) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getMonth12() != null)
                                        .filter(a -> a.getYear().equals(year))
                                        .map(MonthlyEndingInventorySummary::getMonth12)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .divide(data, 0, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                        break;
                }
                decimalList.add(totalAmount);
            }
            Map<String, List<BigDecimal>> map = new HashMap<>();
            map.put(subjectType, decimalList);
            mapListData.add(map);
        }

        vo.setYearMonthList(yearMonthList);
        vo.setSubjectTypeList(subjectTypeList);
        vo.setMapListData(mapListData);
        System.out.println("康方集团近12个月期末库存图表VO---" + vo);
        return vo;
    }
}
