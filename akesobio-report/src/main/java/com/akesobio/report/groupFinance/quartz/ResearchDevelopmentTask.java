package com.akesobio.report.groupFinance.quartz;


import com.akesobio.report.ddi.util.GetComputerName;
import com.akesobio.report.groupFinance.domain.*;
import com.akesobio.report.groupFinance.mapper.*;
import com.akesobio.report.groupFinance.service.IResearchDevelopmentExpenseCostService;
import com.akesobio.report.groupFinance.service.IResearchDevelopmenteExpenseYearService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 研发费用
 * 定时更新接口
 */
@Component("ResearchDevelopmentTask")
@Slf4j
public class ResearchDevelopmentTask {

    @Autowired
    private IResearchDevelopmenteExpenseYearService researchDevelopmenteExpenseYearService;

    @Autowired
    private ResearchDevelopmenteExpenseYearMapper researchDevelopmenteExpenseYearMapper;

    @Autowired
    private ResearchDevelopmentExpenseSummaryMapper researchDevelopmentExpenseSummaryMapper;

    @Autowired
    private ResearchDevelopmentExpenseCostMapper researchDevelopmentExpenseCostMapper;

    @Autowired
    private IResearchDevelopmentExpenseCostService researchDevelopmentExpenseCostService;

    @Autowired
    private ResearchDevelopmentExpensePipelineMapper researchDevelopmentExpensePipelineMapper;

    @Autowired
    private ResearchDevelopmentExpenseProjectMapper researchDevelopmentExpenseProjectMapper;

    /**
     * 定时更新 各年度研发费用分类汇总表
     */
    public void researchDevelopmenteExpenseYearTask() throws Exception {
        String computerName = GetComputerName.getComputerName();
        System.out.println("计算机名称：" + computerName);
        if (computerName.equals("SERVER22")) {
            researchDevelopmenteExpenseYearMapper.deleteAllResearchDevelopmenteExpenseYear();
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            List<Integer> yearList = new ArrayList<>();
            for (int i = 2017; i <= year; i++) {
                yearList.add(i);
            }
            List<String> costList = new ArrayList<>();
            costList.add("材料成本");
            costList.add("人工成本");
            costList.add("运行成本");
            costList.add("折旧及摊销");
            costList.add("直接成本");

            List<String> companyList = new ArrayList<>();
            companyList.add("中山康方生物医药有限公司");
            companyList.add("中山康方生物医药有限公司北京分公司");
            companyList.add("中山康方生物医药有限公司上海分公司");
            companyList.add("康方天成（广东）制药有限公司");
            companyList.add("康方赛诺医药有限公司");
            companyList.add("康方药业有限公司");
            companyList.add("康融东方（广东）医药有限公司");
            companyList.add("康融东方（广州）生物医药有限公司");
            companyList.add("正大天晴康方（上海）生物医药科技有限公司");
            companyList.add("康方汇科（上海）生物有限公司");
            companyList.add("康方汇科（广州）生物有限公司");
            companyList.add("康方隆跃（广东）科技有限公司");
            companyList.add("泽昇医药 (广东)有限公司");
            companyList.add("康方中国有限公司");
            companyList.add("康方生物医药（美国）有限公司");
            companyList.add("康方生物医药（澳大利亚）有限公司");
            companyList.add("康方生物科技（开曼）有限公司");
            companyList.add("AKESO (BVI), INC.");

            ResearchDevelopmentExpenseSummary r = new ResearchDevelopmentExpenseSummary();
            List<ResearchDevelopmentExpenseSummary> list = researchDevelopmentExpenseSummaryMapper.selectResearchDevelopmentExpenseSummaryList(r);

            int successNum = 0;
            int failureNum = 0;

            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            for (int i = 0; i < yearList.size(); i++) {
                Integer newYear = yearList.get(i);
                for (int j = 0; j < companyList.size(); j++) {
                    String companyName = companyList.get(j);
                    for (int k = 0; k < costList.size(); k++) {
                        try {
                            String cost = costList.get(k);
                            BigDecimal totalAmount = new BigDecimal(0);
                            if (list.size() > 0) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getAmount() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getCompanyName() != null)
                                        .filter(a -> a.getCostType() != null)
                                        .filter(a -> a.getYear().equals(newYear))
                                        .filter(a -> a.getCompanyName().equals(companyName))
                                        .filter(a -> a.getCostType().equals(cost))
                                        .map(ResearchDevelopmentExpenseSummary::getAmount)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            ResearchDevelopmenteExpenseYear rd = new ResearchDevelopmenteExpenseYear();
                            rd.setYear(newYear);
                            rd.setCompanyName(companyName);
                            rd.setAmount(totalAmount);
                            rd.setCostType(cost);
                            int result = researchDevelopmenteExpenseYearService.insertResearchDevelopmenteExpenseYear(rd);
                            if (result == 1) {
                                successNum++;
                                successMsg.append("<br/>" + successNum + "、年度 " + newYear + "、公司名称 " + companyName + "、成本中心 " + cost + " 添加成功");
                            } else {
                                failureNum++;
                                failureMsg.append("<br/>" + successNum + "、年度 " + newYear + "、公司名称 " + companyName + "、成本中心 " + cost + " 添加失败");
                            }
                        } catch (Exception e) {
                            failureNum++;
                            String msg = "<br/>" + successNum + "、年度 " + newYear + "、公司名称 " + companyName + "、成本中心 " + costList.get(k) + " 添加失败：";
                            failureMsg.append(msg + e.getMessage());
                        }
                    }
                }
            }
            log.info("新增成功：" + successNum + "条--" + successMsg.toString());
            log.error("新增失败：--" + failureNum + "条--" + failureMsg.toString());
        } else {
            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--定时更新各年度研发费用分类汇总表--不在**********服务器执行的定时任务--跳过");
        }
    }

    /**
     * 定时更新 研发费用成本分类汇总表
     */
    public void researchDevelopmenteExpenseCostTask() throws Exception {
        String computerName = GetComputerName.getComputerName();
        System.out.println("计算机名称：" + computerName);
        if (computerName.equals("SERVER22")) {
            researchDevelopmentExpenseCostMapper.deleteAllResearchDevelopmenteExpenseCost();
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            List<Integer> yearList = new ArrayList<>();
            for (int i = 2017; i <= year; i++) {
                yearList.add(i);
            }

            List<String> monthList = new ArrayList<>();
            for (int i = 1; i <= 12; i++) {
                if (i < 10) {
                    monthList.add("0" + i);
                } else {
                    monthList.add(String.valueOf(i));
                }
            }


            List<String> costList = new ArrayList<>();
            costList.add("材料成本");
            costList.add("人工成本");
            costList.add("运行成本");
            costList.add("折旧及摊销");
            costList.add("直接成本");

            List<String> companyList = new ArrayList<>();
            companyList.add("中山康方生物医药有限公司");
            companyList.add("中山康方生物医药有限公司北京分公司");
            companyList.add("中山康方生物医药有限公司上海分公司");
            companyList.add("康方天成（广东）制药有限公司");
            companyList.add("康方赛诺医药有限公司");
            companyList.add("康方药业有限公司");
            companyList.add("康融东方（广东）医药有限公司");
            companyList.add("康融东方（广州）生物医药有限公司");
            companyList.add("正大天晴康方（上海）生物医药科技有限公司");
            companyList.add("康方汇科（上海）生物有限公司");
            companyList.add("康方汇科（广州）生物有限公司");
            companyList.add("康方隆跃（广东）科技有限公司");
            companyList.add("泽昇医药 (广东)有限公司");
            companyList.add("康方中国有限公司");
            companyList.add("康方生物医药（美国）有限公司");
            companyList.add("康方生物医药（澳大利亚）有限公司");
            companyList.add("康方生物科技（开曼）有限公司");
            companyList.add("AKESO (BVI), INC.");

            ResearchDevelopmentExpenseSummary r = new ResearchDevelopmentExpenseSummary();
            List<ResearchDevelopmentExpenseSummary> list = researchDevelopmentExpenseSummaryMapper.selectResearchDevelopmentExpenseSummaryList(r);

            int successNum = 0;
            int failureNum = 0;

            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            for (int i = 0; i < yearList.size(); i++) {
                Integer newYear = yearList.get(i);
                for (int x = 0; x < monthList.size(); x++) {
                    String month = monthList.get(x);
                    for (int j = 0; j < companyList.size(); j++) {
                        String companyName = companyList.get(j);
                        for (int k = 0; k < costList.size(); k++) {
                            try {
                                String cost = costList.get(k);
                                BigDecimal totalAmount = new BigDecimal(0);
                                List<String> monthlylist = new ArrayList<>();
                                if (month.equals(12)) {
                                    monthlylist.add("12");
                                    monthlylist.add("13");
                                } else {
                                    monthlylist.add(month);
                                }

                                if (list.size() > 0) {
                                    totalAmount = list.stream()
                                            .filter(a -> a.getAmount() != null)
                                            .filter(a -> a.getYear() != null)
                                            .filter(a -> a.getMonth() != null)
                                            .filter(a -> a.getCompanyName() != null)
                                            .filter(a -> a.getCostType() != null)
                                            .filter(a -> a.getYear().equals(newYear))
                                            .filter(a -> monthlylist.contains(a.getMonth()))
                                            .filter(a -> a.getCompanyName().equals(companyName))
                                            .filter(a -> a.getCostType().equals(cost))
                                            .map(ResearchDevelopmentExpenseSummary::getAmount)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                }
                                String yearMonth = newYear + "-" + month;
                                ResearchDevelopmentExpenseCost rd = new ResearchDevelopmentExpenseCost();
                                rd.setYear(newYear);
                                rd.setMonth(month);
                                rd.setYearMonth(yearMonth);
                                rd.setCompanyName(companyName);
                                rd.setAmount(totalAmount);
                                rd.setCostType(cost);
                                int result = researchDevelopmentExpenseCostService.insertResearchDevelopmentExpenseCost(rd);
                                if (result == 1) {
                                    successNum++;
                                    successMsg.append("<br/>" + successNum + "、年月 " + newYear + month + "、公司名称 " + companyName + "、成本中心 " + cost + " 添加成功");
                                } else {
                                    failureNum++;
                                    failureMsg.append("<br/>" + successNum + "、年月 " + newYear + month + "、公司名称 " + companyName + "、成本中心 " + cost + " 添加失败");
                                }
                            } catch (Exception e) {
                                failureNum++;
                                String msg = "<br/>" + successNum + "、年月 " + newYear + month + "、公司名称 " + companyName + "、成本中心 " + costList.get(k) + " 添加失败：";
                                failureMsg.append(msg + e.getMessage());
                            }
                        }
                    }
                }
            }
            log.info("新增成功：" + successNum + "条--" + successMsg.toString());
            log.error("新增失败：--" + failureNum + "条--" + failureMsg.toString());
        } else {
            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--定时更新研发费用成本分类汇总表--不在**********服务器执行的定时任务--跳过");
        }
    }

    /**
     * 定时更新 研究费用管线项目汇总表
     */
    public void researchDevelopmenteExpensePipelineTask() throws Exception {
        String computerName = GetComputerName.getComputerName();
        System.out.println("计算机名称：" + computerName);
        if (computerName.equals("SERVER22")) {
            researchDevelopmentExpensePipelineMapper.deleteAllResearchDevelopmentExpensePipeline();
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            List<Integer> yearList = new ArrayList<>();
            for (int i = 2017; i <= year; i++) {
                yearList.add(i);
            }

            List<String> monthList = new ArrayList<>();
            for (int i = 1; i <= 12; i++) {
                if (i < 10) {
                    monthList.add("0" + i);
                } else {
                    monthList.add(String.valueOf(i));
                }
            }


            List<String> pipelineList = new ArrayList<>();
            pipelineList.add("AK101");
            pipelineList.add("AK102");
            pipelineList.add("AK103");
            pipelineList.add("AK104");
            pipelineList.add("AK104-IIS");
            pipelineList.add("AK104-IIT");
            pipelineList.add("AK104-IIT-C");
            pipelineList.add("AK104-RC48");
            pipelineList.add("AK104ZY");
            pipelineList.add("AK105");
            pipelineList.add("AK106");
            pipelineList.add("AK107");
            pipelineList.add("AK109");
            pipelineList.add("AK111");
            pipelineList.add("AK112");
            pipelineList.add("AK112-3003");
            pipelineList.add("SM112-301");
            pipelineList.add("AK112-IIT");
            pipelineList.add("AK112-IIT-C");
            pipelineList.add("AK112ZY");
            pipelineList.add("AK114");
            pipelineList.add("AK115");
            pipelineList.add("KF项目");
            pipelineList.add("AK117");
            pipelineList.add("AK117ZY");
            pipelineList.add("AK119");
            pipelineList.add("AK120");
            pipelineList.add("AK127");
            pipelineList.add("AK127ZY");
            pipelineList.add("AK129");
            pipelineList.add("AK130");
            pipelineList.add("AK131");
            pipelineList.add("AK132");
            pipelineList.add("AK140N-IIT");
            pipelineList.add("CT");
            pipelineList.add("SPH4336-201");


            ResearchDevelopmentExpenseSummary r = new ResearchDevelopmentExpenseSummary();
            List<ResearchDevelopmentExpenseSummary> list = researchDevelopmentExpenseSummaryMapper.selectResearchDevelopmentExpenseSummaryList(r);

            int successNum = 0;
            int failureNum = 0;

            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            for (int i = 0; i < yearList.size(); i++) {
                Integer newYear = yearList.get(i);
                for (int x = 0; x < monthList.size(); x++) {
                    String month = monthList.get(x);
                    for (int j = 0; j < pipelineList.size(); j++) {
                        String pipeline = pipelineList.get(j);
                        List<String> projectNumberList = new ArrayList<>();
                        if (pipeline.equals("AK101")) {
                            projectNumberList.add("AK101");
                            projectNumberList.add("AK101-101");
                            projectNumberList.add("AK101-102");
                            projectNumberList.add("AK101-103");
                            projectNumberList.add("AK101-201");
                            projectNumberList.add("AK101-301");
                            projectNumberList.add("AK101-302");
                            projectNumberList.add("AK101-303");
                            projectNumberList.add("KF003");
                        } else if (pipeline.equals("AK102")) {
                            projectNumberList.add("AK102");
                            projectNumberList.add("AK102-101");
                            projectNumberList.add("AK102-201");
                            projectNumberList.add("AK102-202");
                            projectNumberList.add("AK102-203");
                            projectNumberList.add("AK102-204");
                            projectNumberList.add("AK102-301");
                            projectNumberList.add("AK102-302");
                            projectNumberList.add("AK102-303");
                            projectNumberList.add("KF011");
                        } else if (pipeline.equals("AK103")) {
                            projectNumberList.add("AK103");
                            projectNumberList.add("KF007");
                        } else if (pipeline.equals("AK104")) {
                            projectNumberList.add("AK104");
                            projectNumberList.add("AK104-101");
                            projectNumberList.add("AK104-102");
                            projectNumberList.add("AK104-201");
                            projectNumberList.add("AK104-201-AU");
                            projectNumberList.add("AK104-201A");
                            projectNumberList.add("AK104-201B");
                            projectNumberList.add("AK104-202");
                            projectNumberList.add("AK104-203");
                            projectNumberList.add("AK104-204");
                            projectNumberList.add("AK104-205");
                            projectNumberList.add("AK104-206");
                            projectNumberList.add("AK104-207");
                            projectNumberList.add("AK104-208");
                            projectNumberList.add("AK104-209");
                            projectNumberList.add("AK104-210");
                            projectNumberList.add("AK104-211");
                            projectNumberList.add("AK104-212");
                            projectNumberList.add("AK104-213");
                            projectNumberList.add("AK104-214");
                            projectNumberList.add("AK104-215");
                            projectNumberList.add("AK104-216");
                            projectNumberList.add("AK104-217");
                            projectNumberList.add("AK104-218");
                            projectNumberList.add("AK104-219");
                            projectNumberList.add("AK104-220");
                            projectNumberList.add("AK104-221");
                            projectNumberList.add("AK104-222");
                            projectNumberList.add("AK104-301");
                            projectNumberList.add("AK104-302");
                            projectNumberList.add("AK104-303");
                            projectNumberList.add("AK104-304");
                            projectNumberList.add("AK104-305");
                            projectNumberList.add("AK104-306");
                            projectNumberList.add("AK104-307");
                            projectNumberList.add("AK104-308");
                            projectNumberList.add("AK104-309");
                            projectNumberList.add("AK104-SAS");
                            projectNumberList.add("KF030");
                        } else if (pipeline.equals("AK104-IIS")) {
                            projectNumberList.add("AK104-IIS-001(澳洲)");
                            projectNumberList.add("AK104-IIS-002(澳洲)");
                        } else if (pipeline.equals("AK104-IIT")) {
                            projectNumberList.add("AK104-IIT-001");
                            projectNumberList.add("AK104-IIT-002");
                            projectNumberList.add("AK104-IIT-003");
                            projectNumberList.add("AK104-IIT-004");
                            projectNumberList.add("AK104-IIT-005");
                            projectNumberList.add("AK104-IIT-006");
                            projectNumberList.add("AK104-IIT-007");
                            projectNumberList.add("AK104-IIT-008");
                            projectNumberList.add("AK104-IIT-009");
                            projectNumberList.add("AK104-IIT-010");
                            projectNumberList.add("AK104-IIT-011");
                            projectNumberList.add("AK104-IIT-012");
                            projectNumberList.add("AK104-IIT-013");
                            projectNumberList.add("AK104-IIT-014");
                            projectNumberList.add("AK104-IIT-015");
                            projectNumberList.add("AK104-IIT-016");
                            projectNumberList.add("AK104-IIT-017");
                            projectNumberList.add("AK104-IIT-018");
                            projectNumberList.add("AK104-IIT-019");
                            projectNumberList.add("AK104-IIT-020");
                            projectNumberList.add("AK104-IIT-021");
                            projectNumberList.add("AK104-IIT-024");
                            projectNumberList.add("AK104-IIT-025");
                            projectNumberList.add("AK104-IIT-026");
                            projectNumberList.add("AK104-IIT-027");
                            projectNumberList.add("AK104-IIT-028");
                            projectNumberList.add("AK104-IIT-029");
                            projectNumberList.add("AK104-IIT-030");
                            projectNumberList.add("AK104-IIT-031");
                            projectNumberList.add("AK104-IIT-032");
                            projectNumberList.add("AK104-IIT-033");
                            projectNumberList.add("AK104-IIT-034");
                            projectNumberList.add("AK104-IIT-035");
                            projectNumberList.add("AK104-IIT-036");
                            projectNumberList.add("AK104-IIT-037");
                            projectNumberList.add("AK104-IIT-038");
                            projectNumberList.add("AK104-IIT-039");
                            projectNumberList.add("AK104-IIT-040");
                            projectNumberList.add("AK104-IIT-041");
                            projectNumberList.add("AK104-IIT-042");
                            projectNumberList.add("AK104-IIT-043");
                        } else if (pipeline.equals("AK104-IIT-C")) {
                            projectNumberList.add("AK104-IIT-C-E-0001");
                            projectNumberList.add("AK104-IIT-C-E-0002");
                            projectNumberList.add("AK104-IIT-C-E-0018");
                            projectNumberList.add("AK104-IIT-C-E-0021");
                            projectNumberList.add("AK104-IIT-C-E-0022");
                            projectNumberList.add("AK104-IIT-C-E-0023");
                            projectNumberList.add("AK104-IIT-C-E-0024");
                            projectNumberList.add("AK104-IIT-C-E-0026");
                            projectNumberList.add("AK104-IIT-C-E-0027");
                            projectNumberList.add("AK104-IIT-C-E-0028");
                            projectNumberList.add("AK104-IIT-C-E-0029");
                            projectNumberList.add("AK104-IIT-C-E-0031");
                            projectNumberList.add("AK104-IIT-C-E-0032");
                            projectNumberList.add("AK104-IIT-C-E-0033");
                            projectNumberList.add("AK104-IIT-C-E-0034");
                            projectNumberList.add("AK104-IIT-C-E-0036");
                            projectNumberList.add("AK104-IIT-C-E-0037");
                            projectNumberList.add("AK104-IIT-C-E-0038");
                            projectNumberList.add("AK104-IIT-C-E-0039");
                            projectNumberList.add("AK104-IIT-C-E-0042");
                            projectNumberList.add("AK104-IIT-C-E-0043");
                            projectNumberList.add("AK104-IIT-C-E-0044");
                            projectNumberList.add("AK104-IIT-C-E-0045");
                            projectNumberList.add("AK104-IIT-C-E-0046");
                            projectNumberList.add("AK104-IIT-C-E-0047");
                            projectNumberList.add("AK104-IIT-C-E-0048");
                            projectNumberList.add("AK104-IIT-C-E-0049");
                            projectNumberList.add("AK104-IIT-C-E-0050");
                            projectNumberList.add("AK104-IIT-C-E-0051");
                            projectNumberList.add("AK104-IIT-C-E-0052");
                            projectNumberList.add("AK104-IIT-C-E-0053");
                            projectNumberList.add("AK104-IIT-C-E-0104");
                            projectNumberList.add("AK104-IIT-C-E-0105");
                            projectNumberList.add("AK104-IIT-C-E-0106");
                            projectNumberList.add("AK104-IIT-C-E-0107");
                            projectNumberList.add("AK104-IIT-C-E-0108");
                            projectNumberList.add("AK104-IIT-C-E-0111");
                            projectNumberList.add("AK104-IIT-C-E-0112");
                            projectNumberList.add("AK104-IIT-C-E-0113");
                            projectNumberList.add("AK104-IIT-C-E-0114");
                            projectNumberList.add("AK104-IIT-C-E-0115");
                            projectNumberList.add("AK104-IIT-C-E-0116");
                            projectNumberList.add("AK104-IIT-C-E-0117");
                            projectNumberList.add("AK104-IIT-C-E-0118");
                            projectNumberList.add("AK104-IIT-C-E-0119");
                            projectNumberList.add("AK104-IIT-C-E-0120");
                            projectNumberList.add("AK104-IIT-C-E-0121");
                            projectNumberList.add("AK104-IIT-C-E-0122");
                            projectNumberList.add("AK104-IIT-C-E-0123");
                            projectNumberList.add("AK104-IIT-C-E-0124");
                            projectNumberList.add("AK104-IIT-C-E-0125");
                            projectNumberList.add("AK104-IIT-C-E-0126");
                            projectNumberList.add("AK104-IIT-C-E-0128");
                            projectNumberList.add("AK104-IIT-C-E-0129");
                            projectNumberList.add("AK104-IIT-C-E-0130");
                            projectNumberList.add("AK104-IIT-C-E-0131");
                            projectNumberList.add("AK104-IIT-C-E-0132");
                            projectNumberList.add("AK104-IIT-C-E-0133");
                            projectNumberList.add("AK104-IIT-C-E-0135");
                            projectNumberList.add("AK104-IIT-C-E-0136");
                            projectNumberList.add("AK104-IIT-C-E-0137");
                            projectNumberList.add("AK104-IIT-C-E-0139");
                            projectNumberList.add("AK104-IIT-C-E-0140");
                            projectNumberList.add("AK104-IIT-C-E-0141");
                            projectNumberList.add("AK104-IIT-C-E-0142");
                            projectNumberList.add("AK104-IIT-C-E-0143");
                            projectNumberList.add("AK104-IIT-C-E-0144");
                            projectNumberList.add("AK104-IIT-C-E-0145");
                            projectNumberList.add("AK104-IIT-C-E-0147");
                            projectNumberList.add("AK104-IIT-C-E-0148");
                            projectNumberList.add("AK104-IIT-C-E-0151");
                            projectNumberList.add("AK104-IIT-C-E-0152");
                            projectNumberList.add("AK104-IIT-C-E-0153");
                            projectNumberList.add("AK104-IIT-C-E-0156");
                            projectNumberList.add("AK104-IIT-C-E-0157");
                            projectNumberList.add("AK104-IIT-C-E-0160");
                            projectNumberList.add("AK104-IIT-C-E-0161");
                            projectNumberList.add("AK104-IIT-C-E-0162");
                            projectNumberList.add("AK104-IIT-C-E-0164");
                            projectNumberList.add("AK104-IIT-C-E-0165");
                            projectNumberList.add("AK104-IIT-C-E-0166");
                            projectNumberList.add("AK104-IIT-C-E-0167");
                            projectNumberList.add("AK104-IIT-C-E-0168");
                            projectNumberList.add("AK104-IIT-C-E-0170");
                            projectNumberList.add("AK104-IIT-C-M-0018");
                            projectNumberList.add("AK104-IIT-C-M-0019");
                            projectNumberList.add("AK104-IIT-C-M-0020");
                            projectNumberList.add("AK104-IIT-C-M-0021");
                            projectNumberList.add("AK104-IIT-C-M-0022");
                            projectNumberList.add("AK104-IIT-C-M-0023");
                            projectNumberList.add("AK104-IIT-C-M-0024");
                            projectNumberList.add("AK104-IIT-C-M-0027");
                            projectNumberList.add("AK104-IIT-C-M-0029");
                            projectNumberList.add("AK104-IIT-C-M-0030");
                            projectNumberList.add("AK104-IIT-C-M-0031");
                            projectNumberList.add("AK104-IIT-C-M-0032");
                            projectNumberList.add("AK104-IIT-C-M-0035");
                            projectNumberList.add("AK104-IIT-C-M-0037");
                            projectNumberList.add("AK104-IIT-C-M-0038");
                            projectNumberList.add("AK104-IIT-C-M-0039");
                            projectNumberList.add("AK104-IIT-C-M-0040");
                            projectNumberList.add("AK104-IIT-C-M-0041");
                            projectNumberList.add("AK104-IIT-C-M-0042");
                            projectNumberList.add("AK104-IIT-C-M-0043");
                            projectNumberList.add("AK104-IIT-C-M-0044");
                            projectNumberList.add("AK104-IIT-C-M-0045");
                            projectNumberList.add("AK104-IIT-C-M-0047");
                            projectNumberList.add("AK104-IIT-C-M-0104");
                            projectNumberList.add("AK104-IIT-C-M-0105");
                            projectNumberList.add("AK104-IIT-C-M-0106");
                            projectNumberList.add("AK104-IIT-C-M-0109");
                            projectNumberList.add("AK104-IIT-C-M-0110");
                            projectNumberList.add("AK104-IIT-C-M-0111");
                            projectNumberList.add("AK104-IIT-C-M-0112");
                            projectNumberList.add("AK104-IIT-C-M-0113");
                            projectNumberList.add("AK104-IIT-C-M-0114");
                            projectNumberList.add("AK104-IIT-C-M-0115");
                            projectNumberList.add("AK104-IIT-C-M-0116");
                            projectNumberList.add("AK104-IIT-C-M-0118");
                            projectNumberList.add("AK104-IIT-C-M-0119");
                            projectNumberList.add("AK104-IIT-C-N1-0001");
                            projectNumberList.add("AK104-IIT-C-N1-0002");
                            projectNumberList.add("AK104-IIT-C-N1-0004");
                            projectNumberList.add("AK104-IIT-C-N1-0005");
                            projectNumberList.add("AK104-IIT-C-N1-0006");
                            projectNumberList.add("AK104-IIT-C-N1-0007");
                            projectNumberList.add("AK104-IIT-C-N1-0009");
                            projectNumberList.add("AK104-IIT-C-N1-0018");
                            projectNumberList.add("AK104-IIT-C-N1-0019");
                            projectNumberList.add("AK104-IIT-C-N1-0022");
                            projectNumberList.add("AK104-IIT-C-N1-0023");
                            projectNumberList.add("AK104-IIT-C-N1-0024");
                            projectNumberList.add("AK104-IIT-C-N1-0029");
                            projectNumberList.add("AK104-IIT-C-N1-0030");
                            projectNumberList.add("AK104-IIT-C-N1-0031");
                            projectNumberList.add("AK104-IIT-C-N1-0032");
                            projectNumberList.add("AK104-IIT-C-N1-0033");
                            projectNumberList.add("AK104-IIT-C-N1-0034");
                            projectNumberList.add("AK104-IIT-C-N1-0036");
                            projectNumberList.add("AK104-IIT-C-N1-0037");
                            projectNumberList.add("AK104-IIT-C-N1-0038");
                            projectNumberList.add("AK104-IIT-C-N1-0039");
                            projectNumberList.add("AK104-IIT-C-N1-0042");
                            projectNumberList.add("AK104-IIT-C-N1-0043");
                            projectNumberList.add("AK104-IIT-C-N1-0045");
                            projectNumberList.add("AK104-IIT-C-N1-0046");
                            projectNumberList.add("AK104-IIT-C-N1-0047");
                            projectNumberList.add("AK104-IIT-C-N1-0048");
                            projectNumberList.add("AK104-IIT-C-N1-0051");
                            projectNumberList.add("AK104-IIT-C-N1-0052");
                            projectNumberList.add("AK104-IIT-C-N1-0053");
                            projectNumberList.add("AK104-IIT-C-N1-0054");
                            projectNumberList.add("AK104-IIT-C-N1-0055");
                            projectNumberList.add("AK104-IIT-C-N1-0057");
                            projectNumberList.add("AK104-IIT-C-N1-0058");
                            projectNumberList.add("AK104-IIT-C-N1-0064");
                            projectNumberList.add("AK104-IIT-C-N1-0065");
                            projectNumberList.add("AK104-IIT-C-N1-0068");
                            projectNumberList.add("AK104-IIT-C-N1-0069");
                            projectNumberList.add("AK104-IIT-C-N1-0104");
                            projectNumberList.add("AK104-IIT-C-N1-0105");
                            projectNumberList.add("AK104-IIT-C-N1-0106");
                            projectNumberList.add("AK104-IIT-C-N1-0108");
                            projectNumberList.add("AK104-IIT-C-N1-0109");
                            projectNumberList.add("AK104-IIT-C-N1-0110");
                            projectNumberList.add("AK104-IIT-C-N1-0111");
                            projectNumberList.add("AK104-IIT-C-N1-0112");
                            projectNumberList.add("AK104-IIT-C-N1-0113");
                            projectNumberList.add("AK104-IIT-C-N1-0115");
                            projectNumberList.add("AK104-IIT-C-N1-0116");
                            projectNumberList.add("AK104-IIT-C-N1-0117");
                            projectNumberList.add("AK104-IIT-C-N1-0118");
                            projectNumberList.add("AK104-IIT-C-N2-0002");
                            projectNumberList.add("AK104-IIT-C-N2-0018");
                            projectNumberList.add("AK104-IIT-C-N2-0019");
                            projectNumberList.add("AK104-IIT-C-N2-0020");
                            projectNumberList.add("AK104-IIT-C-N2-0023");
                            projectNumberList.add("AK104-IIT-C-N2-0024");
                            projectNumberList.add("AK104-IIT-C-N2-0025");
                            projectNumberList.add("AK104-IIT-C-N2-0026");
                            projectNumberList.add("AK104-IIT-C-N2-0028");
                            projectNumberList.add("AK104-IIT-C-N2-0029");
                            projectNumberList.add("AK104-IIT-C-N2-0030");
                            projectNumberList.add("AK104-IIT-C-N2-0035");
                            projectNumberList.add("AK104-IIT-C-N2-0037");
                            projectNumberList.add("AK104-IIT-C-N2-0038");
                            projectNumberList.add("AK104-IIT-C-N2-0039");
                            projectNumberList.add("AK104-IIT-C-N2-0040");
                            projectNumberList.add("AK104-IIT-C-N2-0044");
                            projectNumberList.add("AK104-IIT-C-N2-0045");
                            projectNumberList.add("AK104-IIT-C-N2-0046");
                            projectNumberList.add("AK104-IIT-C-N2-0047");
                            projectNumberList.add("AK104-IIT-C-N2-0049");
                            projectNumberList.add("AK104-IIT-C-N2-0104");
                            projectNumberList.add("AK104-IIT-C-N2-0105");
                            projectNumberList.add("AK104-IIT-C-N2-0106");
                            projectNumberList.add("AK104-IIT-C-N2-0107");
                            projectNumberList.add("AK104-IIT-C-N2-0108");
                            projectNumberList.add("AK104-IIT-C-N2-0110");
                            projectNumberList.add("AK104-IIT-C-N2-0111");
                            projectNumberList.add("AK104-IIT-C-N2-0112");
                            projectNumberList.add("AK104-IIT-C-N2-0113");
                            projectNumberList.add("AK104-IIT-C-N2-0114");
                            projectNumberList.add("AK104-IIT-C-N2-0115");
                            projectNumberList.add("AK104-IIT-C-N2-0119");
                            projectNumberList.add("AK104-IIT-C-N2-0120");
                            projectNumberList.add("AK104-IIT-C-N2-0121");
                            projectNumberList.add("AK104-IIT-C-N2-0122");
                            projectNumberList.add("AK104-IIT-C-N2-0123");
                            projectNumberList.add("AK104-IIT-C-N2-0124");
                            projectNumberList.add("AK104-IIT-C-N2-0125");
                            projectNumberList.add("AK104-IIT-C-N2-0126");
                            projectNumberList.add("AK104-IIT-C-N2-0127");
                            projectNumberList.add("AK104-IIT-C-N2-0129");
                            projectNumberList.add("AK104-IIT-C-N2-0130");
                            projectNumberList.add("AK104-IIT-C-N2-0131");
                            projectNumberList.add("AK104-IIT-C-N2-0133");
                            projectNumberList.add("AK104-IIT-C-N2-0134");
                            projectNumberList.add("AK104-IIT-C-N2-0137");
                            projectNumberList.add("AK104-IIT-C-N2-0138");
                            projectNumberList.add("AK104-IIT-C-N2-0139");
                            projectNumberList.add("AK104-IIT-C-N2-0143");
                            projectNumberList.add("AK104-IIT-C-N2-0144");
                            projectNumberList.add("AK104-IIT-C-N2-0145");
                            projectNumberList.add("AK104-IIT-C-N2-0146");
                            projectNumberList.add("AK104-IIT-C-S-0002");
                            projectNumberList.add("AK104-IIT-C-S-0004");
                            projectNumberList.add("AK104-IIT-C-S-0005");
                            projectNumberList.add("AK104-IIT-C-S-0007");
                            projectNumberList.add("AK104-IIT-C-S-0008");
                            projectNumberList.add("AK104-IIT-C-S-0010");
                            projectNumberList.add("AK104-IIT-C-S-0011");
                            projectNumberList.add("AK104-IIT-C-S-0012");
                            projectNumberList.add("AK104-IIT-C-S-0013");
                            projectNumberList.add("AK104-IIT-C-S-0014");
                            projectNumberList.add("AK104-IIT-C-S-0015");
                            projectNumberList.add("AK104-IIT-C-S-0018");
                            projectNumberList.add("AK104-IIT-C-S-0019");
                            projectNumberList.add("AK104-IIT-C-S-0021");
                            projectNumberList.add("AK104-IIT-C-S-0022");
                            projectNumberList.add("AK104-IIT-C-S-0023");
                            projectNumberList.add("AK104-IIT-C-S-0024");
                            projectNumberList.add("AK104-IIT-C-S-0025");
                            projectNumberList.add("AK104-IIT-C-S-0026");
                            projectNumberList.add("AK104-IIT-C-S-0027");
                            projectNumberList.add("AK104-IIT-C-S-0029");
                            projectNumberList.add("AK104-IIT-C-S-0030");
                            projectNumberList.add("AK104-IIT-C-S-0032");
                            projectNumberList.add("AK104-IIT-C-S-0034");
                            projectNumberList.add("AK104-IIT-C-S-0035");
                            projectNumberList.add("AK104-IIT-C-S-0041");
                            projectNumberList.add("AK104-IIT-C-S-0043");
                            projectNumberList.add("AK104-IIT-C-S-0044");
                            projectNumberList.add("AK104-IIT-C-S-0045");
                            projectNumberList.add("AK104-IIT-C-S-0050");
                            projectNumberList.add("AK104-IIT-C-S-0051");
                            projectNumberList.add("AK104-IIT-C-S-0052");
                            projectNumberList.add("AK104-IIT-C-S-0053");
                            projectNumberList.add("AK104-IIT-C-S-0055");
                            projectNumberList.add("AK104-IIT-C-S-0056");
                            projectNumberList.add("AK104-IIT-C-S-0057");
                            projectNumberList.add("AK104-IIT-C-S-0059");
                            projectNumberList.add("AK104-IIT-C-S-0061");
                            projectNumberList.add("AK104-IIT-C-S-0062");
                            projectNumberList.add("AK104-IIT-C-S-0063");
                            projectNumberList.add("AK104-IIT-C-S-0064");
                            projectNumberList.add("AK104-IIT-C-S-0065");
                            projectNumberList.add("AK104-IIT-C-S-0068");
                            projectNumberList.add("AK104-IIT-C-S-0070");
                            projectNumberList.add("AK104-IIT-C-S-0071");
                            projectNumberList.add("AK104-IIT-C-S-0074");
                            projectNumberList.add("AK104-IIT-C-S-0075");
                            projectNumberList.add("AK104-IIT-C-S-0076");
                            projectNumberList.add("AK104-IIT-C-S-0078");
                            projectNumberList.add("AK104-IIT-C-S-0082");
                            projectNumberList.add("AK104-IIT-C-S-0083");
                            projectNumberList.add("AK104-IIT-C-S-0085");
                            projectNumberList.add("AK104-IIT-C-S-0086");
                            projectNumberList.add("AK104-IIT-C-S-0087");
                            projectNumberList.add("AK104-IIT-C-S-0088");
                            projectNumberList.add("AK104-IIT-C-S-0090");
                            projectNumberList.add("AK104-IIT-C-S-0092");
                            projectNumberList.add("AK104-IIT-C-S-0093");
                            projectNumberList.add("AK104-IIT-C-S-0096");
                            projectNumberList.add("AK104-IIT-C-S-0097");
                            projectNumberList.add("AK104-IIT-C-S-0098");
                            projectNumberList.add("AK104-IIT-C-S-0099");
                            projectNumberList.add("AK104-IIT-C-S-0100");
                            projectNumberList.add("AK104-IIT-C-S-0101");
                            projectNumberList.add("AK104-IIT-C-S-0102");
                            projectNumberList.add("AK104-IIT-C-S-0103");
                            projectNumberList.add("AK104-IIT-C-S-0105");
                            projectNumberList.add("AK104-IIT-C-S-0106");
                            projectNumberList.add("AK104-IIT-C-S-0108");
                            projectNumberList.add("AK104-IIT-C-S-0110");
                            projectNumberList.add("AK104-IIT-C-S-0112");
                            projectNumberList.add("AK104-IIT-C-S-0114");
                            projectNumberList.add("AK104-IIT-C-S-0117");
                            projectNumberList.add("AK104-IIT-C-S-0118");
                            projectNumberList.add("AK104-IIT-C-S-0120");
                            projectNumberList.add("AK104-IIT-C-S-0122");
                            projectNumberList.add("AK104-IIT-C-S-0123");
                            projectNumberList.add("AK104-IIT-C-S-0124");
                            projectNumberList.add("AK104-IIT-C-S-0125");
                            projectNumberList.add("AK104-IIT-C-S-0128");
                            projectNumberList.add("AK104-IIT-C-S-0129");
                            projectNumberList.add("AK104-IIT-C-W-0019");
                            projectNumberList.add("AK104-IIT-C-W-0021");
                            projectNumberList.add("AK104-IIT-C-W-0024");
                            projectNumberList.add("AK104-IIT-C-W-0025");
                            projectNumberList.add("AK104-IIT-C-W-0026");
                            projectNumberList.add("AK104-IIT-C-W-0027");
                            projectNumberList.add("AK104-IIT-C-W-0029");
                            projectNumberList.add("AK104-IIT-C-W-0030");
                            projectNumberList.add("AK104-IIT-C-W-0032");
                            projectNumberList.add("AK104-IIT-C-W-0035");
                            projectNumberList.add("AK104-IIT-C-W-0036");
                            projectNumberList.add("AK104-IIT-C-W-0037");
                            projectNumberList.add("AK104-IIT-C-W-0040");
                            projectNumberList.add("AK104-IIT-C-W-0041");
                            projectNumberList.add("AK104-IIT-C-W-0042");
                            projectNumberList.add("AK104-IIT-C-W-0043");
                            projectNumberList.add("AK104-IIT-C-W-0044");
                            projectNumberList.add("AK104-IIT-C-W-0045");
                            projectNumberList.add("AK104-IIT-C-W-0047");
                            projectNumberList.add("AK104-IIT-C-W-0048");
                            projectNumberList.add("AK104-IIT-C-W-0049");
                            projectNumberList.add("AK104-IIT-C-W-0050");
                            projectNumberList.add("AK104-IIT-C-W-0052");
                            projectNumberList.add("AK104-IIT-C-W-0053");
                            projectNumberList.add("AK104-IIT-C-W-0054");
                            projectNumberList.add("AK104-IIT-C-W-0055");
                            projectNumberList.add("AK104-IIT-C-W-0056");
                            projectNumberList.add("AK104-IIT-C-W-0057");
                            projectNumberList.add("AK104-IIT-C-W-0058");
                            projectNumberList.add("AK104-IIT-C-W-0059");
                            projectNumberList.add("AK104-IIT-C-W-0106");
                            projectNumberList.add("AK104-IIT-C-W-0107");
                            projectNumberList.add("AK104-IIT-C-W-0108");
                            projectNumberList.add("AK104-IIT-C-W-0109");
                            projectNumberList.add("AK104-IIT-C-W-0110");
                            projectNumberList.add("AK104-IIT-C-W-0111");
                            projectNumberList.add("AK104-IIT-C-W-0115");
                            projectNumberList.add("AK104-IIT-C-W-0117");
                            projectNumberList.add("AK104-IIT-C-W-0120");
                            projectNumberList.add("AK104-IIT-C-W-0122");
                            projectNumberList.add("AK104-IIT-C-W-0123");
                            projectNumberList.add("AK104-IIT-C-W-0125");
                            projectNumberList.add("AK104-IIT-C-W-0127");
                            projectNumberList.add("AK104-IIT-C-W-0128");
                            projectNumberList.add("AK104-IIT-C-W-0129");
                            projectNumberList.add("AK104-IIT-C-W-0132");
                            projectNumberList.add("AK104-IIT-C-W-0133");
                        } else if (pipeline.equals("AK104-RC48")) {
                            projectNumberList.add("AK104-RC48");
                        } else if (pipeline.equals("AK104ZY")) {
                            projectNumberList.add("AK104ZY001");
                        } else if (pipeline.equals("AK105")) {
                            projectNumberList.add("AK105");
                            projectNumberList.add("AK105-101");
                            projectNumberList.add("AK105-201");
                            projectNumberList.add("AK105-202");
                            projectNumberList.add("AK105-203");
                            projectNumberList.add("AK105-204");
                            projectNumberList.add("AK105-205");
                            projectNumberList.add("AK105-301");
                            projectNumberList.add("AK105-302");
                            projectNumberList.add("AK105-303");
                            projectNumberList.add("AK105-304");
                            projectNumberList.add("KF051");
                            projectNumberList.add("AK105-SAS");
                        } else if (pipeline.equals("AK106")) {
                            projectNumberList.add("AK106");
                            projectNumberList.add("KF025");
                        } else if (pipeline.equals("AK107")) {
                            projectNumberList.add("AK107");
                            projectNumberList.add("KF001");
                        } else if (pipeline.equals("AK109")) {
                            projectNumberList.add("AK109");
                            projectNumberList.add("AK109-101");
                            projectNumberList.add("AK109-102");
                            projectNumberList.add("AK109-103");
                            projectNumberList.add("AK109-201");
                            projectNumberList.add("AK109-202");
                            projectNumberList.add("AK109-301");
                            projectNumberList.add("KF031");
                        } else if (pipeline.equals("AK111")) {
                            projectNumberList.add("AK111");
                            projectNumberList.add("AK111-101");
                            projectNumberList.add("AK111-102");
                            projectNumberList.add("AK111-103");
                            projectNumberList.add("AK111-201");
                            projectNumberList.add("AK111-202");
                            projectNumberList.add("AK111-203");
                            projectNumberList.add("AK111-301");
                            projectNumberList.add("AK111-302");
                            projectNumberList.add("AK111-303");
                            projectNumberList.add("KF053");
                        } else if (pipeline.equals("AK112")) {
                            projectNumberList.add("AK112");
                            projectNumberList.add("AK112-101");
                            projectNumberList.add("AK112-102");
                            projectNumberList.add("AK112-103");
                            projectNumberList.add("AK112-104");
                            projectNumberList.add("AK112-201");
                            projectNumberList.add("AK112-202");
                            projectNumberList.add("AK112-203");
                            projectNumberList.add("AK112-204");
                            projectNumberList.add("AK112-205");
                            projectNumberList.add("AK112-206");
                            projectNumberList.add("AK112-207");
                            projectNumberList.add("AK112-208");
                            projectNumberList.add("AK112-209");
                            projectNumberList.add("AK112-210");
                            projectNumberList.add("AK112-301");
                            projectNumberList.add("AK112-302");
                            projectNumberList.add("AK112-303");
                            projectNumberList.add("AK112-305");
                            projectNumberList.add("AK112-306");
                            projectNumberList.add("AK112-307");
                            projectNumberList.add("AK112-308");
                            projectNumberList.add("AK112-309");
                            projectNumberList.add("AK112-310");
                            projectNumberList.add("AK112-SAS");
                            projectNumberList.add("KF064");
                        } else if (pipeline.equals("AK112-3003")) {
                            projectNumberList.add("AK112-3003");
                        } else if (pipeline.equals("AK112-IIT")) {
                            projectNumberList.add("AK112-IIT-001");
                            projectNumberList.add("AK112-IIT-002");
                            projectNumberList.add("AK112-IIT-004");
                            projectNumberList.add("AK112-IIT-006");
                            projectNumberList.add("AK112-IIT-007");
                            projectNumberList.add("AK112-IIT-009");
                        } else if (pipeline.equals("AK112-IIT-C")) {
                            projectNumberList.add("AK112-IIT-C-E-0001");
                            projectNumberList.add("AK112-IIT-C-E-0002");
                            projectNumberList.add("AK112-IIT-C-E-0003");
                            projectNumberList.add("AK112-IIT-C-E-0006");
                            projectNumberList.add("AK112-IIT-C-E-0007");
                            projectNumberList.add("AK112-IIT-C-M-0001");
                            projectNumberList.add("AK112-IIT-C-W-0001");
                        } else if (pipeline.equals("AK112ZY")) {
                            projectNumberList.add("AK112ZY001");
                        } else if (pipeline.equals("AK114")) {
                            projectNumberList.add("AK114");
                            projectNumberList.add("AK114-101");
                            projectNumberList.add("AK114-102");
                            projectNumberList.add("KF041");
                        } else if (pipeline.equals("AK115")) {
                            projectNumberList.add("AK115");
                            projectNumberList.add("AK115-101");
                            projectNumberList.add("KF059");
                        } else if (pipeline.equals("AK117")) {
                            projectNumberList.add("AK117");
                            projectNumberList.add("AK117-101");
                            projectNumberList.add("AK117-102");
                            projectNumberList.add("AK117-103");
                            projectNumberList.add("AK117-104");
                            projectNumberList.add("AK117-201");
                            projectNumberList.add("AK117-202");
                            projectNumberList.add("AK117-203");
                            projectNumberList.add("AK117-204");
                            projectNumberList.add("AK117-205");
                            projectNumberList.add("AK117-206");
                            projectNumberList.add("AK117-301");
                            projectNumberList.add("AK117-302");
                            projectNumberList.add("AK117-SAS");
                            projectNumberList.add("KF039");
                        } else if (pipeline.equals("AK117ZY")) {
                            projectNumberList.add("AK117ZY001");
                        } else if (pipeline.equals("AK119")) {
                            projectNumberList.add("AK119");
                            projectNumberList.add("AK119-101");
                            projectNumberList.add("AK119-102");
                            projectNumberList.add("AK119-103");
                            projectNumberList.add("AK119-104");
                            projectNumberList.add("AK119-105");
                            projectNumberList.add("AK119-201");
                            projectNumberList.add("AK119-202");
                            projectNumberList.add("KF068");
                        } else if (pipeline.equals("AK120")) {
                            projectNumberList.add("AK120");
                            projectNumberList.add("AK120-101");
                            projectNumberList.add("AK120-102");
                            projectNumberList.add("AK120-201");
                            projectNumberList.add("AK120-202");
                            projectNumberList.add("AK120-203");
                            projectNumberList.add("AK120-204");
                            projectNumberList.add("AK120-205");
                            projectNumberList.add("AK120-206");
                            projectNumberList.add("AK120-207");
                            projectNumberList.add("AK120-301");
                            projectNumberList.add("KF070");
                        } else if (pipeline.equals("AK127")) {
                            projectNumberList.add("AK127");
                            projectNumberList.add("AK127-101");
                            projectNumberList.add("AK127-102");
                            projectNumberList.add("AK127-103");
                            projectNumberList.add("AK127-104");
                        } else if (pipeline.equals("AK127ZY")) {
                            projectNumberList.add("AK127ZY001");
                        } else if (pipeline.equals("AK129")) {
                            projectNumberList.add("AK129");
                            projectNumberList.add("AK129-101");
                            projectNumberList.add("AK129-102");
                            projectNumberList.add("AK129-103");
                            projectNumberList.add("KF083");
                        } else if (pipeline.equals("AK130")) {
                            projectNumberList.add("AK130");
                            projectNumberList.add("AK130-101");
                            projectNumberList.add("AK130-102");
                        } else if (pipeline.equals("AK131")) {
                            projectNumberList.add("AK131");
                            projectNumberList.add("AK131-101");
                        } else if (pipeline.equals("AK132")) {
                            projectNumberList.add("AK132");
                            projectNumberList.add("AK132-101");
                        } else if (pipeline.equals("AK140N-IIT")) {
                            projectNumberList.add("AK140N-IIT-001");
                            projectNumberList.add("AK140N-IIT-002");
                        } else if (pipeline.equals("CT")) {
                            projectNumberList.add("公摊内部订单（财务和临床专用）");
                        } else if (pipeline.equals("KF项目")) {
                            projectNumberList.add("AK116");
                            projectNumberList.add("AK122");
                            projectNumberList.add("AK123");
                            projectNumberList.add("AK124");
                            projectNumberList.add("AK125");
                            projectNumberList.add("AK126");
                            projectNumberList.add("AK128");
                            projectNumberList.add("AK133");
                            projectNumberList.add("AK134");
                            projectNumberList.add("AK135");
                            projectNumberList.add("AK136");
                            projectNumberList.add("AK137");
                            projectNumberList.add("AK138");
                            projectNumberList.add("AK138D1");
                            projectNumberList.add("AK139");
                            projectNumberList.add("AK140");
                            projectNumberList.add("AK140N");
                            projectNumberList.add("AK141");
                            projectNumberList.add("AK142(研发项目KF085)");
                            projectNumberList.add("AK143");
                            projectNumberList.add("AK143(研发项目KF085)");
                            projectNumberList.add("AK144");
                            projectNumberList.add("AK145");
                            projectNumberList.add("AK146");
                            projectNumberList.add("AK146D1");
                            projectNumberList.add("AK147");
                            projectNumberList.add("AK148");
                            projectNumberList.add("AK150");
                            projectNumberList.add("KF000");
                            projectNumberList.add("KF002");
                            projectNumberList.add("KF005");
                            projectNumberList.add("KF006");
                            projectNumberList.add("KF018");
                            projectNumberList.add("KF021");
                            projectNumberList.add("KF022");
                            projectNumberList.add("KF023");
                            projectNumberList.add("KF024");
                            projectNumberList.add("KF026");
                            projectNumberList.add("KF027");
                            projectNumberList.add("KF032");
                            projectNumberList.add("KF033");
                            projectNumberList.add("KF035");
                            projectNumberList.add("KF036");
                            projectNumberList.add("KF037");
                            projectNumberList.add("KF040");
                            projectNumberList.add("KF042");
                            projectNumberList.add("KF042ZB1");
                            projectNumberList.add("KF047");
                            projectNumberList.add("KF049");
                            projectNumberList.add("KF052");
                            projectNumberList.add("KF061");
                            projectNumberList.add("KF062");
                            projectNumberList.add("KF063");
                            projectNumberList.add("KF069");
                            projectNumberList.add("KF071");
                            projectNumberList.add("KF072");
                            projectNumberList.add("KF073");
                            projectNumberList.add("KF074");
                            projectNumberList.add("KF075");
                            projectNumberList.add("KF076");
                            projectNumberList.add("KF077");
                            projectNumberList.add("KF078");
                            projectNumberList.add("KF079");
                            projectNumberList.add("KF084");
                            projectNumberList.add("KF085");
                            projectNumberList.add("KF086");
                            projectNumberList.add("KF087");
                            projectNumberList.add("KF088");
                            projectNumberList.add("KF089");
                            projectNumberList.add("KF090");
                            projectNumberList.add("KF091");
                            projectNumberList.add("KF092");
                            projectNumberList.add("KF093");
                            projectNumberList.add("KF094");
                            projectNumberList.add("KF095");
                            projectNumberList.add("KF096");
                            projectNumberList.add("KF097");
                            projectNumberList.add("KF098");
                            projectNumberList.add("KF099");
                            projectNumberList.add("KF100");
                            projectNumberList.add("KF101");
                            projectNumberList.add("KF102");
                            projectNumberList.add("KF103");
                            projectNumberList.add("KF104");
                            projectNumberList.add("KF105");
                            projectNumberList.add("KF106");
                            projectNumberList.add("KF107");
                            projectNumberList.add("KF108");
                            projectNumberList.add("KF109");
                        } else if (pipeline.equals("SM112-301")) {
                            projectNumberList.add("SM112-301");
                        } else if (pipeline.equals("SPH4336-201")) {
                            projectNumberList.add("SPH4336-201 药业联合上药");
                        }
                        for (int k = 0; k < projectNumberList.size(); k++) {
                            try {
                                String project = projectNumberList.get(k);
                                BigDecimal totalAmount = new BigDecimal(0);
                                List<String> monthlylist = new ArrayList<>();
                                if (month.equals(12)) {
                                    monthlylist.add("12");
                                    monthlylist.add("13");
                                } else {
                                    monthlylist.add(month);
                                }
                                if (list.size() > 0) {
                                    totalAmount = list.stream()
                                            .filter(a -> a.getAmount() != null)
                                            .filter(a -> a.getYear() != null)
                                            .filter(a -> a.getMonth() != null)
                                            .filter(a -> a.getDrugPipeline() != null)
                                            .filter(a -> a.getProjectNumber() != null)
                                            .filter(a -> a.getYear().equals(newYear))
                                            .filter(a -> monthlylist.contains(a.getMonth()))
                                            .filter(a -> a.getDrugPipeline().equals(pipeline))
                                            .filter(a -> a.getProjectNumber().equals(project))
                                            .map(ResearchDevelopmentExpenseSummary::getAmount)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                }
                                ResearchDevelopmentExpensePipeline rd = new ResearchDevelopmentExpensePipeline();
                                rd.setYear(newYear);
                                rd.setMonth(month);
                                rd.setYearMonth(newYear + "-" + month);
                                rd.setAmount(totalAmount);
                                rd.setDrugPipeline(pipeline);
                                rd.setProjectNumber(project);
                                int result = researchDevelopmentExpensePipelineMapper.insertResearchDevelopmentExpensePipeline(rd);
                                if (result == 1) {
                                    successNum++;
                                    successMsg.append("<br/>" + successNum + "、年月 " + newYear + month + "、管线 " + pipeline + "、项目号 " + project + " 添加成功");
                                } else {
                                    failureNum++;
                                    failureMsg.append("<br/>" + successNum + "、年月 " + newYear + month + "、管线 " + pipeline + "、项目号 " + project + " 添加失败");
                                }
                            } catch (Exception e) {
                                failureNum++;
                                String msg = "<br/>" + successNum + "、年月 " + newYear + month + "、管线 " + pipeline + "、项目号 " + projectNumberList.get(k) + " 添加失败:";
                                failureMsg.append(msg + e.getMessage());
                            }
                        }
                    }
                }
            }
            log.info("新增成功：" + successNum + "条--" + successMsg.toString());
            log.error("新增失败：--" + failureNum + "条--" + failureMsg.toString());
        } else {
            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--定时更新研究费用管线项目汇总表--不在**********服务器执行的定时任务--跳过");
        }
    }

    /**
     * 定时更新 研究费用项目汇总表
     */
    public void researchDevelopmenteExpenseProjectTask() throws Exception {
        String computerName = GetComputerName.getComputerName();
        System.out.println("计算机名称：" + computerName);
        if (computerName.equals("SERVER22")) {
            researchDevelopmentExpenseProjectMapper.deleteAllResearchDevelopmentExpenseProject();
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            List<Integer> yearList = new ArrayList<>();
            for (int i = 2017; i <= year; i++) {
                yearList.add(i);
            }

            List<String> costList = new ArrayList<>();
            costList.add("材料成本");
            costList.add("人工成本");
            costList.add("运行成本");
            costList.add("折旧及摊销");
            costList.add("直接成本");

            List<String> projectList = new ArrayList<>();
            projectList.add("项目号");
            projectList.add("AK101");
            projectList.add("AK101-101");
            projectList.add("AK101-102");
            projectList.add("AK101-103");
            projectList.add("AK101-201");
            projectList.add("AK101-301");
            projectList.add("AK101-302");
            projectList.add("AK101-303");
            projectList.add("KF003");
            projectList.add("AK102");
            projectList.add("AK102-101");
            projectList.add("AK102-201");
            projectList.add("AK102-202");
            projectList.add("AK102-203");
            projectList.add("AK102-204");
            projectList.add("AK102-301");
            projectList.add("AK102-302");
            projectList.add("AK102-303");
            projectList.add("KF011");
            projectList.add("AK103");
            projectList.add("KF007");
            projectList.add("AK104");
            projectList.add("AK104-101");
            projectList.add("AK104-102");
            projectList.add("AK104-201");
            projectList.add("AK104-201-AU");
            projectList.add("AK104-201A");
            projectList.add("AK104-201B");
            projectList.add("AK104-202");
            projectList.add("AK104-203");
            projectList.add("AK104-204");
            projectList.add("AK104-205");
            projectList.add("AK104-206");
            projectList.add("AK104-207");
            projectList.add("AK104-208");
            projectList.add("AK104-209");
            projectList.add("AK104-210");
            projectList.add("AK104-211");
            projectList.add("AK104-212");
            projectList.add("AK104-213");
            projectList.add("AK104-214");
            projectList.add("AK104-215");
            projectList.add("AK104-216");
            projectList.add("AK104-217");
            projectList.add("AK104-218");
            projectList.add("AK104-219");
            projectList.add("AK104-220");
            projectList.add("AK104-221");
            projectList.add("AK104-222");
            projectList.add("AK104-301");
            projectList.add("AK104-302");
            projectList.add("AK104-303");
            projectList.add("AK104-304");
            projectList.add("AK104-305");
            projectList.add("AK104-306");
            projectList.add("AK104-307");
            projectList.add("AK104-308");
            projectList.add("AK104-309");
            projectList.add("AK104-SAS");
            projectList.add("KF030");
            projectList.add("AK104-IIS-001(澳洲)");
            projectList.add("AK104-IIS-002(澳洲)");
            projectList.add("AK104-IIT-001");
            projectList.add("AK104-IIT-002");
            projectList.add("AK104-IIT-003");
            projectList.add("AK104-IIT-004");
            projectList.add("AK104-IIT-005");
            projectList.add("AK104-IIT-006");
            projectList.add("AK104-IIT-007");
            projectList.add("AK104-IIT-008");
            projectList.add("AK104-IIT-009");
            projectList.add("AK104-IIT-010");
            projectList.add("AK104-IIT-011");
            projectList.add("AK104-IIT-012");
            projectList.add("AK104-IIT-013");
            projectList.add("AK104-IIT-014");
            projectList.add("AK104-IIT-015");
            projectList.add("AK104-IIT-016");
            projectList.add("AK104-IIT-017");
            projectList.add("AK104-IIT-018");
            projectList.add("AK104-IIT-019");
            projectList.add("AK104-IIT-020");
            projectList.add("AK104-IIT-021");
            projectList.add("AK104-IIT-024");
            projectList.add("AK104-IIT-025");
            projectList.add("AK104-IIT-026");
            projectList.add("AK104-IIT-027");
            projectList.add("AK104-IIT-028");
            projectList.add("AK104-IIT-029");
            projectList.add("AK104-IIT-030");
            projectList.add("AK104-IIT-031");
            projectList.add("AK104-IIT-032");
            projectList.add("AK104-IIT-033");
            projectList.add("AK104-IIT-034");
            projectList.add("AK104-IIT-035");
            projectList.add("AK104-IIT-036");
            projectList.add("AK104-IIT-037");
            projectList.add("AK104-IIT-038");
            projectList.add("AK104-IIT-039");
            projectList.add("AK104-IIT-040");
            projectList.add("AK104-IIT-041");
            projectList.add("AK104-IIT-042");
            projectList.add("AK104-IIT-043");
            projectList.add("AK104-IIT-C-E-0001");
            projectList.add("AK104-IIT-C-E-0002");
            projectList.add("AK104-IIT-C-E-0018");
            projectList.add("AK104-IIT-C-E-0021");
            projectList.add("AK104-IIT-C-E-0022");
            projectList.add("AK104-IIT-C-E-0023");
            projectList.add("AK104-IIT-C-E-0024");
            projectList.add("AK104-IIT-C-E-0026");
            projectList.add("AK104-IIT-C-E-0027");
            projectList.add("AK104-IIT-C-E-0028");
            projectList.add("AK104-IIT-C-E-0029");
            projectList.add("AK104-IIT-C-E-0031");
            projectList.add("AK104-IIT-C-E-0032");
            projectList.add("AK104-IIT-C-E-0033");
            projectList.add("AK104-IIT-C-E-0034");
            projectList.add("AK104-IIT-C-E-0036");
            projectList.add("AK104-IIT-C-E-0037");
            projectList.add("AK104-IIT-C-E-0038");
            projectList.add("AK104-IIT-C-E-0039");
            projectList.add("AK104-IIT-C-E-0042");
            projectList.add("AK104-IIT-C-E-0043");
            projectList.add("AK104-IIT-C-E-0044");
            projectList.add("AK104-IIT-C-E-0045");
            projectList.add("AK104-IIT-C-E-0046");
            projectList.add("AK104-IIT-C-E-0047");
            projectList.add("AK104-IIT-C-E-0048");
            projectList.add("AK104-IIT-C-E-0049");
            projectList.add("AK104-IIT-C-E-0050");
            projectList.add("AK104-IIT-C-E-0051");
            projectList.add("AK104-IIT-C-E-0052");
            projectList.add("AK104-IIT-C-E-0053");
            projectList.add("AK104-IIT-C-E-0104");
            projectList.add("AK104-IIT-C-E-0105");
            projectList.add("AK104-IIT-C-E-0106");
            projectList.add("AK104-IIT-C-E-0107");
            projectList.add("AK104-IIT-C-E-0108");
            projectList.add("AK104-IIT-C-E-0111");
            projectList.add("AK104-IIT-C-E-0112");
            projectList.add("AK104-IIT-C-E-0113");
            projectList.add("AK104-IIT-C-E-0114");
            projectList.add("AK104-IIT-C-E-0115");
            projectList.add("AK104-IIT-C-E-0116");
            projectList.add("AK104-IIT-C-E-0117");
            projectList.add("AK104-IIT-C-E-0118");
            projectList.add("AK104-IIT-C-E-0119");
            projectList.add("AK104-IIT-C-E-0120");
            projectList.add("AK104-IIT-C-E-0121");
            projectList.add("AK104-IIT-C-E-0122");
            projectList.add("AK104-IIT-C-E-0123");
            projectList.add("AK104-IIT-C-E-0124");
            projectList.add("AK104-IIT-C-E-0125");
            projectList.add("AK104-IIT-C-E-0126");
            projectList.add("AK104-IIT-C-E-0128");
            projectList.add("AK104-IIT-C-E-0129");
            projectList.add("AK104-IIT-C-E-0130");
            projectList.add("AK104-IIT-C-E-0131");
            projectList.add("AK104-IIT-C-E-0132");
            projectList.add("AK104-IIT-C-E-0133");
            projectList.add("AK104-IIT-C-E-0135");
            projectList.add("AK104-IIT-C-E-0136");
            projectList.add("AK104-IIT-C-E-0137");
            projectList.add("AK104-IIT-C-E-0139");
            projectList.add("AK104-IIT-C-E-0140");
            projectList.add("AK104-IIT-C-E-0141");
            projectList.add("AK104-IIT-C-E-0142");
            projectList.add("AK104-IIT-C-E-0143");
            projectList.add("AK104-IIT-C-E-0144");
            projectList.add("AK104-IIT-C-E-0145");
            projectList.add("AK104-IIT-C-E-0147");
            projectList.add("AK104-IIT-C-E-0148");
            projectList.add("AK104-IIT-C-E-0151");
            projectList.add("AK104-IIT-C-E-0152");
            projectList.add("AK104-IIT-C-E-0153");
            projectList.add("AK104-IIT-C-E-0156");
            projectList.add("AK104-IIT-C-E-0157");
            projectList.add("AK104-IIT-C-E-0160");
            projectList.add("AK104-IIT-C-E-0161");
            projectList.add("AK104-IIT-C-E-0162");
            projectList.add("AK104-IIT-C-E-0164");
            projectList.add("AK104-IIT-C-E-0165");
            projectList.add("AK104-IIT-C-E-0166");
            projectList.add("AK104-IIT-C-E-0167");
            projectList.add("AK104-IIT-C-E-0168");
            projectList.add("AK104-IIT-C-E-0170");
            projectList.add("AK104-IIT-C-M-0018");
            projectList.add("AK104-IIT-C-M-0019");
            projectList.add("AK104-IIT-C-M-0020");
            projectList.add("AK104-IIT-C-M-0021");
            projectList.add("AK104-IIT-C-M-0022");
            projectList.add("AK104-IIT-C-M-0023");
            projectList.add("AK104-IIT-C-M-0024");
            projectList.add("AK104-IIT-C-M-0027");
            projectList.add("AK104-IIT-C-M-0029");
            projectList.add("AK104-IIT-C-M-0030");
            projectList.add("AK104-IIT-C-M-0031");
            projectList.add("AK104-IIT-C-M-0032");
            projectList.add("AK104-IIT-C-M-0035");
            projectList.add("AK104-IIT-C-M-0037");
            projectList.add("AK104-IIT-C-M-0038");
            projectList.add("AK104-IIT-C-M-0039");
            projectList.add("AK104-IIT-C-M-0040");
            projectList.add("AK104-IIT-C-M-0041");
            projectList.add("AK104-IIT-C-M-0042");
            projectList.add("AK104-IIT-C-M-0043");
            projectList.add("AK104-IIT-C-M-0044");
            projectList.add("AK104-IIT-C-M-0045");
            projectList.add("AK104-IIT-C-M-0047");
            projectList.add("AK104-IIT-C-M-0104");
            projectList.add("AK104-IIT-C-M-0105");
            projectList.add("AK104-IIT-C-M-0106");
            projectList.add("AK104-IIT-C-M-0109");
            projectList.add("AK104-IIT-C-M-0110");
            projectList.add("AK104-IIT-C-M-0111");
            projectList.add("AK104-IIT-C-M-0112");
            projectList.add("AK104-IIT-C-M-0113");
            projectList.add("AK104-IIT-C-M-0114");
            projectList.add("AK104-IIT-C-M-0115");
            projectList.add("AK104-IIT-C-M-0116");
            projectList.add("AK104-IIT-C-M-0118");
            projectList.add("AK104-IIT-C-M-0119");
            projectList.add("AK104-IIT-C-N1-0001");
            projectList.add("AK104-IIT-C-N1-0002");
            projectList.add("AK104-IIT-C-N1-0004");
            projectList.add("AK104-IIT-C-N1-0005");
            projectList.add("AK104-IIT-C-N1-0006");
            projectList.add("AK104-IIT-C-N1-0007");
            projectList.add("AK104-IIT-C-N1-0009");
            projectList.add("AK104-IIT-C-N1-0018");
            projectList.add("AK104-IIT-C-N1-0019");
            projectList.add("AK104-IIT-C-N1-0022");
            projectList.add("AK104-IIT-C-N1-0023");
            projectList.add("AK104-IIT-C-N1-0024");
            projectList.add("AK104-IIT-C-N1-0029");
            projectList.add("AK104-IIT-C-N1-0030");
            projectList.add("AK104-IIT-C-N1-0031");
            projectList.add("AK104-IIT-C-N1-0032");
            projectList.add("AK104-IIT-C-N1-0033");
            projectList.add("AK104-IIT-C-N1-0034");
            projectList.add("AK104-IIT-C-N1-0036");
            projectList.add("AK104-IIT-C-N1-0037");
            projectList.add("AK104-IIT-C-N1-0038");
            projectList.add("AK104-IIT-C-N1-0039");
            projectList.add("AK104-IIT-C-N1-0042");
            projectList.add("AK104-IIT-C-N1-0043");
            projectList.add("AK104-IIT-C-N1-0045");
            projectList.add("AK104-IIT-C-N1-0046");
            projectList.add("AK104-IIT-C-N1-0047");
            projectList.add("AK104-IIT-C-N1-0048");
            projectList.add("AK104-IIT-C-N1-0051");
            projectList.add("AK104-IIT-C-N1-0052");
            projectList.add("AK104-IIT-C-N1-0053");
            projectList.add("AK104-IIT-C-N1-0054");
            projectList.add("AK104-IIT-C-N1-0055");
            projectList.add("AK104-IIT-C-N1-0057");
            projectList.add("AK104-IIT-C-N1-0058");
            projectList.add("AK104-IIT-C-N1-0064");
            projectList.add("AK104-IIT-C-N1-0065");
            projectList.add("AK104-IIT-C-N1-0068");
            projectList.add("AK104-IIT-C-N1-0069");
            projectList.add("AK104-IIT-C-N1-0104");
            projectList.add("AK104-IIT-C-N1-0105");
            projectList.add("AK104-IIT-C-N1-0106");
            projectList.add("AK104-IIT-C-N1-0108");
            projectList.add("AK104-IIT-C-N1-0109");
            projectList.add("AK104-IIT-C-N1-0110");
            projectList.add("AK104-IIT-C-N1-0111");
            projectList.add("AK104-IIT-C-N1-0112");
            projectList.add("AK104-IIT-C-N1-0113");
            projectList.add("AK104-IIT-C-N1-0115");
            projectList.add("AK104-IIT-C-N1-0116");
            projectList.add("AK104-IIT-C-N1-0117");
            projectList.add("AK104-IIT-C-N1-0118");
            projectList.add("AK104-IIT-C-N2-0002");
            projectList.add("AK104-IIT-C-N2-0018");
            projectList.add("AK104-IIT-C-N2-0019");
            projectList.add("AK104-IIT-C-N2-0020");
            projectList.add("AK104-IIT-C-N2-0023");
            projectList.add("AK104-IIT-C-N2-0024");
            projectList.add("AK104-IIT-C-N2-0025");
            projectList.add("AK104-IIT-C-N2-0026");
            projectList.add("AK104-IIT-C-N2-0028");
            projectList.add("AK104-IIT-C-N2-0029");
            projectList.add("AK104-IIT-C-N2-0030");
            projectList.add("AK104-IIT-C-N2-0035");
            projectList.add("AK104-IIT-C-N2-0037");
            projectList.add("AK104-IIT-C-N2-0038");
            projectList.add("AK104-IIT-C-N2-0039");
            projectList.add("AK104-IIT-C-N2-0040");
            projectList.add("AK104-IIT-C-N2-0044");
            projectList.add("AK104-IIT-C-N2-0045");
            projectList.add("AK104-IIT-C-N2-0046");
            projectList.add("AK104-IIT-C-N2-0047");
            projectList.add("AK104-IIT-C-N2-0049");
            projectList.add("AK104-IIT-C-N2-0104");
            projectList.add("AK104-IIT-C-N2-0105");
            projectList.add("AK104-IIT-C-N2-0106");
            projectList.add("AK104-IIT-C-N2-0107");
            projectList.add("AK104-IIT-C-N2-0108");
            projectList.add("AK104-IIT-C-N2-0110");
            projectList.add("AK104-IIT-C-N2-0111");
            projectList.add("AK104-IIT-C-N2-0112");
            projectList.add("AK104-IIT-C-N2-0113");
            projectList.add("AK104-IIT-C-N2-0114");
            projectList.add("AK104-IIT-C-N2-0115");
            projectList.add("AK104-IIT-C-N2-0119");
            projectList.add("AK104-IIT-C-N2-0120");
            projectList.add("AK104-IIT-C-N2-0121");
            projectList.add("AK104-IIT-C-N2-0122");
            projectList.add("AK104-IIT-C-N2-0123");
            projectList.add("AK104-IIT-C-N2-0124");
            projectList.add("AK104-IIT-C-N2-0125");
            projectList.add("AK104-IIT-C-N2-0126");
            projectList.add("AK104-IIT-C-N2-0127");
            projectList.add("AK104-IIT-C-N2-0129");
            projectList.add("AK104-IIT-C-N2-0130");
            projectList.add("AK104-IIT-C-N2-0131");
            projectList.add("AK104-IIT-C-N2-0133");
            projectList.add("AK104-IIT-C-N2-0134");
            projectList.add("AK104-IIT-C-N2-0137");
            projectList.add("AK104-IIT-C-N2-0138");
            projectList.add("AK104-IIT-C-N2-0139");
            projectList.add("AK104-IIT-C-N2-0143");
            projectList.add("AK104-IIT-C-N2-0144");
            projectList.add("AK104-IIT-C-N2-0145");
            projectList.add("AK104-IIT-C-N2-0146");
            projectList.add("AK104-IIT-C-S-0002");
            projectList.add("AK104-IIT-C-S-0004");
            projectList.add("AK104-IIT-C-S-0005");
            projectList.add("AK104-IIT-C-S-0007");
            projectList.add("AK104-IIT-C-S-0008");
            projectList.add("AK104-IIT-C-S-0010");
            projectList.add("AK104-IIT-C-S-0011");
            projectList.add("AK104-IIT-C-S-0012");
            projectList.add("AK104-IIT-C-S-0013");
            projectList.add("AK104-IIT-C-S-0014");
            projectList.add("AK104-IIT-C-S-0015");
            projectList.add("AK104-IIT-C-S-0018");
            projectList.add("AK104-IIT-C-S-0019");
            projectList.add("AK104-IIT-C-S-0021");
            projectList.add("AK104-IIT-C-S-0022");
            projectList.add("AK104-IIT-C-S-0023");
            projectList.add("AK104-IIT-C-S-0024");
            projectList.add("AK104-IIT-C-S-0025");
            projectList.add("AK104-IIT-C-S-0026");
            projectList.add("AK104-IIT-C-S-0027");
            projectList.add("AK104-IIT-C-S-0029");
            projectList.add("AK104-IIT-C-S-0030");
            projectList.add("AK104-IIT-C-S-0032");
            projectList.add("AK104-IIT-C-S-0034");
            projectList.add("AK104-IIT-C-S-0035");
            projectList.add("AK104-IIT-C-S-0041");
            projectList.add("AK104-IIT-C-S-0043");
            projectList.add("AK104-IIT-C-S-0044");
            projectList.add("AK104-IIT-C-S-0045");
            projectList.add("AK104-IIT-C-S-0050");
            projectList.add("AK104-IIT-C-S-0051");
            projectList.add("AK104-IIT-C-S-0052");
            projectList.add("AK104-IIT-C-S-0053");
            projectList.add("AK104-IIT-C-S-0055");
            projectList.add("AK104-IIT-C-S-0056");
            projectList.add("AK104-IIT-C-S-0057");
            projectList.add("AK104-IIT-C-S-0059");
            projectList.add("AK104-IIT-C-S-0061");
            projectList.add("AK104-IIT-C-S-0062");
            projectList.add("AK104-IIT-C-S-0063");
            projectList.add("AK104-IIT-C-S-0064");
            projectList.add("AK104-IIT-C-S-0065");
            projectList.add("AK104-IIT-C-S-0068");
            projectList.add("AK104-IIT-C-S-0070");
            projectList.add("AK104-IIT-C-S-0071");
            projectList.add("AK104-IIT-C-S-0074");
            projectList.add("AK104-IIT-C-S-0075");
            projectList.add("AK104-IIT-C-S-0076");
            projectList.add("AK104-IIT-C-S-0078");
            projectList.add("AK104-IIT-C-S-0082");
            projectList.add("AK104-IIT-C-S-0083");
            projectList.add("AK104-IIT-C-S-0085");
            projectList.add("AK104-IIT-C-S-0086");
            projectList.add("AK104-IIT-C-S-0087");
            projectList.add("AK104-IIT-C-S-0088");
            projectList.add("AK104-IIT-C-S-0090");
            projectList.add("AK104-IIT-C-S-0092");
            projectList.add("AK104-IIT-C-S-0093");
            projectList.add("AK104-IIT-C-S-0096");
            projectList.add("AK104-IIT-C-S-0097");
            projectList.add("AK104-IIT-C-S-0098");
            projectList.add("AK104-IIT-C-S-0099");
            projectList.add("AK104-IIT-C-S-0100");
            projectList.add("AK104-IIT-C-S-0101");
            projectList.add("AK104-IIT-C-S-0102");
            projectList.add("AK104-IIT-C-S-0103");
            projectList.add("AK104-IIT-C-S-0105");
            projectList.add("AK104-IIT-C-S-0106");
            projectList.add("AK104-IIT-C-S-0108");
            projectList.add("AK104-IIT-C-S-0110");
            projectList.add("AK104-IIT-C-S-0112");
            projectList.add("AK104-IIT-C-S-0114");
            projectList.add("AK104-IIT-C-S-0117");
            projectList.add("AK104-IIT-C-S-0118");
            projectList.add("AK104-IIT-C-S-0120");
            projectList.add("AK104-IIT-C-S-0122");
            projectList.add("AK104-IIT-C-S-0123");
            projectList.add("AK104-IIT-C-S-0124");
            projectList.add("AK104-IIT-C-S-0125");
            projectList.add("AK104-IIT-C-S-0128");
            projectList.add("AK104-IIT-C-S-0129");
            projectList.add("AK104-IIT-C-W-0019");
            projectList.add("AK104-IIT-C-W-0021");
            projectList.add("AK104-IIT-C-W-0024");
            projectList.add("AK104-IIT-C-W-0025");
            projectList.add("AK104-IIT-C-W-0026");
            projectList.add("AK104-IIT-C-W-0027");
            projectList.add("AK104-IIT-C-W-0029");
            projectList.add("AK104-IIT-C-W-0030");
            projectList.add("AK104-IIT-C-W-0032");
            projectList.add("AK104-IIT-C-W-0035");
            projectList.add("AK104-IIT-C-W-0036");
            projectList.add("AK104-IIT-C-W-0037");
            projectList.add("AK104-IIT-C-W-0040");
            projectList.add("AK104-IIT-C-W-0041");
            projectList.add("AK104-IIT-C-W-0042");
            projectList.add("AK104-IIT-C-W-0043");
            projectList.add("AK104-IIT-C-W-0044");
            projectList.add("AK104-IIT-C-W-0045");
            projectList.add("AK104-IIT-C-W-0047");
            projectList.add("AK104-IIT-C-W-0048");
            projectList.add("AK104-IIT-C-W-0049");
            projectList.add("AK104-IIT-C-W-0050");
            projectList.add("AK104-IIT-C-W-0052");
            projectList.add("AK104-IIT-C-W-0053");
            projectList.add("AK104-IIT-C-W-0054");
            projectList.add("AK104-IIT-C-W-0055");
            projectList.add("AK104-IIT-C-W-0056");
            projectList.add("AK104-IIT-C-W-0057");
            projectList.add("AK104-IIT-C-W-0058");
            projectList.add("AK104-IIT-C-W-0059");
            projectList.add("AK104-IIT-C-W-0106");
            projectList.add("AK104-IIT-C-W-0107");
            projectList.add("AK104-IIT-C-W-0108");
            projectList.add("AK104-IIT-C-W-0109");
            projectList.add("AK104-IIT-C-W-0110");
            projectList.add("AK104-IIT-C-W-0111");
            projectList.add("AK104-IIT-C-W-0115");
            projectList.add("AK104-IIT-C-W-0117");
            projectList.add("AK104-IIT-C-W-0120");
            projectList.add("AK104-IIT-C-W-0122");
            projectList.add("AK104-IIT-C-W-0123");
            projectList.add("AK104-IIT-C-W-0125");
            projectList.add("AK104-IIT-C-W-0127");
            projectList.add("AK104-IIT-C-W-0128");
            projectList.add("AK104-IIT-C-W-0129");
            projectList.add("AK104-IIT-C-W-0132");
            projectList.add("AK104-IIT-C-W-0133");
            projectList.add("AK104-RC48");
            projectList.add("AK104ZY001");
            projectList.add("AK105");
            projectList.add("AK105-101");
            projectList.add("AK105-201");
            projectList.add("AK105-202");
            projectList.add("AK105-203");
            projectList.add("AK105-204");
            projectList.add("AK105-205");
            projectList.add("AK105-301");
            projectList.add("AK105-302");
            projectList.add("AK105-303");
            projectList.add("AK105-304");
            projectList.add("KF051");
            projectList.add("AK105-SAS");
            projectList.add("AK106");
            projectList.add("KF025");
            projectList.add("AK107");
            projectList.add("KF001");
            projectList.add("AK109");
            projectList.add("AK109-101");
            projectList.add("AK109-102");
            projectList.add("AK109-103");
            projectList.add("AK109-201");
            projectList.add("AK109-202");
            projectList.add("AK109-301");
            projectList.add("KF031");
            projectList.add("AK111");
            projectList.add("AK111-101");
            projectList.add("AK111-102");
            projectList.add("AK111-103");
            projectList.add("AK111-201");
            projectList.add("AK111-202");
            projectList.add("AK111-203");
            projectList.add("AK111-301");
            projectList.add("AK111-302");
            projectList.add("AK111-303");
            projectList.add("KF053");
            projectList.add("AK112");
            projectList.add("AK112-101");
            projectList.add("AK112-102");
            projectList.add("AK112-103");
            projectList.add("AK112-104");
            projectList.add("AK112-201");
            projectList.add("AK112-202");
            projectList.add("AK112-203");
            projectList.add("AK112-204");
            projectList.add("AK112-205");
            projectList.add("AK112-206");
            projectList.add("AK112-207");
            projectList.add("AK112-208");
            projectList.add("AK112-209");
            projectList.add("AK112-210");
            projectList.add("AK112-301");
            projectList.add("AK112-302");
            projectList.add("AK112-303");
            projectList.add("AK112-305");
            projectList.add("AK112-306");
            projectList.add("AK112-307");
            projectList.add("AK112-308");
            projectList.add("AK112-309");
            projectList.add("AK112-310");
            projectList.add("AK112-SAS");
            projectList.add("KF064");
            projectList.add("AK112-3003");
            projectList.add("AK112-IIT-001");
            projectList.add("AK112-IIT-002");
            projectList.add("AK112-IIT-004");
            projectList.add("AK112-IIT-006");
            projectList.add("AK112-IIT-007");
            projectList.add("AK112-IIT-009");
            projectList.add("AK112-IIT-C-E-0001");
            projectList.add("AK112-IIT-C-E-0002");
            projectList.add("AK112-IIT-C-E-0003");
            projectList.add("AK112-IIT-C-E-0006");
            projectList.add("AK112-IIT-C-E-0007");
            projectList.add("AK112-IIT-C-M-0001");
            projectList.add("AK112-IIT-C-W-0001");
            projectList.add("AK112ZY001");
            projectList.add("AK114");
            projectList.add("AK114-101");
            projectList.add("AK114-102");
            projectList.add("KF041");
            projectList.add("AK115");
            projectList.add("AK115-101");
            projectList.add("KF059");
            projectList.add("AK117");
            projectList.add("AK117-101");
            projectList.add("AK117-102");
            projectList.add("AK117-103");
            projectList.add("AK117-104");
            projectList.add("AK117-201");
            projectList.add("AK117-202");
            projectList.add("AK117-203");
            projectList.add("AK117-204");
            projectList.add("AK117-205");
            projectList.add("AK117-206");
            projectList.add("AK117-301");
            projectList.add("AK117-302");
            projectList.add("AK117-SAS");
            projectList.add("KF039");
            projectList.add("AK117ZY001");
            projectList.add("AK119");
            projectList.add("AK119-101");
            projectList.add("AK119-102");
            projectList.add("AK119-103");
            projectList.add("AK119-104");
            projectList.add("AK119-105");
            projectList.add("AK119-201");
            projectList.add("AK119-202");
            projectList.add("KF068");
            projectList.add("AK120");
            projectList.add("AK120-101");
            projectList.add("AK120-102");
            projectList.add("AK120-201");
            projectList.add("AK120-202");
            projectList.add("AK120-203");
            projectList.add("AK120-204");
            projectList.add("AK120-205");
            projectList.add("AK120-206");
            projectList.add("AK120-207");
            projectList.add("AK120-301");
            projectList.add("KF070");
            projectList.add("AK127");
            projectList.add("AK127-101");
            projectList.add("AK127-102");
            projectList.add("AK127-103");
            projectList.add("AK127-104");
            projectList.add("AK127ZY001");
            projectList.add("AK129");
            projectList.add("AK129-101");
            projectList.add("AK129-102");
            projectList.add("AK129-103");
            projectList.add("KF083");
            projectList.add("AK130");
            projectList.add("AK130-101");
            projectList.add("AK130-102");
            projectList.add("AK131");
            projectList.add("AK131-101");
            projectList.add("AK132");
            projectList.add("AK132-101");
            projectList.add("AK140N-IIT-001");
            projectList.add("AK140N-IIT-002");
            projectList.add("公摊内部订单（财务和临床专用）");
            projectList.add("AK116");
            projectList.add("AK122");
            projectList.add("AK123");
            projectList.add("AK124");
            projectList.add("AK125");
            projectList.add("AK126");
            projectList.add("AK128");
            projectList.add("AK133");
            projectList.add("AK134");
            projectList.add("AK135");
            projectList.add("AK136");
            projectList.add("AK137");
            projectList.add("AK138");
            projectList.add("AK138D1");
            projectList.add("AK139");
            projectList.add("AK140");
            projectList.add("AK140N");
            projectList.add("AK141");
            projectList.add("AK142(研发项目KF085)");
            projectList.add("AK143");
            projectList.add("AK143(研发项目KF085)");
            projectList.add("AK144");
            projectList.add("AK145");
            projectList.add("AK146");
            projectList.add("AK146D1");
            projectList.add("AK147");
            projectList.add("AK148");
            projectList.add("AK150");
            projectList.add("KF000");
            projectList.add("KF002");
            projectList.add("KF005");
            projectList.add("KF006");
            projectList.add("KF018");
            projectList.add("KF021");
            projectList.add("KF022");
            projectList.add("KF023");
            projectList.add("KF024");
            projectList.add("KF026");
            projectList.add("KF027");
            projectList.add("KF032");
            projectList.add("KF033");
            projectList.add("KF035");
            projectList.add("KF036");
            projectList.add("KF037");
            projectList.add("KF040");
            projectList.add("KF042");
            projectList.add("KF042ZB1");
            projectList.add("KF047");
            projectList.add("KF049");
            projectList.add("KF052");
            projectList.add("KF061");
            projectList.add("KF062");
            projectList.add("KF063");
            projectList.add("KF069");
            projectList.add("KF071");
            projectList.add("KF072");
            projectList.add("KF073");
            projectList.add("KF074");
            projectList.add("KF075");
            projectList.add("KF076");
            projectList.add("KF077");
            projectList.add("KF078");
            projectList.add("KF079");
            projectList.add("KF084");
            projectList.add("KF085");
            projectList.add("KF086");
            projectList.add("KF087");
            projectList.add("KF088");
            projectList.add("KF089");
            projectList.add("KF090");
            projectList.add("KF091");
            projectList.add("KF092");
            projectList.add("KF093");
            projectList.add("KF094");
            projectList.add("KF095");
            projectList.add("KF096");
            projectList.add("KF097");
            projectList.add("KF098");
            projectList.add("KF099");
            projectList.add("KF100");
            projectList.add("KF101");
            projectList.add("KF102");
            projectList.add("KF103");
            projectList.add("KF104");
            projectList.add("KF105");
            projectList.add("KF106");
            projectList.add("KF107");
            projectList.add("KF108");
            projectList.add("KF109");
            projectList.add("SM112-301");
            projectList.add("SPH4336-201 药业联合上药");

            ResearchDevelopmentExpenseSummary r = new ResearchDevelopmentExpenseSummary();
            List<ResearchDevelopmentExpenseSummary> list = researchDevelopmentExpenseSummaryMapper.selectResearchDevelopmentExpenseSummaryList(r);

            int successNum = 0;
            int failureNum = 0;

            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            for (int i = 0; i < yearList.size(); i++) {
                Integer newYear = yearList.get(i);
                for (int x = 0; x < costList.size(); x++) {
                    String cost = costList.get(x);
                    for (int k = 0; k < projectList.size(); k++) {
                        try {
                            String project = projectList.get(k);
                            BigDecimal totalAmount = new BigDecimal(0);
                            if (list.size() > 0) {
                                totalAmount = list.stream()
                                        .filter(a -> a.getAmount() != null)
                                        .filter(a -> a.getYear() != null)
                                        .filter(a -> a.getCostType() != null)
                                        .filter(a -> a.getProjectNumber() != null)
                                        .filter(a -> a.getYear().equals(newYear))
                                        .filter(a -> a.getCostType().equals(cost))
                                        .filter(a -> a.getProjectNumber().equals(project))
                                        .map(ResearchDevelopmentExpenseSummary::getAmount)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            }
                            ResearchDevelopmentExpenseProject rd = new ResearchDevelopmentExpenseProject();
                            rd.setYear(newYear);
                            rd.setAmount(totalAmount);
                            rd.setCostType(cost);
                            rd.setProjectNumber(project);
                            int result = researchDevelopmentExpenseProjectMapper.insertResearchDevelopmentExpenseProject(rd);
                            if (result == 1) {
                                successNum++;
                                successMsg.append("<br/>" + successNum + "、年 " + newYear + "、项目号 " + project + " 添加成功");
                            } else {
                                failureNum++;
                                failureMsg.append("<br/>" + successNum + "、年 " + newYear + "、项目号 " + project + " 添加失败");
                            }
                        } catch (Exception e) {
                            failureNum++;
                            String msg = "<br/>" + successNum + "、年 " + newYear + "、项目号 " + projectList.get(k) + " 添加失败:";
                            failureMsg.append(msg + e.getMessage());
                        }
                    }
                }
            }
            log.info("新增成功：" + successNum + "条--" + successMsg.toString());
            log.error("新增失败：--" + failureNum + "条--" + failureMsg.toString());
        } else {
            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--定时更新研究费用项目汇总表--不在**********服务器执行的定时任务--跳过");
        }
    }
}