package com.akesobio.report.groupFinance.util;


import java.io.IOException;
import java.util.List;

public class ToCBS {
    /**
     * 查询银行实时余额
     * @return
     * @throws IOException
     */
    public static List<Acactinfy> selectAcactinfyList()throws IOException {
        //正式服
        String url = "http://192.168.1.53:1212";

        String CRC32_PASSWORD = "CMBChina2009";
        String CRC32_PREFIX = "Z";
        String key = "123";

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(CRC32_PASSWORD);
        stringBuffer.append(key);
        stringBuffer.append("<?xml version=\"1.0\" encoding=\"GBK\"?>");
        stringBuffer.append("<CBSERPPGK>");
        stringBuffer.append("<INFO>");
        stringBuffer.append("<FUNNAM>ERCURBAL</FUNNAM>");
        stringBuffer.append("</INFO>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027619200073510</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022909114010181</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929114016681</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929238003669</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929200266784</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011000929000068811</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027629200073690</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>671766099030</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>700369080053</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>52730188000010048</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110503</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572132505</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110702</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110901</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110520</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572132201</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110666</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>862003290011</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>484601400013000150667</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>9550880209227900366</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>80020000019014152</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801000001795</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121939911710401</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>110938635910301</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027609200074329</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929200296006</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>727672530768</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910410901</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910410690</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910410702</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910432888</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929200294574</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027609200103048</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900967310301</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>739369947155</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680810802</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680810603</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680810404</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680832301</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760901489410806</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757904616610201</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027609200106029</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781910601</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781910402</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781932802</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781910203</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>97550078801900001213</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>98190078801500002804</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>98190078801700002803</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>98190078814600002795</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>739369147240</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120916475410101</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120916475410803</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801700002200</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>9550880236603700150</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>9550880236603700240</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>660069509824</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801000001858</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>691268728658</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895710222</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895710900</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895710506</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801700001686</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801500001821</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>643169985447</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120919276110701</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120918859010603</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120918859010802</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120918859010205</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286110302</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286110103</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286110501</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286132901</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011000929000068811</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022909114010181</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929114016681</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929200266784</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929238003669</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027619200073510</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027629200073690</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>484601400013000150667</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>52730188000010048</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>641050807</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>671766099030</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>700369080053</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110503</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110520</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110666</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110702</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572110901</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572132201</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757902572132505</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>80020000019014152</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>80020000020856468</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801000001795</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>862003290011</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>9550880209227900366</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>110938635910301</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121939911710401</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680810404</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680810603</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680810802</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944680832301</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781910203</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781910402</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781910601</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>121944781932802</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011000929000106383</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929200294574</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011022929200296006</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027609200074329</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>2011027609200103048</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>31050178360000006659</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>641051352</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>727672530768</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>739369947155</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>757904616610201</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910410690</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910410702</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910410901</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900910432888</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900967310301</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760901489410806</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>97550078801900001213</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>98190078801500002804</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>98190078801700002803</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>98190078814600002795</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286110103</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286110302</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286110501</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120922286132901</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120918859010205</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120918859010802</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120918859010603</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120916475410101</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120916475410803</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120916475432501</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>739369147240</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801700002200</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>9550880236603700150</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>9550880236603700240</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895710506</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895732616</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801100002414</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801500001821</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801700001686</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>691268728658</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895710222</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>760900895710900</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>82150078801000001858</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>660069509824</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>120919276110701</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>643169985447</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>0546000022992601P</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>0546000022992605A</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>054600229926</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>382841100212001</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>382841100212003</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>60112574547</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>60112876561</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>60134312641</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>60134312641-1</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>60188293755</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>60188293755-1</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-208417-838CU-HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-208417-838SA-AUD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-208417-838SA-CNY</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-208417-838SA-HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-208417-838SA-USD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>DQOSA123002049</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>FTN771131851696AUD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>FTN771131851696CNY</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>FTN771131851696HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>FTN771131851696USD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA20110009090100082894</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA2011000914000001926</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA2011000929000083962</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>OSA11443633640424</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");

        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA760900998710102</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA76090099877900019</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA760900998732701</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA76090099877900036</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>NRA76090099877900040</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>OSA760900998732001</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>OSA760900998732001-1</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>OSA760900998732001-2</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>12091647547900048</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>12091647547800305</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");

        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>000000527517392</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>000003796170570</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>000003796170570-1</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>011-267747-001</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>011-267747-159</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>104040840</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>325001575497</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>325033935487</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>33002981595</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>34702109444</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-098115-838/CU-HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-098115-838/SA-CNY</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-098115-838/SA-HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-098115-838/SA-USD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-251987-838/CU-HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-251987-838/SA-AUD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-251987-838/SA-CNY</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-251987-838/SA-HKD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>741-251987-838/SA-USD</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>76W-68G26</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>76W-68G26-1</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");
        stringBuffer.append("<ERCURBALX>");
        stringBuffer.append("<ACTNBR>FTN7609006536665</ACTNBR>");
        stringBuffer.append("</ERCURBALX>");

        stringBuffer.append("</CBSERPPGK>");

        //CRC-32校验
        long crc32 = CRCUtils.calculateCRC32(stringBuffer.toString());
        //System.out.println("crc32=="+crc32);
        // long转十六进制数
        String long16 = Long.toHexString(crc32);
        // System.out.println("long转十六进制数=="+long16);
        //16进制补零
        String xmlData = CRCUtils.getRightLengths(long16);
        //System.out.println("16进制补零=="+xmlData);

        xmlData = CRC32_PREFIX + xmlData.toUpperCase();
        //System.out.println("xmlData="+xmlData);
        String xml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<PGK>\n" +
                "<DATA>\n" +
                "<![CDATA[\n" +
                "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<CBSERPPGK>\n" +
                "<INFO>\n" +
                "<FUNNAM>ERCURBAL</FUNNAM>\n" +
                "</INFO>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027619200073510</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022909114010181</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929114016681</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929238003669</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929200266784</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011000929000068811</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027629200073690</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>671766099030</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>700369080053</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>52730188000010048</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110503</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572132505</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110702</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110901</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110520</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572132201</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110666</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>862003290011</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>484601400013000150667</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>9550880209227900366</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>80020000019014152</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801000001795</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121939911710401</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>110938635910301</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027609200074329</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929200296006</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>727672530768</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910410901</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910410690</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910410702</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910432888</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929200294574</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027609200103048</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900967310301</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>739369947155</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680810802</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680810603</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680810404</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680832301</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760901489410806</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757904616610201</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027609200106029</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781910601</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781910402</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781932802</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781910203</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>97550078801900001213</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>98190078801500002804</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>98190078801700002803</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>98190078814600002795</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>739369147240</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120916475410101</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120916475410803</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801700002200</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>9550880236603700150</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>9550880236603700240</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>660069509824</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801000001858</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>691268728658</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895710222</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895710900</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895710506</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801700001686</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801500001821</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>643169985447</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120919276110701</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120918859010603</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120918859010802</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120918859010205</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286110302</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286110103</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286110501</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286132901</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011000929000068811</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022909114010181</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929114016681</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929200266784</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929238003669</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027619200073510</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027629200073690</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>484601400013000150667</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>52730188000010048</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>641050807</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>671766099030</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>700369080053</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110503</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110520</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110666</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110702</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572110901</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572132201</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757902572132505</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>80020000019014152</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>80020000020856468</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801000001795</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>862003290011</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>9550880209227900366</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>110938635910301</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121939911710401</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680810404</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680810603</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680810802</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944680832301</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781910203</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781910402</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781910601</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>121944781932802</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011000929000106383</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929200294574</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011022929200296006</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027609200074329</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>2011027609200103048</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>31050178360000006659</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>641051352</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>727672530768</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>739369947155</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>757904616610201</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910410690</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910410702</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910410901</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900910432888</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900967310301</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760901489410806</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>97550078801900001213</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>98190078801500002804</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>98190078801700002803</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>98190078814600002795</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286110103</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286110302</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286110501</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120922286132901</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120918859010205</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120918859010802</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120918859010603</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120916475410101</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120916475410803</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120916475432501</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>739369147240</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801700002200</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>9550880236603700150</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>9550880236603700240</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895710506</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895732616</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801100002414</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801500001821</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801700001686</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>691268728658</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895710222</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>760900895710900</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>82150078801000001858</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>660069509824</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>120919276110701</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>643169985447</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>0546000022992601P</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>0546000022992605A</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>054600229926</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>382841100212001</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>382841100212003</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>60112574547</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>60112876561</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>60134312641</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>60134312641-1</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>60188293755</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>60188293755-1</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-208417-838CU-HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-208417-838SA-AUD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-208417-838SA-CNY</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-208417-838SA-HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-208417-838SA-USD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>DQOSA123002049</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>FTN771131851696AUD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>FTN771131851696CNY</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>FTN771131851696HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>FTN771131851696USD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA20110009090100082894</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA2011000914000001926</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA2011000929000083962</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>OSA11443633640424</ACTNBR>\n" +
                "</ERCURBALX>\n" +

                "<ERCURBALX>\n" +
                "<ACTNBR>NRA760900998710102</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA76090099877900019</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA760900998732701</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA76090099877900036</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>NRA76090099877900040</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>OSA760900998732001</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>OSA760900998732001-1</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>OSA760900998732001-2</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>12091647547900048</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>12091647547800305</ACTNBR>\n" +
                "</ERCURBALX>\n" +

                "<ERCURBALX>\n" +
                "<ACTNBR>000000527517392</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>000003796170570</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>000003796170570-1</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>011-267747-001</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>011-267747-159</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>104040840</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>325001575497</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>325033935487</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>33002981595</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>34702109444</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-098115-838/CU-HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-098115-838/SA-CNY</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-098115-838/SA-HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-098115-838/SA-USD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-251987-838/CU-HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-251987-838/SA-AUD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-251987-838/SA-CNY</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-251987-838/SA-HKD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>741-251987-838/SA-USD</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>76W-68G26</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>76W-68G26-1</ACTNBR>\n" +
                "</ERCURBALX>\n" +
                "<ERCURBALX>\n" +
                "<ACTNBR>FTN7609006536665</ACTNBR>\n" +
                "</ERCURBALX>\n" +

                "</CBSERPPGK>\n" +
                "]]>\n" +
                "</DATA>\n" +
                "<CHECKCODE>" + xmlData + "</CHECKCODE>\n" +
                "</PGK>";
        String requestCBS = RequestToCBS.requestCBS(url, xml);
        // System.out.println("requestCBS=" + requestCBS);
        String xmlInfo = requestCBS.substring(requestCBS.indexOf("<CBSERPPGK>"), requestCBS.indexOf("</CBSERPPGK>") + 12);
        //System.out.println("字符串截取值==" + xmlInfo);
        //xml转对象
        Cbserppgk cbserppgk = (Cbserppgk) XMLUtil.convertXmlStrToObject(Cbserppgk.class, xmlInfo);
        // System.out.println("Cbserppgk对象=" + cbserppgk);
        List<Acactinfy> list = cbserppgk.getACACTINFY();
        return  list;
    }

    /**
     * 查询银行历史余额
     * @return
     * @throws IOException
     */
    public static List<Dchblqryz> selectDchblqryzList(String date) throws IOException{
        //正式服
        String url = "http://192.168.1.53:1212";

        String CRC32_PASSWORD = "CMBChina2009";
        String CRC32_PREFIX = "Z";
        String key = "123";

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(CRC32_PASSWORD);
        stringBuffer.append(key);
        stringBuffer.append("<?xml version=\"1.0\" encoding=\"GBK\"?>");
        stringBuffer.append("<CBSERPPGK>");
        stringBuffer.append("<INFO>");
        stringBuffer.append("<FUNNAM>EPQRYHBL</FUNNAM>");
        stringBuffer.append("</INFO>");
        stringBuffer.append("<DCHBLQRYX>");
        stringBuffer.append("<BGNDAT>");
        stringBuffer.append(date);
        stringBuffer.append("</BGNDAT>");
        stringBuffer.append("<ENDDAT>");
        stringBuffer.append(date);
        stringBuffer.append("</ENDDAT>");
        stringBuffer.append("</DCHBLQRYX>");
        stringBuffer.append("</CBSERPPGK>");


        //CRC-32校验
        long crc32 = CRCUtils.calculateCRC32(stringBuffer.toString());
        //System.out.println("crc32=="+crc32);
        // long转十六进制数
        String long16 = Long.toHexString(crc32);
        // System.out.println("long转十六进制数=="+long16);
        //16进制补零
        String xmlData = CRCUtils.getRightLengths(long16);
        //System.out.println("16进制补零=="+xmlData);

        xmlData = CRC32_PREFIX + xmlData.toUpperCase();
        //System.out.println("xmlData="+xmlData);
        String xml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<PGK>\n" +
                "<DATA>\n" +
                "<![CDATA[\n" +
                "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<CBSERPPGK>\n" +
                "<INFO>\n" +
                "<FUNNAM>EPQRYHBL</FUNNAM>\n" +
                "</INFO>\n" +
                "<DCHBLQRYX>\n" +
                "<BGNDAT>" + date + "</BGNDAT>\n" +
                "<ENDDAT>" + date + "</ENDDAT>\n" +
                "</DCHBLQRYX>" +
                "</CBSERPPGK>" +
                "]]>\n" +
                "</DATA>\n" +
                "<CHECKCODE>" + xmlData + "</CHECKCODE>\n" +
                "</PGK>";
        // System.out.println("发送报文数据："+xml);
        String requestCBS = RequestToCBS.requestCBS(url, xml);
        // System.out.println("CBS返回数据=" + requestCBS);
        String xmlInfo = requestCBS.substring(requestCBS.indexOf("<CBSERPPGK>"), requestCBS.indexOf("</CBSERPPGK>") + 12);
        //System.out.println("字符串截取值==" + xmlInfo);
        //xml转对象
        Cbserppgk cbserppgk = (Cbserppgk) XMLUtil.convertXmlStrToObject(Cbserppgk.class, xmlInfo);
        //System.out.println("Cbserppgk对象=" + cbserppgk);
        List<Dchblqryz> list = cbserppgk.getDCHBLQRYZ();
        return list;
    }


}
