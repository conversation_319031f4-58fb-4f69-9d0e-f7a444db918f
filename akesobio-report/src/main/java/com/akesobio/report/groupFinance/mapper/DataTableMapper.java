package com.akesobio.report.groupFinance.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.groupFinance.domain.DataTable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Mapper
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@Mapper
@DataSource(DataSourceType.ABUTMENTDATA)
public interface DataTableMapper extends BaseMapper<DataTable> {
    List<DataTable> queryDataTableList(DataTable o);
    List<String> queryDataTableDISTINCT();
    String queryDataTableByDataName(String dataName);
}