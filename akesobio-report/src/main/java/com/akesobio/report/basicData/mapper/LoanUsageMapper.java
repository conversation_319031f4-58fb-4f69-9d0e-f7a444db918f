package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.LoanUsage;

/**
 * 贷款使用情况Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface LoanUsageMapper {
    /**
     * 查询贷款使用情况
     *
     * @param id 贷款使用情况主键
     * @return 贷款使用情况
     */
    public LoanUsage selectLoanUsageById(Integer id);

    /**
     * 查询贷款使用情况列表
     *
     * @param loanUsage 贷款使用情况
     * @return 贷款使用情况集合
     */
    public List<LoanUsage> selectLoanUsageList(LoanUsage loanUsage);

    /**
     * 新增贷款使用情况
     *
     * @param loanUsage 贷款使用情况
     * @return 结果
     */
    public int insertLoanUsage(LoanUsage loanUsage);

    /**
     * 修改贷款使用情况
     *
     * @param loanUsage 贷款使用情况
     * @return 结果
     */
    public int updateLoanUsage(LoanUsage loanUsage);

    /**
     * 删除贷款使用情况
     *
     * @param id 贷款使用情况主键
     * @return 结果
     */
    public int deleteLoanUsageById(Integer id);

    /**
     * 批量删除贷款使用情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLoanUsageByIds(Integer[] ids);
}
