package com.akesobio.report.procure.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.procure.mapper.HistoricalInventoryReportMapper;
import com.akesobio.report.procure.domain.HistoricalInventoryReport;
import com.akesobio.report.procure.service.IHistoricalInventoryReportService;

import javax.annotation.Resource;

/**
 * SAP历史库存报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-14
 */
@Service
public class HistoricalInventoryReportServiceImpl implements IHistoricalInventoryReportService 
{
    @Resource
    private HistoricalInventoryReportMapper historicalInventoryReportMapper;

    /**
     * 查询SAP历史库存报表
     * 
     * @param sid SAP历史库存报表主键
     * @return SAP历史库存报表
     */
    @Override
    public HistoricalInventoryReport selectHistoricalInventoryReportBySid(Long sid)
    {
        return historicalInventoryReportMapper.selectHistoricalInventoryReportBySid(sid);
    }

    /**
     * 查询SAP历史库存报表列表
     * 
     * @param historicalInventoryReport SAP历史库存报表
     * @return SAP历史库存报表
     */
    @Override
    public List<HistoricalInventoryReport> selectHistoricalInventoryReportList(HistoricalInventoryReport historicalInventoryReport)
    {
        return historicalInventoryReportMapper.selectHistoricalInventoryReportList(historicalInventoryReport);
    }

    /**
     * 新增SAP历史库存报表
     * 
     * @param historicalInventoryReport SAP历史库存报表
     * @return 结果
     */
    @Override
    public int insertHistoricalInventoryReport(HistoricalInventoryReport historicalInventoryReport)
    {
        return historicalInventoryReportMapper.insertHistoricalInventoryReport(historicalInventoryReport);
    }

    /**
     * 修改SAP历史库存报表
     * 
     * @param historicalInventoryReport SAP历史库存报表
     * @return 结果
     */
    @Override
    public int updateHistoricalInventoryReport(HistoricalInventoryReport historicalInventoryReport)
    {
        return historicalInventoryReportMapper.updateHistoricalInventoryReport(historicalInventoryReport);
    }

    /**
     * 批量删除SAP历史库存报表
     * 
     * @param sids 需要删除的SAP历史库存报表主键
     * @return 结果
     */
    @Override
    public int deleteHistoricalInventoryReportBySids(Long[] sids)
    {
        return historicalInventoryReportMapper.deleteHistoricalInventoryReportBySids(sids);
    }

    /**
     * 删除SAP历史库存报表信息
     * 
     * @param sid SAP历史库存报表主键
     * @return 结果
     */
    @Override
    public int deleteHistoricalInventoryReportBySid(Long sid)
    {
        return historicalInventoryReportMapper.deleteHistoricalInventoryReportBySid(sid);
    }
}
