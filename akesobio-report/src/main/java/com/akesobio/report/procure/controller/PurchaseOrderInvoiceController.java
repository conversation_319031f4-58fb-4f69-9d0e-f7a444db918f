package com.akesobio.report.procure.controller;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.EasyExcelUtils;
import com.akesobio.report.procure.domain.PurchaseOrderInvoice;
import com.akesobio.report.procure.service.PurchaseOrderInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 采购订单发票 信息 Controller
 *
 * @Package_Name com.akesobio.report.procure.controller
 * <AUTHOR>
 * @TIME 24-2-29 16:20
 * @Version 1.0
 */
@RestController
@RequestMapping("/procure/PurchaseOrderInvoice")
public class PurchaseOrderInvoiceController extends BaseController {

    @Autowired
    private PurchaseOrderInvoiceService purchaseOrderInvoiceService;

    @PreAuthorize("@ss.hasPermi('procure:PurchaseOrderInvoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(PurchaseOrderInvoice o) {
        startPage();
        List<PurchaseOrderInvoice> list = purchaseOrderInvoiceService.queryPurchaseOrderInvoiceList(o);
        return getDataTable(list);
    }

    /**
     * 导出发票采购列表
     */
    @PreAuthorize("@ss.hasPermi('procure:PurchaseOrderInvoice:export')")
    @Log(title = "发票采购", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PurchaseOrderInvoice o) {
        try {
            List<PurchaseOrderInvoice> list = purchaseOrderInvoiceService.queryPurchaseOrderInvoiceList(o);
            EasyExcelUtils.downloadFragmentation2(response, list, "采购订单发票信息", 2, 1);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

}