package com.akesobio.report.marketingFinance.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.akesobio.common.utils.collection.CollectionUtils;
import com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.redisson.Redisson;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.akesobio.report.marketingFinance.mapper.AddedTaxInvoiceMapper;
import com.akesobio.report.marketingFinance.domain.AddedTaxInvoice;
import com.akesobio.report.marketingFinance.service.IAddedTaxInvoiceService;

import javax.annotation.Resource;

/**
 * 增值税票Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-09
 */
@Service
public class AddedTaxInvoiceServiceImpl extends ServiceImpl<AddedTaxInvoiceMapper, AddedTaxInvoiceVo> implements IAddedTaxInvoiceService {
    @Resource
    private AddedTaxInvoiceMapper addedTaxInvoiceMapper;

    @Resource
    private RedisTemplate redisTemplate;
    private static final String LINK = "http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=${fdId}";
    private static final String REDIS_KEY = "addedTaxInvoices";

    /**
     * 查询增值税票列表
     *
     * @param addedTaxInvoice 增值税票
     * @return 增值税票
     */
    @Override

    public List<AddedTaxInvoice> selectAddedTaxInvoiceList(AddedTaxInvoice addedTaxInvoice) {
        return addedTaxInvoiceMapper.selectAddedTaxInvoiceList(addedTaxInvoice);
    }

    @Override
    public List<AddedTaxInvoiceVo> queryInvoiceFeeType(AddedTaxInvoiceVo invoiceList) {

        if (invoiceList.getSaleName() != null && !invoiceList.getSaleName().isEmpty()) {
            List<AddedTaxInvoiceVo> list = addedTaxInvoiceMapper.selectAddedTaxInvoice(invoiceList.getSaleName());
            List<AddedTaxInvoiceVo> addedTaxInvoiceVos = addedTaxInvoiceMapper.queryInvoiceFeeList(invoiceList);

            // 使用 Map 存储 list 中的 AddedTaxInvoiceVo 对象
            Map<String, AddedTaxInvoiceVo> map = list.stream()
                    .collect(Collectors.toMap(
                            vo -> vo.getInvoiceNo() + "_" + vo.getInvoiceId(),
                            vo -> vo,
                            (existing, replacement) -> existing
                    ));

            List<AddedTaxInvoiceVo> newList = addedTaxInvoiceVos.stream()
                    .map(invoice -> {
                        String key = invoice.getInvoiceNo() + "_" + invoice.getInvoiceId();
                        AddedTaxInvoiceVo vo = map.get(key);
                        if (vo != null) {
                            invoice.setFeeType(vo.getFeeType());
                            invoice.setOaNum(vo.getOaNum());
                            invoice.setCreateDate(vo.getCreateDate());
                            invoice.setLink(LINK.replace("${fdId}", vo.getFdId()));
                        }
                        return invoice;
                    })
                    .collect(Collectors.toList());


            return newList;
        } else {
            // 查询出发票号、费用类型、oa单号、单据创建时间
            List<AddedTaxInvoiceVo> addedTaxInvoiceVos = addedTaxInvoiceMapper.queryInvoiceFeeList(invoiceList);
            // 拿OA发票去发票库里查询返回信息
            List<AddedTaxInvoiceVo> invoiceVo = addedTaxInvoiceMapper.selectList(addedTaxInvoiceVos);
            List<AddedTaxInvoiceVo> list = new ArrayList<>();
            if (invoiceVo.size() > 0) {
                list = invoiceVo.parallelStream().peek(invoice -> {
                    String invoiceNo = invoice.getInvoiceNo();
                    String invoiceId = invoice.getInvoiceId();
                    for (AddedTaxInvoiceVo vo : addedTaxInvoiceVos) {
                        if (Objects.equals(vo.getInvoiceNo(), invoiceNo) && Objects.equals(vo.getInvoiceId(), invoiceId)) {
                            invoice.setFeeType(vo.getFeeType());
                            invoice.setOaNum(vo.getOaNum());
                            invoice.setCreateDate(vo.getCreateDate());
                            invoice.setLink(LINK.replace("${fdId}", vo.getFdId()));
                        }
                    }
                }).collect(Collectors.toList());
            }
            return list;
//            // 查询出发票号、费用类型、oa单号、单据创建时间
//            long startTime = System.nanoTime();
//            List<AddedTaxInvoiceVo> addedTaxInvoiceVos = addedTaxInvoiceMapper.queryInvoiceFeeList(invoiceList);
//
//            long endTime = System.nanoTime();
//            long duration = (endTime - startTime);  // 单位为纳秒
//            System.out.println("addedTaxInvoiceVos代码执行时间: " + duration / 1_000_000 + " 毫秒");
//            // 使用Map来存储发票信息，以便快速查找
//            Map<String, AddedTaxInvoiceVo> invoiceMap = addedTaxInvoiceVos.stream()
//                    .collect(Collectors.toMap(
//                            vo -> vo.getInvoiceNo() + "_" + vo.getInvoiceId(),
//                            vo -> vo,
//                            (existing, replacement) -> existing
//                    ));
//            long startTimeB = System.nanoTime();
//            // 拿OA发票去发票库里查询返回信息
//            List<AddedTaxInvoiceVo> invoiceVo = addedTaxInvoiceMapper.selectList(addedTaxInvoiceVos);
//
//            List<AddedTaxInvoiceVo> list = new ArrayList<>();
//            if (!invoiceVo.isEmpty()) {
//                list = invoiceVo.parallelStream().peek(invoice -> {
//                    String key = invoice.getInvoiceNo() + "_" + invoice.getInvoiceId();
//                    AddedTaxInvoiceVo vo = invoiceMap.get(key);
//                    if (vo != null) {
//                        invoice.setFeeType(vo.getFeeType());
//                        invoice.setOaNum(vo.getOaNum());
//                        invoice.setCreateDate(vo.getCreateDate());
//                        invoice.setLink(LINK.replace("${fdId}", vo.getFdId()));
//                    }
//                }).collect(Collectors.toList());
//            }
//            long endTimeB = System.nanoTime();
//            long durationB = (endTimeB - startTimeB);  // 单位为纳秒
//            System.out.println("invoiceVo代码执行时间: " + durationB / 1_000_000 + " 毫秒");
//            return list;ss
        }
    }



//        long startTime = System.currentTimeMillis();
//
//        List<AddedTaxInvoiceVo> addedTaxInvoiceVos = addedTaxInvoiceMapper.queryInvoiceFeeList(invoiceList);
//        List<AddedTaxInvoiceVo> list = new ArrayList<>();
//        List<AddedTaxInvoiceVo> list3 = new ArrayList<>();
//
//        if (!addedTaxInvoiceVos.isEmpty()) {
//            int batchSize = 1000;
//            int totalSize = addedTaxInvoiceVos.size();
//            int batchCount = (totalSize + batchSize - 1) / batchSize;
//
//            for (int i = 0; i < batchCount; i++) {
//                int start = i * batchSize;
//                int end = Math.min((i + 1) * batchSize, totalSize);
//
//                List<AddedTaxInvoiceVo> subList = addedTaxInvoiceVos.subList(start, end);
//                list.addAll(addedTaxInvoiceMapper.selectList(subList));
//            }
//
//            // 过滤 null 值
//            List<AddedTaxInvoiceVo> temp = list.stream()
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//
//            System.out.println(temp.size());
//
//            // 转换为 Map
//            Map<String, AddedTaxInvoiceVo> map = list.stream()
//                    .collect(Collectors.toMap(
//                            AddedTaxInvoiceVo::getInvoiceNo,
//                            Function.identity(),
//                            (oldValue, newValue) -> oldValue // 解决 key 冲突
//                    ));
//
//            // 更新数据
//            addedTaxInvoiceVos.forEach(invoiceVo -> {
//                if (map.containsKey(invoiceVo.getInvoiceNo())) {
//                    AddedTaxInvoiceVo vo = map.get(invoiceVo.getInvoiceNo());
//                    vo.setFeeType(invoiceVo.getFeeType());
//                    vo.setOaNum(invoiceVo.getOaNum());
//                    vo.setCreateDate(invoiceVo.getCreateDate());
//                    list3.add(vo);
//                }
//            });
//        }
//
//        long endTime = System.currentTimeMillis();
//        System.out.println("花费时间：" + (endTime - startTime) + "ms");
    @Override
    public List<AddedTaxInvoiceVo> downloadInvoice(AddedTaxInvoiceVo invoiceList) {
        List<AddedTaxInvoiceVo> addedTaxInvoiceVos = addedTaxInvoiceMapper.queryInvoiceFeeList(invoiceList);
        List<AddedTaxInvoiceVo> list = new ArrayList<>();

        if (addedTaxInvoiceVos.size() > 0) {
            int batchSize = 1000;
            int batchCount = (addedTaxInvoiceVos.size() + batchSize - 1) / batchSize;

            for (int i = 0; i < batchCount; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min(fromIndex + batchSize, addedTaxInvoiceVos.size());
                List<AddedTaxInvoiceVo> subList = addedTaxInvoiceVos.subList(fromIndex, toIndex);
                list.addAll(addedTaxInvoiceMapper.selectList(subList));
            }

            Map<String, AddedTaxInvoiceVo> map = CollectionUtils.convertMap(list, AddedTaxInvoiceVo::getInvoiceNo);

            List<AddedTaxInvoiceVo> list3 = Collections.synchronizedList(new ArrayList<>());
            addedTaxInvoiceVos.parallelStream().forEach(invoiceVo -> {
                if (map.containsKey(invoiceVo.getInvoiceNo())) {
                    AddedTaxInvoiceVo vo = map.get(invoiceVo.getInvoiceNo());
                    vo.setFeeType(invoiceVo.getFeeType());
                    vo.setOaNum(invoiceVo.getOaNum());
                    vo.setCreateDate(invoiceVo.getCreateDate());
                    list3.add(vo);
                }
            });
            return list3;
        }else {
            return list;
        }
    }

    public List<AddedTaxInvoiceVo> downloadInvoice2(AddedTaxInvoiceVo invoiceList) {
        long l = System.currentTimeMillis();
        List<AddedTaxInvoiceVo> addedTaxInvoiceVos = addedTaxInvoiceMapper.queryInvoiceFeeList(invoiceList);
        List<AddedTaxInvoiceVo> list = new ArrayList<>();
        List<AddedTaxInvoiceVo> list3 = Collections.synchronizedList(new ArrayList<>());
        if (addedTaxInvoiceVos.size() > 0) {
            int batch = addedTaxInvoiceVos.size() / 1000;
            if (addedTaxInvoiceVos.size() % 1000 != 0) {
                batch = batch + 1;
            }

            for (int i = 0; i < batch; i++) {
                List<AddedTaxInvoiceVo> subList;
                if (i == batch - 1) {
                    subList = addedTaxInvoiceVos.subList(1000 * i, addedTaxInvoiceVos.size());
                } else {
                    subList = addedTaxInvoiceVos.subList(1000 * i, 1000 * (i + 1));
                }
                list.addAll(addedTaxInvoiceMapper.selectList(subList));
            }
            List<AddedTaxInvoiceVo> temp = CollectionUtils.filterList(list, Objects::isNull);
            System.out.println(temp.size());
            Map<String, AddedTaxInvoiceVo> map = CollectionUtils.convertMap(list, AddedTaxInvoiceVo::getInvoiceNo);
            addedTaxInvoiceVos.parallelStream().forEach(invoiceVo -> {
                if (map.containsKey(invoiceVo.getInvoiceNo())){
                    AddedTaxInvoiceVo vo = map.get(invoiceVo.getInvoiceNo());
                    vo.setFeeType(invoiceVo.getFeeType());
                    vo.setOaNum(invoiceVo.getOaNum());
                    vo.setCreateDate(invoiceVo.getCreateDate());
                    list3.add(vo);
                }
            });
        }
        long a = System.currentTimeMillis();
        System.out.println("花费时间：" + (a - l) + "ms");
        return list3;
    }

}
















