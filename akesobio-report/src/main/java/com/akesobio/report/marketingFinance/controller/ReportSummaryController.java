package com.akesobio.report.marketingFinance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.marketingFinance.domain.ReportSummary;
import com.akesobio.report.marketingFinance.service.IReportSummaryService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 报表合计Controller
 * 
 * <AUTHOR>
 * @date 2023-06-08
 */
@RestController
@RequestMapping("/marketingFinance/reportSummary")
public class ReportSummaryController extends BaseController
{
    @Autowired
    private IReportSummaryService reportSummaryService;

    /**
     * 查询报表合计列表
     */
    @PreAuthorize("@ss.hasPermi('marketingFinance:reportSummary:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReportSummary reportSummary)
    {
        startPage();
        List<ReportSummary> list = reportSummaryService.selectReportSummaryList(reportSummary);
        return getDataTable(list);
    }

    /**
     * 导出报表合计列表
     */
    @PreAuthorize("@ss.hasPermi('marketingFinance:reportSummary:export')")
    @Log(title = "报表合计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ReportSummary reportSummary)
    {
        List<ReportSummary> list = reportSummaryService.selectReportSummaryList(reportSummary);
        ExcelUtil<ReportSummary> util = new ExcelUtil<ReportSummary>(ReportSummary.class);
        util.exportExcel(response, list, "报表合计数据");
    }
}
