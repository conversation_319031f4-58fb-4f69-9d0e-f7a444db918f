package com.akesobio.report.marketingFinance.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.marketingFinance.domain.ResignationApprovalTrackingVO;
import com.akesobio.report.marketingFinance.domain.query.ResignationApprovalTrackingQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 离职审批追踪报表Mapper接口
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Mapper
@DataSource(DataSourceType.CPKFSW)
public interface ResignationApprovalTrackingMapper {

    /**
     * 查询离职审批追踪报表列表
     *
     * @param query 查询条件
     * @return 离职审批追踪报表列表
     */
    List<ResignationApprovalTrackingVO> selectResignationApprovalTrackingList(@Param("query") ResignationApprovalTrackingQuery query);

    /**
     * 分页查询离职审批追踪报表列表
     *
     * @param page 分页对象
     * @param query 查询条件
     * @return 离职审批追踪报表分页列表
     */
    Page<ResignationApprovalTrackingVO> selectResignationApprovalTrackingList(@Param("page") Page<ResignationApprovalTrackingVO> page, @Param("query") ResignationApprovalTrackingQuery query);
}