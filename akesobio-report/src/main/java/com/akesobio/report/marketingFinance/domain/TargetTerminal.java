package com.akesobio.report.marketingFinance.domain;

import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 2023报表数据-目标终端对象 target_terminal
 *
 * <AUTHOR>
 * @date 2023-06-09
 */
public class TargetTerminal extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 表单名称
     */
    @Excel(name = "表单名称")
    private String tableName;

    /**
     * OA单号
     */
    @Excel(name = "OA单号", mergeLine = "1,20,27,28,29")
    private String OANumber;

    /**
     * 申请日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请日期")
    private String applicationDate;

    /**
     * 申请人
     */
    @Excel(name = "申请人")
    private String applicant;

    /**
     * 申请人工号
     */
    @Excel(name = "申请人工号")
    private String applicantJobNumber;

    /**
     * 报销人
     */
    @Excel(name = "报销人")
    private String reimbursementPerson;

    /**
     * 文档状态
     */
    @Excel(name = "文档状态")
    private String currentSession;

    /**
     * 当前环节
     */
    @Excel(name = "当前环节")
    private String documentStatus;
    /**
     * 当前处理人
     */
    @Excel(name = "当前处理人")
    private String currentProcessor;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审批时间")
    private String approvalTime;

    /**
     * 费用归属
     */
    @Excel(name = "费用归属")
    private String expenses;

    /**
     * 所属部门
     */
    @Excel(name = "所属部门")
    private String department;

    /**
     * 会议形式
     */
    @Excel(name = "会议形式")
    private String meetingFormat;

    /**
     * 会议开始时间
     */
    @Excel(name = "会议开始时间")
    private String meetingStartTime;

    /**
     * 会议结束时间
     */
    @Excel(name = "会议结束时间")
    private String meetingEndTime;

    /**
     * 会议城市
     */
    @Excel(name = "会议城市")
    private String conferenceCity;

    /**
     * 会议类型
     */
    @Excel(name = "会议类型")
    private String meetingType;

    /**
     * 会议类别/费用类型/报销类型
     */
    @Excel(name = "会议类别/费用类型/报销类型")
    private String meetingCategory;

    /**
     * 费用类型-2024
     */
    @Excel(name = "费用类型-2024")
    private String costType2024;

    /**
     * 业务类型-2024
     */
    @Excel(name = "业务类型-2024")
    private String businessType2024;

    /**
     * 报销金额合计
     */
    @Excel(name = "报销金额合计")
    private String totalReimbursementAmount;

    /**
     * 目标终端编码
     */
    @Excel(name = "目标终端编码")
    private String targetTerminalCode;

    /**
     * 目标终端
     */
    @Excel(name = "目标终端")
    private String targetTerminal;

    /**
     * 目标终端分摊金额
     */
    @Excel(name = "目标终端分摊金额")
    private String targetTerminalAllocatedAmount;

    /**
     * 实际销售数量
     */
    @Excel(name = "实际销售数量")
    private String actualSalesQuantity;

    /**
     * 所属期
     */
    @Excel(name = "所属期")
    private String period;

    /**
     * 是否冲销借款
     */
    @Excel(name = "是否冲销借款")
    private String isWhether;

    /**
     * 借款单号
     */
    @Excel(name = "借款单号")
    private String loanNumber;

    /**
     * 借款金额
     */
    @Excel(name = "借款金额")
    private String loanAmount;

    /**
     * 会议申请单号
     */
    @Excel(name = "会议申请单号")
    private String conferenceApplicationNo;


    /**
     * 会议目的
     */
    @Excel(name = "会议名称")
    private String meetingObjective;

    /**
     * 中央/区域原会议申请号
     */
    @Excel(name = "中央/区域原会议申请号")
    private String applicationNumber;

    /**
     * SAP应付凭证号
     */
    @Excel(name = "SAP应付凭证号")
    private String sapPayNumber;

    /**
     * 适应症
     */
    @Excel(name = "适应症")
    private String indication;

    /**
     * 药房/经销商
     */
    @Excel(name = "药房/经销商")
    private String pharmacyDealer;

    /**
     * 费用说明
     */
    @Excel(name = "费用说明")
    private String costDescription;

    /**
     * 合同审批单
     */
    @Excel(name = "合同审批单")
    private String contractApprovalForm;

    /**
     * 对公合作单位
     */
    @Excel(name = "对公合作单位")
    private String corporateCooperationUnit;

    /**
     * 对公预付凭证号
     */
    @Excel(name = "对公预付凭证号")
    private String prepaidVoucherNumber;

    /**
     * 业务审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务审批时间")
    private String businessApprovalTime;

    /**
     * 剩余可用额度
     */
    @Excel(name = "剩余可用额度")
    private String availableCredit;

    /**
     * 项目号
     */
    @Excel(name = "项目号")
    private String projectNumber;


    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public String getAvailableCredit() {
        return availableCredit;
    }

    public void setAvailableCredit(String availableCredit) {
        this.availableCredit = availableCredit;
    }

    public String getPrepaidVoucherNumber() {
        return prepaidVoucherNumber;
    }

    public void setPrepaidVoucherNumber(String prepaidVoucherNumber) {
        this.prepaidVoucherNumber = prepaidVoucherNumber;
    }

    public String getBusinessApprovalTime() {
        return businessApprovalTime;
    }

    public void setBusinessApprovalTime(String businessApprovalTime) {
        this.businessApprovalTime = businessApprovalTime;
    }


    public String getContractApprovalForm() {
        return contractApprovalForm;
    }

    public void setContractApprovalForm(String contractApprovalForm) {
        this.contractApprovalForm = contractApprovalForm;
    }

    public String getCorporateCooperationUnit() {
        return corporateCooperationUnit;
    }

    public void setCorporateCooperationUnit(String corporateCooperationUnit) {
        this.corporateCooperationUnit = corporateCooperationUnit;
    }

    public String getCostType2024() {
        return costType2024;
    }

    public void setCostType2024(String costType2024) {
        this.costType2024 = costType2024;
    }

    public String getBusinessType2024() {
        return businessType2024;
    }

    public void setBusinessType2024(String businessType2024) {
        this.businessType2024 = businessType2024;
    }

    public String getCostDescription() {
        return costDescription;
    }

    public void setCostDescription(String costDescription) {
        this.costDescription = costDescription;
    }

    public String getIndication() {
        return indication;
    }

    public void setIndication(String indication) {
        this.indication = indication;
    }

    public String getPharmacyDealer() {
        return pharmacyDealer;
    }

    public void setPharmacyDealer(String pharmacyDealer) {
        this.pharmacyDealer = pharmacyDealer;
    }

    public String getSapPayNumber() {
        return sapPayNumber;
    }

    public void setSapPayNumber(String sapPayNumber) {
        this.sapPayNumber = sapPayNumber;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setOANumber(String OANumber) {
        this.OANumber = OANumber;
    }

    public String getOANumber() {
        return OANumber;
    }

    public void setApplicationDate(String applicationDate) {
        this.applicationDate = applicationDate;
    }

    public String getApplicationDate() {
        return applicationDate;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicantJobNumber(String applicantJobNumber) {
        this.applicantJobNumber = applicantJobNumber;
    }

    public String getApplicantJobNumber() {
        return applicantJobNumber;
    }

    public void setReimbursementPerson(String reimbursementPerson) {
        this.reimbursementPerson = reimbursementPerson;
    }

    public String getReimbursementPerson() {
        return reimbursementPerson;
    }

    public void setCurrentSession(String currentSession) {
        this.currentSession = currentSession;
    }

    public String getCurrentSession() {
        return currentSession;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getDocumentStatus() {
        return documentStatus;
    }

    public void setApprovalTime(String approvalTime) {
        this.approvalTime = approvalTime;
    }

    public String getApprovalTime() {
        return approvalTime;
    }

    public void setExpenses(String expenses) {
        this.expenses = expenses;
    }

    public String getExpenses() {
        return expenses;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartment() {
        return department;
    }

    public void setMeetingFormat(String meetingFormat) {
        this.meetingFormat = meetingFormat;
    }

    public String getMeetingFormat() {
        return meetingFormat;
    }

    public void setMeetingStartTime(String meetingStartTime) {
        this.meetingStartTime = meetingStartTime;
    }

    public String getMeetingStartTime() {
        return meetingStartTime;
    }

    public void setMeetingEndTime(String meetingEndTime) {
        this.meetingEndTime = meetingEndTime;
    }

    public String getMeetingEndTime() {
        return meetingEndTime;
    }

    public void setConferenceCity(String conferenceCity) {
        this.conferenceCity = conferenceCity;
    }

    public String getConferenceCity() {
        return conferenceCity;
    }

    public void setMeetingType(String meetingType) {
        this.meetingType = meetingType;
    }

    public String getMeetingType() {
        return meetingType;
    }

    public void setMeetingCategory(String meetingCategory) {
        this.meetingCategory = meetingCategory;
    }

    public String getMeetingCategory() {
        return meetingCategory;
    }

    public void setTotalReimbursementAmount(String totalReimbursementAmount) {
        this.totalReimbursementAmount = totalReimbursementAmount;
    }

    public String getTotalReimbursementAmount() {
        return totalReimbursementAmount;
    }

    public void setTargetTerminalCode(String targetTerminalCode) {
        this.targetTerminalCode = targetTerminalCode;
    }

    public String getTargetTerminalCode() {
        return targetTerminalCode;
    }

    public void setTargetTerminal(String targetTerminal) {
        this.targetTerminal = targetTerminal;
    }

    public String getTargetTerminal() {
        return targetTerminal;
    }

    public void setTargetTerminalAllocatedAmount(String targetTerminalAllocatedAmount) {
        this.targetTerminalAllocatedAmount = targetTerminalAllocatedAmount;
    }

    public String getTargetTerminalAllocatedAmount() {
        return targetTerminalAllocatedAmount;
    }

    public void setActualSalesQuantity(String actualSalesQuantity) {
        this.actualSalesQuantity = actualSalesQuantity;
    }

    public String getActualSalesQuantity() {
        return actualSalesQuantity;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getPeriod() {
        return period;
    }

    public void setIsWhether(String isWhether) {
        this.isWhether = isWhether;
    }

    public String getIsWhether() {
        return isWhether;
    }

    public void setLoanNumber(String loanNumber) {
        this.loanNumber = loanNumber;
    }

    public String getLoanNumber() {
        return loanNumber;
    }

    public void setLoanAmount(String loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getLoanAmount() {
        return loanAmount;
    }

    public void setConferenceApplicationNo(String conferenceApplicationNo) {
        this.conferenceApplicationNo = conferenceApplicationNo;
    }

    public String getConferenceApplicationNo() {
        return conferenceApplicationNo;
    }

    public void setCurrentProcessor(String currentProcessor) {
        this.currentProcessor = currentProcessor;
    }

    public String getCurrentProcessor() {
        return currentProcessor;
    }

    public void setMeetingObjective(String meetingObjective) {
        this.meetingObjective = meetingObjective;
    }

    public String getMeetingObjective() {
        return meetingObjective;
    }

    public void setApplicationNumber(String applicationNumber) {
        this.applicationNumber = applicationNumber;
    }

    public String getApplicationNumber() {
        return applicationNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("tableName", getTableName())
                .append("OANumber", getOANumber())
                .append("applicationDate", getApplicationDate())
                .append("applicant", getApplicant())
                .append("applicantJobNumber", getApplicantJobNumber())
                .append("reimbursementPerson", getReimbursementPerson())
                .append("currentSession", getCurrentSession())
                .append("documentStatus", getDocumentStatus())
                .append("approvalTime", getApprovalTime())
                .append("expenses", getExpenses())
                .append("department", getDepartment())
                .append("meetingFormat", getMeetingFormat())
                .append("meetingStartTime", getMeetingStartTime())
                .append("meetingEndTime", getMeetingEndTime())
                .append("conferenceCity", getConferenceCity())
                .append("meetingType", getMeetingType())
                .append("meetingCategory", getMeetingCategory())
                .append("costType2024", getCostType2024())
                .append("businessType2024", getBusinessType2024())
                .append("totalReimbursementAmount", getTotalReimbursementAmount())
                .append("targetTerminalCode", getTargetTerminalCode())
                .append("targetTerminal", getTargetTerminal())
                .append("targetTerminalAllocatedAmount", getTargetTerminalAllocatedAmount())
                .append("actualSalesQuantity", getActualSalesQuantity())
                .append("period", getPeriod())
                .append("isWhether", getIsWhether())
                .append("loanNumber", getLoanNumber())
                .append("loanAmount", getLoanAmount())
                .append("conferenceApplicationNo", getConferenceApplicationNo())
                .append("currentProcessor", getCurrentProcessor())
                .append("meetingObjective", getMeetingObjective())
                .append("applicationNumber", getApplicationNumber())
                .append("sapPayNumber", getSapPayNumber())
                .append("indication", getIndication())
                .append("pharmacyDealer", getPharmacyDealer())
                .append("costDescription", getCostDescription())
                .append("contractApprovalForm", getContractApprovalForm())
                .append("corporateCooperationUnit", getCorporateCooperationUnit())
                .append("prepaidVoucherNumber", getPrepaidVoucherNumber())
                .append("businessApprovalTime", getBusinessApprovalTime())
                .append("availableCredit", getAvailableCredit())
                .append("projectNumber", getProjectNumber())
                .toString();
    }
}
