package com.akesobio.report.costControlApi.domain;

import lombok.Data;

import java.util.List;

/**
 * 供应商主数据
 */
@Data
public class SupplierMasterData {
    /**
     * 供应商代码
     */
    private String code;
    /**
     * 供应商名称
     */
    private String supplier_name;
    /**
     * 启用标志（Y/N，新增时默认为Y）
     */
    private String enabled_flag;
    /**
     * 是否设置所属分公司标志（Y/N，默认为Y，置为N则清空当前分公司配置，重置为默认状态即所有分公司可用）
     */
    private String branch_code_flag;
    /**
     * 所属分公司代码集合（branch_code_flag为Y时生效，集合为空时仍为默认状态即所有分公司可用）
     */
    private List<String> branch_code;
    /**
     * 供应商标识
     */
    private String column1;
}
