import request from '@/utils/request'

// 查询虚拟岗校验规则配置列表
export function listMqConfig(query) {
  return request({
    url: '/system/mqconfig/list',
    method: 'get',
    params: query
  })
}

// 查询虚拟岗校验规则配置详细
export function getMqConfig(id) {
  return request({
    url: '/system/mqconfig/' + id,
    method: 'get'
  })
}

// 根据接口名称查询配置信息
export function getMqConfigByInterfaceName(interfaceName) {
  return request({
    url: '/system/mqconfig/interfaceName/' + interfaceName,
    method: 'get'
  })
}

// 新增虚拟岗校验规则配置
export function addMqConfig(data) {
  return request({
    url: '/system/mqconfig',
    method: 'post',
    data: data
  })
}

// 修改虚拟岗校验规则配置
export function updateMqConfig(data) {
  return request({
    url: '/system/mqconfig',
    method: 'put',
    data: data
  })
}

// 删除虚拟岗校验规则配置
export function delMqConfig(id) {
  return request({
    url: '/system/mqconfig/' + id,
    method: 'delete'
  })
}

// 导出虚拟岗校验规则配置
export function exportMqConfig(query) {
  return request({
    url: '/system/mqconfig/export',
    method: 'post',
    params: query
  })
}

// 校验接口名称唯一性
export function checkInterfaceNameUnique(data) {
  return request({
    url: '/system/mqconfig/checkInterfaceNameUnique',
    method: 'post',
    data: data
  })
}
