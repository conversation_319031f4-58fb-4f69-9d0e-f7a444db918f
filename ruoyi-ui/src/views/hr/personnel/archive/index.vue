<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="工号" prop="loginName">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生日日期">
        <el-date-picker
          ref="birthdayDatePicker"
          v-model="dateRangeBirthday"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(1)"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="岗位" prop="post">
        <el-input
          v-model="queryParams.post"
          placeholder="请输入岗位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptNameAll">
        <el-input
          v-model="queryParams.deptNameAll"
          placeholder="请输入部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入职日期">
        <el-date-picker
          ref="entryDatePicker"
          v-model="dateRangeEntryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(2)"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="职级" prop="jobGrade">
        <el-input
          v-model="queryParams.jobGrade"
          placeholder="请输入职级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学历" prop="highestEducation">
        <el-input
          v-model="queryParams.highestEducation"
          placeholder="请输入学历"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转正日期">
        <el-date-picker
          ref="positiveDatePicker"
          v-model="dateRangePositiveDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(3)"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.employeeStatus" placeholder="请选择"
                   style="width: 150px">
          <el-option
            v-for="item in statusOptions"
            :key="item.id"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合同到期日期" label-width="150px">
        <el-date-picker
          ref="contractDatePicker"
          v-model="dateRangeContractDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(4)"
        ></el-date-picker>
      </el-form-item>
      <!--      <el-form-item style="width: 37%"/>-->
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-has-permi="['hr:pm:archive:import']"
        >导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:pm:archive:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border max-height="510" :data="archiveList" @row-click="selectDetail"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name" key="name" v-if="columns[1].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" fixed align="center" prop="jobNumber" key="loginName" v-if="columns[0].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="公司" align="center" prop="orgName" key="orgName" v-if="columns[2].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" key="firstDept" v-if="columns[3].visible"
                       width="150" :show-overflow-tooltip='true'/>
      <el-table-column label="直接上级" align="center" prop="leaderName" key="leaderName" v-if="columns[5].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="分管VP" align="center" prop="vp" key="vp" v-if="columns[72].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" key="post" v-if="columns[4].visible" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="职级" align="center" prop="jobGrade" key="jobGrade" v-if="columns[19].visible" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="状态" align="center" prop="employeeStatus" key="employeeStatus" v-if="columns[7].visible"
                       width="50"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="类型" align="center" prop="employeeType" key="employeeType" v-if="columns[74].visible"
                       width="50"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" key="entryDate" v-if="columns[6].visible"
                       width="120" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转正日期" align="center" prop="positiveDate" key="fdPositiveTime" width="120"
                       v-if="columns[8].visible" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fdPositiveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试用期限/月" align="center" prop="fdTrialOperationPeriod" key="fdTrialOperationPeriod"
                       v-if="columns[20].visible" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="离职日期" align="center" prop="fdLeaveTime" key="fdLeaveTime" width="120"
                       v-if="columns[21].visible" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fdLeaveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="再次入职原任职期" align="center" prop="lastTermOfOffice" key="lastTermOfOffice"
                       v-if="columns[22].visible" width="125" :show-overflow-tooltip='true'/>
      <el-table-column label="退休日期" align="center" prop="txrq" key="txrq" v-if="columns[23].visible" width="120"
                       :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.txrq, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="司龄(月)" align="center" prop="currentSeniority" key="currentSeniority"
                       v-if="columns[13].visible" width="90" :show-overflow-tooltip='true'/>
      <el-table-column label="工龄(月)" align="center" prop="realshgl" key="realshgl" v-if="columns[14].visible"
                       width="90" :show-overflow-tooltip='true'/>
      <el-table-column label="累计工龄(月)" align="center" prop="totalSeniority" key="totalSeniority"
                       v-if="columns[49].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="劳动合同主体" align="center" prop="contractName" key="contractName"
                       v-if="columns[25].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="劳动合同到期日期" align="center" prop="contractEndDate" key="contractEndDate"
                       v-if="columns[9].visible" width="130" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.contractEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="薪资发放公司" align="center" prop="gzffssgs" key="gzffssgs" v-if="columns[26].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="五险一金所在公司1" align="center" prop="socialSecurityOrg1" key="socialSecurityOrg1"
                       v-if="columns[27].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="五险一金所在公司2" align="center" prop="socialSecurityOrg2" key="socialSecurityOrg2"
                       v-if="columns[28].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="五险一金所在公司3" align="center" prop="socialSecurityOrg3" key="socialSecurityOrg3"
                       v-if="columns[73].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="五险一金缴纳地" align="center" prop="socialSecurityLocale" key="socialSecurityLocale"
                       v-if="columns[29].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="第三方代缴机构" align="center" prop="thirdPartyPaymentAgency"
                       key="thirdPartyPaymentAgency"
                       v-if="columns[30].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="工作地点" align="center" prop="fdWorkAddress" key="fdWorkAddress"
                       v-if="columns[32].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="工作地住址" align="center" prop="gzdi" key="gzdi" v-if="columns[33].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="身份证件号码" align="center" prop="idCard" key="idCard" v-if="columns[34].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="证件有效日期" align="center" prop="youxiaoqi" key="youxiaoqi"
                       v-if="columns[35].visible" width="120" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.youxiaoqi, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出生日期" align="center" prop="birthday" key="birthday"
                       v-if="columns[36].visible" width="120" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.birthday, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center" prop="age" key="age" v-if="columns[12].visible" width="50"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="性别" align="center" prop="sex" key="sex" v-if="columns[37].visible" width="50"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="民族" align="center" prop="fdNation" key="fdNation" v-if="columns[38].visible"
                       width="50" :show-overflow-tooltip='true'/>
      <el-table-column label="婚姻状况" align="center" prop="fdMaritalStatus" key="fdMaritalStatus"
                       v-if="columns[39].visible" width="90" :show-overflow-tooltip='true'/>
      <el-table-column label="政治面貌" align="center" prop="fdPoliticalLandscape" key="fdPoliticalLandscape"
                       v-if="columns[40].visible" width="90" :show-overflow-tooltip='true'/>
      <el-table-column label="毕业院校" align="center" prop="fdSchoolName" key="fdSchoolName" v-if="columns[41].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="专业" align="center" prop="fdMajor" key="fdMajor" v-if="columns[42].visible" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="学历" align="center" prop="highestEducation" key="highestEducation"
                       v-if="columns[10].visible" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="毕业时间" align="center" prop="graduationDate" key="graduationDate"
                       v-if="columns[11].visible" width="120" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.graduationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次参加工作日期" align="center" prop="participateInWork" key="participateInWork"
                       v-if="columns[43].visible" width="125" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.participateInWork, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="曾任职公司" align="center" prop="previouslyWorkedInACompany"
                       key="previouslyWorkedInACompany" v-if="columns[44].visible"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="本人联系方式" align="center" prop="fdMobileNo" key="fdMobileNo" v-if="columns[15].visible"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="紧急联系人" align="center" prop="fdEmergencyContact" key="fdEmergencyContact"
                       v-if="columns[45].visible"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="紧急联系人电话" align="center" prop="fdEmergencyContactPhone"
                       key="fdEmergencyContactPhone" v-if="columns[46].visible"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="现居住地址" align="center" prop="fdLivingPlace" key="fdLivingPlace"
                       v-if="columns[47].visible"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="籍贯" align="center" prop="fdNativePlace" key="fdNativePlace" v-if="columns[48].visible"
                       width="100" :show-overflow-tooltip='true'/>

      <el-table-column label="户口所在地" align="center" width="100">
        <el-table-column label="省" align="center" prop="registeredProvince" key="registeredProvince"
                         v-if="columns[50].visible" width="80" :show-overflow-tooltip='true'/>
        <el-table-column label="市" align="center" prop="registeredCity" key="registeredCity"
                         v-if="columns[51].visible" width="80" :show-overflow-tooltip='true'/>
        <el-table-column label="详细地址" align="center" prop="fdRegisteredResidence" key="fdRegisteredResidence"
                         v-if="columns[52].visible" width="120" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="户口类型" align="center" prop="fdAccountProperties" key="fdAccountProperties"
                       v-if="columns[53].visible" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="户口所在派出所" align="center" prop="fdResidencePoliceStation"
                       key="fdResidencePoliceStation"
                       v-if="columns[54].visible" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="职称" align="center" prop="titleCertificate" key="titleCertificate"
                       v-if="columns[55].visible" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="职业资格" align="center" prop="certificate" key="certificate"
                       v-if="columns[56].visible" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="考勤号" align="center" prop="fdKqbh" key="fdKqbh"
                       v-if="columns[57].visible" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="工时类型" align="center" prop="workType" key="workType"
                       v-if="columns[58].visible" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" key="costCenterName"
                       v-if="columns[59].visible" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" key="costCenterCode"
                       v-if="columns[60].visible" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="互助基金乐捐金额" align="center" prop="mutualFund" key="mutualFund"
                       v-if="columns[61].visible" width="130" :show-overflow-tooltip='true'/>

      <el-table-column label="<境内员工>薪资账号信息" align="center" width="100"
                       v-if="columns[62].visible || columns[63].visible || columns[64].visible || columns[65].visible || columns[66].visible">
        <el-table-column label="银行名称" align="center" prop="bankmain" key="bankmain" width="100"
                         v-if="columns[62].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="账号名称" align="center" prop="accountname" key="accountname" width="100"
                         v-if="columns[63].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="银行账号" align="center" prop="bank" key="bank" width="100"
                         v-if="columns[64].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="开户行" align="center" prop="bankname" key="bankname" width="100"
                         v-if="columns[65].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="联行号" align="center" prop="bankcode" key="bankcode" width="100"
                         v-if="columns[66].visible" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="<境外员工>薪资账号信息" align="center" width="100"
                       v-if="columns[67].visible || columns[68].visible || columns[69].visible">
        <el-table-column label="Swift code" align="center" prop="swiftCode" key="swiftCode" width="100"
                         v-if="columns[67].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="BSB" align="center" prop="bsb" key="bsb" width="100"
                         v-if="columns[68].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="Beneficiary address" align="center" prop="beneficiaryAddress" key="beneficiaryAddress"
                         width="100"
                         v-if="columns[69].visible" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="HC统计分类" align="center" prop="hc" key="hc" width="100"
                       v-if="columns[70].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="ESG职能分类" align="center" prop="esg" key="esg" width="100"
                       v-if="columns[71].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="出差人员分类" align="center" prop="businessTripType" key="businessTripType" width="100"
                       v-if="columns[31].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="公司宿舍人员分类" align="center" prop="dormitoryType" key="dormitoryType" width="100"
                       v-if="columns[75].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="曾使用工号" align="center" prop="jobNumberUsedBefore" key="jobNumberUsedBefore"
                       width="100"
                       v-if="columns[76].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="用工状态" align="center" prop="employmentStatus" key="employmentStatus"
                       v-if="columns[24].visible" width="50" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="secondDept" key="secondDept" width="100"
                       v-if="columns[16].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" key="thirdDept" width="100"
                       v-if="columns[17].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" key="fourthDept" width="100"
                       v-if="columns[18].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="selectDetail(scope.row)"
            v-hasPermi="['hr:pm:archive:list']"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <!--          <div class="el-upload__tip" slot="tip">-->
          <!--            <el-checkbox v-model="upload.updateSupport"/>-->
          <!--            是否更新已经存在的用户数据-->
          <!--          </div>-->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listArchive} from "@/api/hr/personnel/archive";
import {parseTime} from "@/utils/ruoyi"
import {getToken} from "@/utils/auth";

export default {
  name: "Archive",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 员工信息表格数据
      archiveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 出生日期时间范围
      dateRangeBirthday: [],
      // 入职日期时间范围
      dateRangeEntryDate: [],
      // 转正日期时间范围
      dateRangePositiveDate: [],
      // 合同到期日期时间范围
      dateRangeContractDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        post: null,
        deptNameAll: null,
        jobGrade: null,
        highestEducation: null,
        employeeStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/hr/personnel/archive/importData"
      },
      // 状态选项
      statusOptions: [
        {value: '', "id": 0, label: "所有"},
        {value: '在职', "id": 1, label: "在职"},
        {value: '离职', "id": 4, label: "离职"}
      ],
      // 列信息
      columns: [
        {key: 0, label: `工号`, visible: true},
        {key: 1, label: `姓名`, visible: true},
        {key: 2, label: `公司`, visible: true},
        {key: 3, label: `一级部门`, visible: true},
        {key: 4, label: `职位`, visible: true},
        {key: 5, label: `直接上级`, visible: true},
        {key: 6, label: `入职日期`, visible: true},
        {key: 7, label: `状态`, visible: true},
        {key: 8, label: `转正日期`, visible: true},
        {key: 9, label: `劳动合同到期日期`, visible: true},
        {key: 10, label: `最高学历`, visible: true},
        {key: 11, label: `毕业时间`, visible: true},
        {key: 12, label: `年龄`, visible: true},
        {key: 13, label: `司龄`, visible: true},
        {key: 14, label: `工龄`, visible: true},
        {key: 15, label: `联系方式`, visible: true},
        {key: 16, label: `二级部门`, visible: true},
        {key: 17, label: `三级部门`, visible: true},
        {key: 18, label: `四级部门`, visible: true},
        {key: 19, label: `职级`, visible: false},
        {key: 20, label: `试用期限/月`, visible: true},
        {key: 21, label: `离职日期`, visible: true},
        {key: 22, label: `再次入职原任职期`, visible: false},
        {key: 23, label: `退休日期`, visible: true},
        {key: 24, label: `用工状态`, visible: false},
        {key: 25, label: `劳动合同主体`, visible: true},
        {key: 26, label: `薪资发放公司`, visible: true},
        {key: 27, label: `五险一金所在公司1`, visible: true},
        {key: 28, label: `五险一金所在公司2`, visible: true},
        {key: 29, label: `五险一金缴纳地`, visible: true},
        {key: 30, label: `第三方代缴机构`, visible: true},
        {key: 31, label: `出差人员分类`, visible: true},
        {key: 32, label: `工作地点`, visible: true},
        {key: 33, label: `工作地住址`, visible: true},
        {key: 34, label: `身份证件号码`, visible: true},
        {key: 35, label: `证件有效日期`, visible: true},
        {key: 36, label: `出生日期`, visible: true},
        {key: 37, label: `性别`, visible: true},
        {key: 38, label: `民族`, visible: true},
        {key: 39, label: `婚姻状况`, visible: true},
        {key: 40, label: `政治面貌`, visible: true},
        {key: 41, label: `毕业院校`, visible: true},
        {key: 42, label: `专业`, visible: true},
        {key: 43, label: `首次参加工作日期`, visible: true},
        {key: 44, label: `曾任职公司`, visible: true},
        {key: 45, label: `紧急联系人`, visible: true},
        {key: 46, label: `紧急联系人电话`, visible: true},
        {key: 47, label: `现居地`, visible: true},
        {key: 48, label: `籍贯`, visible: true},
        {key: 49, label: `累计工龄`, visible: true},
        {key: 50, label: `省`, visible: true},
        {key: 51, label: `市`, visible: true},
        {key: 52, label: `详细地址`, visible: true},
        {key: 53, label: `户口类型`, visible: true},
        {key: 54, label: `户口所在派出所`, visible: true},
        {key: 55, label: `职称`, visible: true},
        {key: 56, label: `职业资格`, visible: true},
        {key: 57, label: `考勤号`, visible: true},
        {key: 58, label: `工时类型`, visible: true},
        {key: 59, label: `成本中心名称`, visible: true},
        {key: 60, label: `成本中心代码`, visible: true},
        {key: 61, label: `互助基金乐捐金额`, visible: true},
        {key: 62, label: `银行名称`, visible: true},
        {key: 63, label: `账户名称`, visible: true},
        {key: 64, label: `银行账号`, visible: true},
        {key: 65, label: `开户行`, visible: true},
        {key: 66, label: `联行号`, visible: true},
        {key: 67, label: `Swift code`, visible: true},
        {key: 68, label: `BSB`, visible: true},
        {key: 69, label: `Beneficiary address`, visible: true},
        {key: 70, label: `HC统计分类`, visible: true},
        {key: 71, label: `ESG职能分类`, visible: true},
        {key: 72, label: `分管VP`, visible: true},
        {key: 73, label: `五险一金所在公司3`, visible: true},
        {key: 74, label: `类型`, visible: true},
        {key: 75, label: `公司宿舍人员分类`, visible: true},
        {key: 76, label: `曾使用工号`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,
    /** 查询员工信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRangeBirthday && '' != this.dateRangeBirthday) {
        this.queryParams.params["beginBirthday"] = this.dateRangeBirthday[0];
        this.queryParams.params["endBirthday"] = this.dateRangeBirthday[1];
      }
      if (null != this.dateRangeEntryDate && '' != this.dateRangeEntryDate) {
        this.queryParams.params["beginEntryDate"] = this.dateRangeEntryDate[0];
        this.queryParams.params["endEntryDate"] = this.dateRangeEntryDate[1];
      }
      if (null != this.dateRangePositiveDate && '' != this.dateRangePositiveDate) {
        this.queryParams.params["beginPositiveDate"] = this.dateRangePositiveDate[0];
        this.queryParams.params["endPositiveDate"] = this.dateRangePositiveDate[1];
      }
      if (null != this.dateRangeContractDate && '' != this.dateRangeContractDate) {
        this.queryParams.params["beginContractExpireDate"] = this.dateRangeContractDate[0];
        this.queryParams.params["endContractExpireDate"] = this.dateRangeContractDate[1];
      }
      listArchive(this.queryParams).then(response => {
        this.archiveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        idCard: null,
        birthday: null,
        email: null,
        entryDate: null,
        status: null,
        mobile: null,
        orgId: null,
        deptId: null,
        loginName: null,
        post: null,
        orgName: null,
        deptName: null,
        deptNameAll: null,
        jobGrade: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.employeeStatus = '';
      this.daterangeBirthday = [];
      this.daterangeEntryDate = [];
      this.daterangePositiveDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    selectDetail(row) {
      if (window.getSelection().toString() === '') {
        const loc = "/hr/pm/archive/" + row.id
        this.$router.push(loc)
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/personnel/archive/export', {
        ...this.queryParams
      }, `员工档案_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    },
    /* 导入按钮 */
    handleImport() {
      this.upload.title = "导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('/hr/personnel/archive/importTemplate', {}, `员工档案导入模板_${dateTime}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    getPickerOptions(controlId) {
      const today = new Date(); // 当前日期
      const aWeekAgo = new Date; // 一周前
      const aMonthAgo = new Date; // 一个月前
      const threeMonthsAgo = new Date(); // 三个月前的日期
      const threeMonthsLater = new Date(); // 三个月后的日期
      aWeekAgo.setDate(today.getDate() - 7);
      aMonthAgo.setMonth(today.getMonth() - 1);
      threeMonthsAgo.setMonth(today.getMonth() - 3);
      threeMonthsLater.setMonth(today.getMonth() + 3)
      const todayStr = parseTime(today, '{y}-{m}-{d}')
      const aWeekAgoStr = parseTime(aWeekAgo, '{y}-{m}-{d}')
      const aMonthAgoStr = parseTime(aMonthAgo, '{y}-{m}-{d}')
      const threeMonthsAgoStr = parseTime(threeMonthsAgo, '{y}-{m}-{d}')
      const threeMonthsLaterStr = parseTime(threeMonthsLater, '{y}-{m}-{d}')

      function updateDatePicker(from, to) {
        return () => {
          if (controlId === 1) {
            this.dateRangeBirthday = [from, to];
            this.$refs.birthdayDatePicker.pickerVisible = false;
          } else if (controlId === 2) {
            this.dateRangeEntryDate = [from, to];
            this.$refs.entryDatePicker.pickerVisible = false;
          } else if (controlId === 3) {
            this.dateRangePositiveDate = [from, to];
            this.$refs.positiveDatePicker.pickerVisible = false;
          } else if (controlId === 4) {
            this.dateRangeContractDate = [from, to];
            this.$refs.contractDatePicker.pickerVisible = false;
          }
        };
      }

      return {
        shortcuts: [
          {
            text: '近一周',
            onClick: updateDatePicker.call(this, aWeekAgoStr, todayStr),
          },
          {
            text: '近一个月',
            onClick: updateDatePicker.call(this, aMonthAgoStr, todayStr),
          },
          {
            text: '近三个月',
            onClick: updateDatePicker.call(this, threeMonthsAgoStr, todayStr),
          },
          {
            text: '未来三个月',
            onClick: updateDatePicker.call(this, todayStr, threeMonthsLaterStr),
          },
        ],
      };
    },
  }
};
</script>
