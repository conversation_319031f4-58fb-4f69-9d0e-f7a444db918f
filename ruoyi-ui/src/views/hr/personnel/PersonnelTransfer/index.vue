<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="queryParams.queryDate"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="单号" prop="oddNumber">
        <el-input
          v-model="queryParams.oddNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="确认调动日期">
        <el-date-picker
          v-model="queryParams.queryDate1"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="申请人" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请人工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="调岗人员" prop="transferName">
        <el-input
          v-model="queryParams.transferName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="现岗位" prop="post">
        <el-input
          v-model="queryParams.post"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="拟安排岗位" prop="posts">
        <el-input
          v-model="queryParams.posts"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="拟调入部门" prop="transferDepartment">
        <el-input
          v-model="queryParams.transferDepartment"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="调入直属领导" prop="zs">
        <el-input
          v-model="queryParams.zs"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="调岗原因" prop="transferReason">
        <el-input
          v-model="queryParams.transferReason"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

<!--      <el-form-item label="城市" prop="city">-->
<!--        <el-select placeholder="请选择..." clearable filterable v-model="queryParams.city">-->
<!--          <el-option-->
<!--            v-for="item in this.queryList"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:PersonnelTransfer:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" border
              :max-height="tableMaxHeight">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="单号" align="center" prop="oddNumber" width="200" :show-overflow-tooltip="true" fixed>
        <template v-slot="scope">
          <span @click="jump(scope.row)" style="color: #3967FF;cursor:pointer;">{{ scope.row.oddNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="100" fixed>
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="name" width="100" :show-overflow-tooltip="true" fixed/>
      <el-table-column label="申请人工号" align="center" prop="jobNumber" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="调岗人员" align="center" prop="transferName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="250" :show-overflow-tooltip="true"/>
      <el-table-column label="是否需要变更五险一金" align="center" prop="isFive" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="原缴纳地" align="center" prop="placeOfPayment" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="申请变动缴纳地" align="center" prop="placeOfPayments" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="现岗位" align="center" prop="post" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="拟调动日期" align="center" prop="transferDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.transferDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="拟调入部门" align="center" prop="transferDepartment" width="250" :show-overflow-tooltip="true"/>
      <el-table-column label="拟安排岗位" align="center" prop="posts" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="现驻地" align="center" prop="base" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="拟调入驻地" align="center" prop="bases" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="拟调入直属领导" align="center" prop="zs" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="拟调入直属领导确认调动日期" align="center" prop="zsDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.zsDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="拟调入部门领导" align="center" prop="bm" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="拟调入部门分管副总裁" align="center" prop="fzc" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="调岗原因" align="center" prop="transferReason" width="300" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {queryPersonnelTransferList} from "@/api/hr/personnel/PersonnelTransfer.js";

export default {
  name: "PersonnelTransfer",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      disabled: 'disabled',
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 数据集合
      dataList: [],
      queryList: [],
      // 查询参数
      queryParams: {
        // 页码、页显示量
        pageNum: 1,
        pageSize: 10,
        // 日期查询控件值集合
        queryDate: null,
        // 日期范围查询开始、结束
        startDate: null,
        endDate: null,
        queryDate1: null,
        // 日期范围查询开始、结束
        startDate1: null,
        endDate1: null,
        // 其他查询参数
        id: null,
        name: null,
        applicationDate: null,
      },/** queryParams 范围 */
    };/** return 范围 */
  }, /** data 范围 */
  created() {
    this.getList();
  }, /** created 范围 */
  computed: {
    /** 页面适应 */
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  }, /** computed 范围 */
  methods: {
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.queryParams.queryDate != null && this.queryParams.queryDate !== '') {
        this.queryParams.startDate = this.queryParams.queryDate[0];
        this.queryParams.endDate = this.queryParams.queryDate[1];
      }
      if (this.queryParams.queryDate1 != null && this.queryParams.queryDate1 !== '') {
        this.queryParams.startDate1 = this.queryParams.queryDate1[0];
        this.queryParams.endDate1 = this.queryParams.queryDate1[1];
      }
      queryPersonnelTransferList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = null;
      this.queryParams.startDate = null;
      this.queryParams.endDate = null;
      this.queryParams.queryDate1 = null;
      this.queryParams.startDate1 = null;
      this.queryParams.endDate1 = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/PersonnelTransfer/export', {
        ...this.queryParams
      }, `导出人员调动_${dateTime}.xlsx`)
    },
    jump(row) {
      window.open(row.filePath)
    },
  }, /** methods 范围 */
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
    },
    'queryParams.queryDate1'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate1 = ''
        this.queryParams.startDate1 = ''
        this.queryParams.endDate1 = ''
      }
    }
  },/** watch 范围 */

};
</script>
