<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="单号" prop="fdD<PERSON>hao">
        <el-input
          v-model="queryParams.fdDanhao"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属部门" prop="fdSuoshubumen">
        <el-input
          v-model="queryParams.fdSuoshubumen"
          placeholder="请输入所属部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="出门时间" prop="fdChumenshijian">
        <el-date-picker
          clearable
          v-model="queryParams.fdChumenshijian"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择出门时间"
        ></el-date-picker>
      </el-form-item> -->
      <el-form-item label="驾驶员" prop="fdJiashiyuan">
        <el-input
          v-model="queryParams.fdJiashiyuan"
          placeholder="请输入驾驶员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="fdChepaihao">
        <el-input
          v-model="queryParams.fdChepaihao"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="司机电话" prop="fdJiashiyuanlianxidianhua">
        <el-input
          v-model="queryParams.fdJiashiyuanlianxidianhua"
          placeholder="请输入驾驶人联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:wzcm:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:wzcm:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:wzcm:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:wzcm:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>-->

    <el-table style="width: 100%" v-loading="loading" :data="wzcmList" @selection-change="handleSelectionChange">

      <el-table-column label="展开" type="expand"  >
        <template slot-scope="props">
          <el-form  label-position="left" inline class="demo-table-expand" label-width="80px">
            <el-form-item label="申请人">
              <span>{{ props.row.name }}</span>
            </el-form-item>
            <el-form-item label="单号">
              <span>{{ props.row.fdDanhao }}</span>
            </el-form-item>
            <el-form-item label="所属部门">
              <span>{{ props.row.fdSuoshubumen }}</span>
            </el-form-item>
            <el-form-item label="申请日期">
              <span>{{ props.row.fdShenqingriqi }}</span>
            </el-form-item>
            <el-form-item label="货物名称">
              <span>{{ props.row.huoWuMingChen }}</span>
            </el-form-item>
            <el-form-item label="数量">
              <span>{{ props.row.shuLiang }}</span>
            </el-form-item>
            <el-form-item label="出门方式">
                <span>{{ props.row.fdChumenfangshi}}</span>
            </el-form-item>
            <el-form-item label="出门时间">
                 <span>{{ props.row.fdChumenshijian }}</span>
            </el-form-item>
            <el-form-item label="事由">
              <span>{{ props.row.fdWuzichumenshiyou }}</span>
            </el-form-item>

          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        label="单号"
        align="center"
        width="200"
        prop="fdDanhao"
        :show-overflow-tooltip="false"
      />
      <el-table-column label="申请日期	" align="center" prop="fdShenqingriqi" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fdShenqingriqi, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="name" />
      <el-table-column
        label="所属部门"
        align="center"
        prop="fdSuoshubumen"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="货物名称"
        align="center"
        width="100"
        prop="huoWuMingChen"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="数量" align="center" prop="shuLiang" :show-overflow-tooltip="true" />
      <el-table-column
        label="物资出门事由"
        align="center"
        prop="fdWuzichumenshiyou"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="出门方式"
        align="center"
        prop="fdChumenfangshi"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="出门时间" align="center" prop="fdChumenshijian" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fdChumenshijian, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驾驶员" align="center" prop="fdJiashiyuan" />
      <el-table-column label="车牌号" align="center" prop="fdChepaihao" />
      <el-table-column label="驾驶人联系电话" align="center" prop="fdJiashiyuanlianxidianhua" />

      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:wzcm:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"x
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:wzcm:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物资出门申请
    对话框-->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="单号" prop="fdDanhao">
          <el-input v-model="form.fdDanhao" placeholder="请输入单号" />
        </el-form-item>
        <el-form-item label="申请日期	" prop="fdShenqingriqi">
          <el-date-picker
            clearable
            v-model="form.fdShenqingriqi"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择申请日期	"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="申请人" prop="fdShenqingren1">
          <el-input v-model="form.fdShenqingren1" placeholder="请输入申请人" />
        </el-form-item>
        <el-form-item label="所属部门" prop="fdSuoshubumen">
          <el-input v-model="form.fdSuoshubumen" placeholder="请输入所属部门" />
        </el-form-item>
        <el-form-item label="出门方式" prop="fdChumenfangshi">
          <el-input v-model="form.fdChumenfangshi" placeholder="请输入出门方式" />
        </el-form-item>
        <el-form-item label="出门时间" prop="fdChumenshijian">
          <el-date-picker
            clearable
            v-model="form.fdChumenshijian"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出门时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="驾驶员" prop="fdJiashiyuan">
          <el-input v-model="form.fdJiashiyuan" placeholder="请输入驾驶员" />
        </el-form-item>
        <el-form-item label="车牌号" prop="fdChepaihao">
          <el-input v-model="form.fdChepaihao" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="驾驶人联系电话" prop="fdJiashiyuanlianxidianhua">
          <el-input v-model="form.fdJiashiyuanlianxidianhua" placeholder="请输入驾驶人联系电话" />
        </el-form-item>
        <el-form-item label="物资出门事由" prop="fdWuzichumenshiyou">
          <el-input v-model="form.fdWuzichumenshiyou" placeholder="请输入物资出门事由" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWzcm,
  getWzcm,
  delWzcm,
  addWzcm,
  updateWzcm
} from "@/api/hr/suppliesOut/wzcm";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "Wzcm",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物资出门申请

      wzcmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fdDanhao: null,
        fdShenqingren1: null,
        fdSuoshubumen: null,
        fdChumenshijian: null,
        fdJiashiyuan: null,
        fdChepaihao: null,
        fdJiashiyuanlianxidianhua: null,
        name: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物资出门申请
     列表 */
    getList() {
      this.loading = true;
      listWzcm(this.queryParams).then(response => {
        this.wzcmList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        fdId: null,
        fdDanhao: null,
        fdShenqingriqi: null,
        fdShenqingren1: null,
        fdSuoshubumen: null,
        fdChumenfangshi: null,
        fdChumenshijian: null,
        fdJiashiyuan: null,
        fdChepaihao: null,
        fdJiashiyuanlianxidianhua: null,
        fdWuzichumenshiyou: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fdId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物资出门申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const fdId = row.fdId || this.ids;
      getWzcm(fdId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物资出门申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.fdId != null) {
            updateWzcm(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWzcm(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fdIds = row.fdId || this.ids;
      this.$modal
        .confirm('是否确认删除物资出门申请编号为"' + fdIds + '"的数据项？')
        .then(function() {
          return delWzcm(fdIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/wzcm/export",
        {
          ...this.queryParams
        },
        `wzcm_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`
      );
    }
  }
};
</script>
<style scoped>
  .demo-table-expand {
    font-size: 0;
  }
  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }
  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }
</style>
