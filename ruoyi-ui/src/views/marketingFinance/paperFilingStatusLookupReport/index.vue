<!--4、纸质归档情况查询报表-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="所属部门">
        <el-input v-model="queryParams.department" placeholder="请输入所属部门" clearable size="small"/>
      </el-form-item>
      <el-form-item label="申请人">
        <el-input v-model="queryParams.applicant" placeholder="请输入申请人" clearable size="small"/>
      </el-form-item>
      <el-form-item label="单号">
        <el-input v-model="queryParams.documentNumber" placeholder="请输入单号" clearable size="small"/>
      </el-form-item>
      <el-form-item label="SAP凭证号">
        <el-input v-model="queryParams.sapVoucherNumber" placeholder="请输入SAP凭证号" clearable size="small"/>
      </el-form-item>
      <el-form-item label="财务收单人">
        <el-input v-model="queryParams.financialReceiptPerson" placeholder="请输入财务收单人" clearable size="small"/>
      </el-form-item>
      <el-form-item label="收单情况">
        <el-input v-model="queryParams.billed" placeholder="请输入收单情况" clearable size="small"/>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="单据类型" align="center" prop="documentType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单号" align="center" prop="documentNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="申请人工号" align="center" prop="applicantEmployeeNumber" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="所属部门（末三级）" align="center" prop="department" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="所属部门（末二级）" align="center" prop="region" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门（末一级）" align="center" prop="area" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="reimbursementAmount" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="是否需要收单" align="center" prop="billed" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP凭证号" align="center" prop="sapVoucherNumber" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="财务收单人" align="center" prop="financialReceiptPerson" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="收单情况" align="center" prop="receiptStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="票据类型（若有）" align="center" prop="billType" width="200" :show-overflow-tooltip="true"/>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { managementReportPage } from '@/api/marketingFinance/managementReport'
import { periodList } from '@/api/marketingFinance/period'
import { paperFilingStatusLookupReportPage } from '@/api/marketingFinance/paperFilingStatusLookupReport'

export default {
  name: 'ManagementReporting',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        department: null,
        applicant: null,
        documentNumber: null,
        sapVoucherNumber: null,
        financialReceiptPerson: null,
        billed: null,
        pageSize: 10,
        pageNum: 1
      },
      tableData: [{
        documentType: null,
        documentNumber: null,
        applicant: null,
        applicationDate: null,
        applicantEmployeeNumber: null,
        department: null,
        region: null,
        area: null,
        district: null,
        reimbursementAmount: null,
        billed: null,
        unbilled: null,
        sapVoucherNumber: null,
        financialReceiptPerson: null,
        receiptStatus: null,
        billType: null
      }]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询目标终端列表 */
    getList() {
      this.loading = true
      paperFilingStatusLookupReportPage(this.queryParams).then(res => {
        this.tableData = res.data.rows
        if (this.queryParams.pageNum === 1) {
          this.total = res.data.total
        }
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        department: null,
        applicant: null,
        documentNumber: null,
        sapVoucherNumber: null,
        financialReceiptPerson: null,
        billed: null
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/marketingFinance/paperFilingStatusLookupReport/export', {
        ...this.queryParams
      }, `纸质归档情况查询报表_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>


<style scoped lang="scss">

</style>


