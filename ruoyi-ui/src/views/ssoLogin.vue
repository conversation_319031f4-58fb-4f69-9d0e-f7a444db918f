<template>

</template>

<script>

export default {
  name: "testLogin",
  data() {
    return {
      redirect: undefined,
      token: undefined,
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
        this.token = route.query && route.query.token;
      },
      immediate: true
    }
  },
  methods: {
    init() {
      if (this.token) {
        this.$store.dispatch("thirdPartyLogin", this.token).then(() => {
          this.$router.push({path: this.redirect || "/"}).catch(() => {
          });
        })
      } else {
        if (this.redirect === undefined || this.redirect === ""){
          this.redirect = "index"
        }
        window.location.href = "https://sso.akesobio.com/sso/oauth2.0/authorize?client_id=1894e0087det411c01213f8f47c18723&redirect_uri=https%3A%2F%2Fdas.akesobio.com%2Fprod-api%2Flogin%3Fredirect%3D" + this.redirect + "&response_type=code"
      }
    },
  },
  created() {
    this.init()
  }
}
</script>

<style scoped>

</style>
