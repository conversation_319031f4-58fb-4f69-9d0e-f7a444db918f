<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司ID" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入公司ID"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接口名称" prop="interfaceName">
        <el-input
          v-model="queryParams.interfaceName"
          placeholder="请输入接口名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事件代码" prop="eventCode">
        <el-select v-model="queryParams.eventCode" placeholder="请选择事件代码" clearable>
          <el-option label="开始审批(wfp)" value="wfp" />
          <el-option label="审批通过(wfa)" value="wfa" />
          <el-option label="单据提交(expsubmitted)" value="expsubmitted" />
          <el-option label="审批通过(expapproved)" value="expapproved" />
        </el-select>
      </el-form-item>
      <el-form-item label="单据类型" prop="headerTypeCode">
        <el-input
          v-model="queryParams.headerTypeCode"
          placeholder="请输入单据类型代码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="岗位编码" prop="positionCode">
        <el-input
          v-model="queryParams.positionCode"
          placeholder="请输入岗位编码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:mqconfig:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:mqconfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:mqconfig:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mqConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="配置ID" align="center" prop="id" width="80" />
      <el-table-column label="公司ID" align="center" prop="companyId" width="80" />
      <el-table-column label="交换机" align="center" prop="exchange" :show-overflow-tooltip="true" />
      <el-table-column label="路由键" align="center" prop="rootingKey" :show-overflow-tooltip="true" />
      <el-table-column label="平台" align="center" prop="platform" />
      <el-table-column label="接口名称" align="center" prop="interfaceName" :show-overflow-tooltip="true" />
      <el-table-column label="事件代码" align="center" prop="eventCode" />
      <el-table-column label="单据类型" align="center" prop="headerTypeCode" />
      <el-table-column label="岗位编码" align="center" prop="positionCode" />
      <el-table-column label="工作流类型" align="center" prop="workflowType" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:mqconfig:edit']"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改虚拟岗校验规则配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公司ID" prop="companyId">
              <el-input v-model="form.companyId" placeholder="请输入公司ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台" prop="platform">
              <el-input v-model="form.platform" placeholder="请输入平台" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="交换机" prop="exchange">
              <el-input v-model="form.exchange" placeholder="请输入交换机" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口名称" prop="interfaceName">
              <el-input v-model="form.interfaceName" placeholder="请输入接口名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="路由键" prop="rootingKey">
          <el-input v-model="form.rootingKey" placeholder="请输入路由键" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="事件代码" prop="eventCode">
              <el-select v-model="form.eventCode" placeholder="请选择事件代码">
                <el-option label="开始审批(wfp)" value="wfp" />
                <el-option label="审批通过(wfa)" value="wfa" />
                <el-option label="单据提交(expsubmitted)" value="expsubmitted" />
                <el-option label="审批通过(expapproved)" value="expapproved" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单据类型代码" prop="headerTypeCode">
              <el-input v-model="form.headerTypeCode" placeholder="请输入单据类型代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位编码" prop="positionCode">
              <el-input v-model="form.positionCode" placeholder="请输入岗位编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作流类型" prop="workflowType">
              <el-input v-model="form.workflowType" placeholder="请输入工作流类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMqConfig, getMqConfig, delMqConfig, addMqConfig, updateMqConfig, checkInterfaceNameUnique } from "@/api/system/mqconfig";

export default {
  name: "VirtualPositionValidation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 虚拟岗校验规则配置表格数据
      mqConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyId: undefined,
        exchange: undefined,
        rootingKey: undefined,
        platform: undefined,
        interfaceName: undefined,
        eventCode: undefined,
        headerTypeCode: undefined,
        positionCode: undefined,
        workflowType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        companyId: [
          { required: true, message: "公司ID不能为空", trigger: "blur" }
        ],
        exchange: [
          { required: true, message: "交换机不能为空", trigger: "blur" }
        ],
        rootingKey: [
          { required: true, message: "路由键不能为空", trigger: "blur" }
        ],
        platform: [
          { required: true, message: "平台不能为空", trigger: "blur" }
        ],
        interfaceName: [
          { required: true, message: "接口名称不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value) {
                checkInterfaceNameUnique({
                  id: this.form.id,
                  interfaceName: value
                }).then(response => {
                  if (!response) {
                    callback(new Error("接口名称已存在"));
                  } else {
                    callback();
                  }
                });
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        eventCode: [
          { required: true, message: "事件代码不能为空", trigger: "change" }
        ],
        headerTypeCode: [
          { required: true, message: "单据类型代码不能为空", trigger: "blur" }
        ],
        positionCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询虚拟岗校验规则配置列表 */
    getList() {
      this.loading = true;
      listMqConfig(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.mqConfigList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        companyId: undefined,
        exchange: undefined,
        rootingKey: undefined,
        platform: undefined,
        interfaceName: undefined,
        description: undefined,
        eventCode: undefined,
        headerTypeCode: undefined,
        positionCode: undefined,
        workflowType: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加虚拟岗校验规则配置";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMqConfig(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改虚拟岗校验规则配置";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateMqConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMqConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除配置编号为"' + ids + '"的数据项？').then(function() {
          return delMqConfig(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/mqconfig/export', {
        ...this.queryParams
      }, `mqconfig_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
