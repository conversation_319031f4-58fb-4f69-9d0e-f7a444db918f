<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="75px">
      <el-form-item label="核算代码" prop="mpmCode">
        <el-input
          v-model="queryParams.mpmCode"
          placeholder="请输入核算代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核算名称" prop="mpmName">
        <el-input
          v-model="queryParams.mpmName"
          placeholder="请输入核算名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车间" prop="workshop">
        <el-input
          v-model="queryParams.workshop"
          placeholder="请输入车间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核人员" prop="auditors">
        <el-input
          v-model="queryParams.auditors"
          placeholder="请输入审核人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="creationTime">
        <el-input
          v-model="queryParams.creationTime"
          placeholder="请输入创建时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createdBy">
        <el-input
          v-model="queryParams.createdBy"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新时间" prop="updated">
        <el-input
          v-model="queryParams.updated"
          placeholder="请输入更新时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新人" prop="updater">
        <el-input
          v-model="queryParams.updater"
          placeholder="请输入更新人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="删除时间" prop="deletionTime">
        <el-input
          v-model="queryParams.deletionTime"
          placeholder="请输入删除时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产线" prop="productLine">
        <el-input
          v-model="queryParams.productLine"
          placeholder="请输入生产线"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产月份" prop="productMonth">
        <el-date-picker clearable
                        v-model="queryParams.productMonth"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择生产月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="生产批次" prop="producBatch">
        <el-input
          v-model="queryParams.producBatch"
          placeholder="请输入生产批次"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['report:proplan:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['report:proplan:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['report:proplan:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['report:proplan:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->

    <el-table ref="table" v-loading="loading" :data="planList" @row-click="singleElection">
      <el-table-column label="序号" width="65">
        <template slot-scope="scope">
          <el-radio class="radio" v-model="templateSelection" :label="scope.$index">&nbsp;</el-radio>
        </template>
      </el-table-column>
<!--      <el-table-column type="selection" width="55" align="center" />-->
<!--      <el-table-column label="主键id" align="center" prop="id" />-->
      <el-table-column label="核算代码" align="center" prop="mpmCode" />
      <el-table-column label="核算名称" align="center" prop="mpmName" />
      <el-table-column label="车间" align="center" prop="workshop" />
      <el-table-column label="审核人员" align="center" prop="auditors" />
<!--      <el-table-column label="创建时间" align="center" prop="creationTime" />-->
<!--      <el-table-column label="创建人" align="center" prop="createdBy" />-->
<!--      <el-table-column label="更新时间" align="center" prop="updated" />-->
<!--      <el-table-column label="更新人" align="center" prop="updater" />-->
<!--      <el-table-column label="删除时间" align="center" prop="deletionTime" />-->
<!--      <el-table-column label="删除状态" align="center" prop="deleteStatus" />-->
      <el-table-column label="生产线" align="center" prop="productLine" />
      <el-table-column label="生产月份" align="center" prop="productMonth" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.productMonth, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生产批次" align="center" prop="producBatch" />

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-button type="success"  size="medium " @click="emitCheckValue">确认选中</el-button>

  </div>
</template>

<script>
import { listPlan, getPlan, delPlan, addPlan, updatePlan } from "@/api/autoacct/plan";
import MpmData from "@/views/autoacct/components/MpmData";

export default {
  name: "ProPlanData",
  components: {MpmData},
  data() {
    return {
      templateSelection: '',
      templateRadio: null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: null,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 生产计划表格数据
      planList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      innerOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mpmCode: null,
        mpmName: null,
        workshop: null,
        auditors: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        productLine: null,
        productMonth: null,
        producBatch: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    singleElection (row) {
      this.templateSelection = this.planList.indexOf(row);
      this.templateRadio = row.id;
      this.ids = row
    },
    emitCheckValue(){
      this.$emit("emitCheckValue",this.ids)
    },
    onEmitMPMCodes(param1){
      if(param1.length > 0){
        this.form.mpmCode = param1[0].mpmCode
        this.form.mpmName = param1[0].mpmName
      }
      this.innerOpen = false
    },
    /** 查询生产计划列表 */
    getList() {
      this.loading = true;
      listPlan(this.queryParams).then(response => {
        this.planList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mpmCode: null,
        mpmName: null,
        workshop: null,
        auditors: null,
        creationTime: null,
        createdBy: null,
        updated: null,
        updater: null,
        deletionTime: null,
        deleteStatus: null,
        productLine: null,
        productMonth: null,
        producBatch: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {

      // this.ids = selection.map(item => item.id)
      // this.single = selection.length!==1
      // this.multiple = !selection.length

      if (selection.length > 1) {
        this.$refs.table.clearSelection()
        this.$refs.table.toggleRowSelection(selection.pop())
      }
      this.ids = selection

    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加生产计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPlan(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改生产计划";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePlan(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlan(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除生产计划编号为"' + ids + '"的数据项？').then(function() {
        return delPlan(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/prodplan/export', {
        ...this.queryParams
      }, `生产计划_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

