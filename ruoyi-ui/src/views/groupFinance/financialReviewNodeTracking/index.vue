<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="表单类型" prop="docType">
        <el-select v-model="queryParams.docType" placeholder="请选择表单类型">
          <el-option label="FD24付款申请-科学试验费" value="FD24付款申请-科学试验费"></el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="主题  " prop="docSubject">
        <el-input
          v-model="queryParams.docSubject"
          placeholder="请输入主题  "
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入申请人部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用" prop="expenseAmount">
        <el-input
          v-model="queryParams.expenseAmount"
          placeholder="请输入费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="fdName">
        <el-input
          v-model="queryParams.fdName"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['report:FinancialBpNodeTrackingController:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="trackingList" border @selection-change="handleSelectionChange" :header-cell-style="{'text-align':'center'}">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="表单类型"  width="150" align="center" prop="docType" />
      <el-table-column label="单号" align="center" prop="单号" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
        <a @click="handleClick(scope.row)" style="color: #00afff">
          {{ scope.row.docNo }}
        </a>
        </template>
      </el-table-column>
      <el-table-column label="主题  " align="center" prop="docSubject" width="300" :show-overflow-tooltip="true" />
      <el-table-column label="费用主体" align="center" prop="expenseEntity" width="150" />
      <el-table-column label="部门" align="center" prop="deptName" :show-overflow-tooltip="true" />
      <el-table-column label="费用"  prop="expenseAmount"  align="right" width="150" >
        <template slot-scope="scope">
        <span >
        {{ formatNumberWithCommas(scope.row) }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="fdName" />

      <el-table-column label="表单状态" align="center" prop="docStatus" />
      <el-table-column label="当前节点" align="center" prop="fdFactNodeName" />
      <el-table-column label="当前处理人" align="center" prop="currentHandler"  width="100" />
      <el-table-column label="到达节点时间" align="center" width="180" prop="fdStartDate" >
        <template slot-scope="scope">
        <span >
        {{dayjs(scope.row.fdStartDate).format('YYYY-MM-DD HH:mm')}}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="滞留小时" align="center" prop="fdStartDate" >
        <template slot-scope="scope">
        <span >
        {{ getTimeDifferenceInHours(scope.row) }}
        </span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import {listTracking} from "@/api/groupFinance/financialBpNodeTracking";
import dayjs from 'dayjs';

export default {
  name: "FinancialReviewNodeTracking",
  data() {
    return {
      dayjs,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务BP节点追踪表格数据
      trackingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docType: null,
        docSubject: null,
        deptName: null,
        expenseAmount: null,
        fdName: null,
        docStatus: null,
        fdFactNodeName: null,
        fdStartDate: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询财务BP节点追踪列表 */
    getList() {
      this.loading = true;
      listTracking(this.queryParams).then(response => {
        this.trackingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        docType: null,
        docSubject: null,
        deptName: null,
        expenseAmount: null,
        fdName: null,
        docStatus: null,
        fdFactNodeName: null,
        fdStartDate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docType)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('report/GroupFinancialBpNodeTrackingController/export', {
        ...this.queryParams
      }, `财务BP滞留_${new Date().getTime()}.xlsx`)
    },
    handleClick(row) {
      // const processNumber = row.processNumber;
      const id = row.fdId;
      // if (processNumber != null && processNumber != '' && id != null && id != '') {
      //   if (processNumber.includes('TY005')) {
      //     const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
      //     window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      //   } else {
          const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
          window.open(url, '_blank'); // 在新窗口或标签页中打开链接
        // }
      // }
    },
    getTimeDifferenceInHours(row) {
  // 将日期字符串转换为Date对象
      const date1 = new Date(row.fdStartDate);
      const date2 = new Date();
      // 计算时间差，单位为毫秒
      const timeDifference = date2.getTime() - date1.getTime();

      // 将时间差转换为小时，并四舍五入到一位小数
      const hoursDifference = Math.round(timeDifference / (1000 * 60 * 60) * 10) / 10;

      return hoursDifference;
    },
    formatNumberWithCommas(row) {
      const numberStr = row.expenseAmount;
      // 将字符串转换为数字
      const number = parseFloat(numberStr);

      // 保留三位有效数字，不足的部分用零补充
      const formattedNumberStr = number.toFixed(3).replace(/(\.\d*?)0+$/, '$1'); // 去除小数点后的多余零

      // 使用Intl.NumberFormat来格式化数字，添加千分位分隔符
      return new Intl.NumberFormat('en-US', {
        style: 'decimal',
        minimumFractionDigits: 2, // 确保至少有三位小数
        maximumFractionDigits: 2  // 最多三位小数
      }).format(parseFloat(formattedNumberStr));
    }
  }
};
</script>
