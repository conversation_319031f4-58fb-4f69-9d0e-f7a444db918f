<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="公司主体" prop="companyEntity">
        <el-input
          v-model="queryParams.companyEntity"
          placeholder="请输入公司主体"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用发生年份" prop="expenseYear">
        <el-input
          v-model="queryParams.expenseYear"
          placeholder="请输入费用发生年份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用发生月份" prop="expenseMonth">
        <el-input
          v-model="queryParams.expenseMonth"
          placeholder="请输入费用发生月份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会计科目" prop="accountingSubjectCode">
        <el-input
          v-model="queryParams.accountingSubjectCode"
          placeholder="请输入会计科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="内部订单号" prop="internalOrderNumber">
        <el-input
          v-model="queryParams.internalOrderNumber"
          placeholder="请输入内部订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本中心" prop="costCenterCode">
        <el-input
          v-model="queryParams.costCenterCode"
          placeholder="请输入成本中心"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级分类" prop="firstLevelClassification">
        <el-input
          v-model="queryParams.firstLevelClassification"
          placeholder="请输入一级分类"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级分类" prop="secondaryClassification">
        <el-input
          v-model="queryParams.secondaryClassification"
          placeholder="请输入二级分类"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="功能范围" prop="functionalScope">
        <el-input
          v-model="queryParams.functionalScope"
          placeholder="请输入功能范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:logisticsCostSummary:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logisticsCostSummaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="公司主体" align="center" prop="companyEntity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用发生年份" align="center" prop="expenseYear" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用发生月份" align="center" prop="expenseMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会计科目" align="center" prop="accountingSubjectCode" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="会计科目名称" align="center" prop="accountingSubjectName" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="内部订单号" align="center" prop="internalOrderNumber" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="一级分类" align="center" prop="firstLevelClassification" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="二级分类" align="center" prop="secondaryClassification" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心" align="center" prop="costCenterCode" />
      <el-table-column label="功能范围" align="center" prop="functionalScope" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="功能范围描述" align="center" prop="functionalScopeName" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="money" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listLogisticsCostSummary,
  getLogisticsCostSummary,
  delLogisticsCostSummary,
  addLogisticsCostSummary,
  updateLogisticsCostSummary
} from "@/api/groupFinance/logisticsCostSummary";

export default {
  name: "LogisticsCostSummary",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物流费用采购申请汇总表格数据
      logisticsCostSummaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyEntity: null,
        expenseYear: null,
        expenseMonth: null,
        accountingSubjectCode: null,
        accountingSubjectName: null,
        internalOrderNumber: null,
        firstLevelClassification: null,
        secondaryClassification: null,
        functionalScope: null,
        functionalScopeName: null,
        money: null,
        deleteStatus: null,
        costCenterCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物流费用采购申请汇总列表 */
    getList() {
      this.loading = true;
      listLogisticsCostSummary(this.queryParams).then(response => {
        this.logisticsCostSummaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyEntity: null,
        expenseYear: null,
        expenseMonth: null,
        accountingSubjectCode: null,
        accountingSubjectName: null,
        internalOrderNumber: null,
        firstLevelClassification: null,
        secondaryClassification: null,
        functionalScope: null,
        functionalScopeName: null,
        money: null,
        deleteStatus: null,
        costCenterCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物流费用采购申请汇总";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLogisticsCostSummary(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物流费用采购申请汇总";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLogisticsCostSummary(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLogisticsCostSummary(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除物流费用采购申请汇总编号为"' + ids + '"的数据项？').then(function () {
        return delLogisticsCostSummary(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/logisticsCostSummary/export', {
        ...this.queryParams
      }, `logisticsCostSummary_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
