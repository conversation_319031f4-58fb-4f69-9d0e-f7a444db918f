<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="付款日期">
        <el-date-picker
          v-model="daterangePaymentApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收款单位" prop="receivingUnit">
        <el-input
          v-model="queryParams.receivingUnit"
          placeholder="请输入收款单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="单号" prop="OANumber">
        <el-input
          v-model="queryParams.OANumber"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:clinicalTotalExpenses:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="clinicalTotalExpensesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="OA编号" align="center" prop="oanumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同号" align="center" prop="contractNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款申请日期" align="center" prop="paymentApplicationDate" width="200"
                       :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentApplicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担公司" align="center" prop="costBearingCompany" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用类型" align="center" prop="feeType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="款项说明" align="center" prop="paymentDescription" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目" align="center" prop="suject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="结算账期" align="center" prop="settlementPeriod" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="病例数" align="center" prop="casesNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="amount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="汇率" align="center" prop="exchangeRate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="收款单位" align="center" prop="receivingUnit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同类型" align="center" prop="contractType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心" align="center" prop="costCenter" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目代码" align="center" prop="accountCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="amountWithoutTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="专票税额" align="center" prop="specialTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款状态" align="center" prop="sapDocumentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="sapCredentialPushDate" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="应付凭证号" align="center" prop="sapPayNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA发票类型" align="center" prop="invoiceType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA发票号" align="center" prop="invoiceNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA收票" align="center" prop="invoice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP发票号" align="center" prop="sapInvoiceNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP税额" align="center" prop="sapTaxAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票开票日期" align="center" prop="invoiceIssuanceDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.invoiceIssuanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商请款日期" align="center" prop="supplierDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.supplierDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="确认可以支付日期" align="center" prop="paymentDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发票回收日期" align="center" prop="recyclingDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.recyclingDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对外发票金额确认时间" align="center" prop="confirmTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款耗时" align="center" prop="paymentTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票回收耗时" align="center" prop="invoiceCollectionTime" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {listClinicalTotalExpenses} from "@/api/groupFinance/clinicalTotalExpenses";

export default {
  name: "ClinicalTotalExpenses",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床总支出表格数据
      clinicalTotalExpensesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 应付凭证号时间范围
      daterangePaymentApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        OANumber: null,
        contractNo: null,
        paymentApplicationDate: null,
        applicant: null,
        department: null,
        costBearingCompany: null,
        projectNumber: null,
        feeType: null,
        paymentDescription: null,
        suject: null,
        settlementPeriod: null,
        casesNumber: null,
        amount: null,
        currency: null,
        exchangeRate: null,
        receivingUnit: null,
        contractType: null,
        costCenterCode: null,
        costCenter: null,
        accountCode: null,
        amountWithoutTax: null,
        specialTax: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        sapCredentialPushDate: null,
        applicationSubject: null,
        sapPayNumber: null,
        invoiceType: null,
        invoiceNumber: null,
        invoice: null,
        currentProcessor: null,
        centerNumber: null,
        invoiceIssuanceDate: null,
        supplierDate: null,
        paymentDate: null,
        recyclingDate: null,
        confirmTime: null,
        paymentTime: null,
        invoiceCollectionTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询临床总支出列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePaymentApplicationDate && '' != this.daterangePaymentApplicationDate) {
        this.queryParams.params["beginPaymentApplicationDate"] = this.daterangePaymentApplicationDate[0];
        this.queryParams.params["endPaymentApplicationDate"] = this.daterangePaymentApplicationDate[1];
      }
      listClinicalTotalExpenses(this.queryParams).then(response => {
        this.clinicalTotalExpensesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        OANumber: null,
        contractNo: null,
        paymentApplicationDate: null,
        applicant: null,
        department: null,
        costBearingCompany: null,
        projectNumber: null,
        feeType: null,
        paymentDescription: null,
        suject: null,
        settlementPeriod: null,
        casesNumber: null,
        amount: null,
        currency: null,
        exchangeRate: null,
        receivingUnit: null,
        contractType: null,
        costCenterCode: null,
        costCenter: null,
        accountCode: null,
        amountWithoutTax: null,
        specialTax: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        sapCredentialPushDate: null,
        applicationSubject: null,
        sapPayNumber: null,
        invoiceType: null,
        invoiceNumber: null,
        invoice: null,
        currentProcessor: null,
        centerNumber: null,
        invoiceIssuanceDate: null,
        supplierDate: null,
        paymentDate: null,
        recyclingDate: null,
        confirmTime: null,
        paymentTime: null,
        invoiceCollectionTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePaymentApplicationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.OANumber)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/clinicalTotalExpenses/export', {
        ...this.queryParams
      }, `clinicalTotalExpenses_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
