<template>
  <div>

    <div class="form" style="">
      <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="200px">
        <el-form-item label="年份" prop="year" label-width="70px">
          <el-select v-model="queryParams.year" clearable placeholder="请选择" style="width: 80px">
            <el-option
              v-for="item in yearOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            style="margin-left: 50px"
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="list"
          >搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div
      id="main"
      ref="main"
      style="width: 1100px; height: 600px; background-color: #ffffff;  border-radius: 20px;  "
    >加载中...</div>
  </div>
</template>

<script>
import { cart } from "@/api/groupFinance/clinicalTotalExpenses";

import * as echarts from "echarts";
export default {
  data() {
    return {
      costBearingCompany: [],
      option: {},
      arr: [],
      total: [],
      amount: [],
      yearOptions: [],
      queryParams: {
        year: new Date().getFullYear()
      }
    };
  },
  created() {
    this.getYearSelector();
  },
  mounted() {
    this.nullCart();
    this.newCart();
  },
  methods: {
    list() {
      this.newCart();
    },
    newCart() {
      this.costBearingCompany.splice(0, this.costBearingCompany.length);
      this.amount.splice(0, this.amount.length);

      cart(this.queryParams).then(response => {
        var Company = response.rows.map(item => {
          return item.costBearingCompany;
        });
        var Total = response.rows.map(item => {
          return item.total;
        });
        this.arr = Company;
        this.total = Total;
        for (var i = 0; i < this.arr.length; i++) {
          this.costBearingCompany.push(this.arr[i]);
        }
        for (var i = 0; i < this.total.length; i++) {
          this.amount.push(this.total[i]);
        }
        this.applyChart.setOption({
          xAxis: {
            type: "category",
            axisLabel: { interval: 0, rotate: 20 },
            data: this.costBearingCompany
          },
          title: {
            text: this.queryParams.year + "年临床支出汇总图表"
          },
          series: [
            {
              data: this.amount /* 这里要和第一组数据一样、才能保持在最高处*/,
              type: "line",
              symbol: "circle", //拐点样式
              symbolSize: 8, //拐点大小
              itemStyle: {
                normal: {

                  lineStyle: {
                    width: 3, //折线宽度
                    color: "#501fdb" //折线颜色
                  },
                  color: "#501fdb", //拐点颜色
                  borderColor: "#5138ad", //拐点边框颜色
                  borderWidth: 3 //拐点边框大小
                },
                emphasis: {
                  color: "#4CCEFE" //hover拐点颜色定义
                }
              }
            },
            {
              type: "pictorialBar", // pictorialBar(象形柱图)
              label: {
                // 图形上的文本标签,可用于说明图像的一些数据信息,比如值,名称等

                position: ["17", "-30"], // 标签的位置(可以是绝对的像素值或者百分比['50%','50%',也可以是top,left等])
                color: "#01E4FF",
                fontSize: 14
              },
              symbolSize: [40, 20], // 图形的大小用数组分别比表示宽和高,也乐意设置成10相当于[10,10]
              symbolOffset: [0, 10], // 图形相对于原本位置的偏移
              z: 12, // 象形柱状图组件的所有图形的 z 值.控制图形的前后顺序.z 值小的图形会被 z 值大的图形覆盖.
              itemStyle: {
                // 图形样式
                // echarts.graphic.LinearGradient(echarts内置的渐变色生成器)
                // 4个参数用于配置渐变色的起止位置,这4个参数依次对应右 下 左 上
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  // 这里 offset: 0 1 ,表示从下往上的渐变色
                  {
                    offset: 0, // 0%处的颜色
                    color: "rgba(31,155,255,1)"
                  },
                  {
                    offset: 1, // 100%处的颜色
                    color: "rgba(0,229,255,1)"
                  }
                ])
              },
              data: this.amount
            },
            // 中间的长方形柱状图(柱状图):bar
            {
              type: "bar", // 柱状图
              barWidth: 40, // 柱条的宽度,不设时自适应
              barGap: "0%", // 柱子与柱子之间的距离
              itemStyle: {
                // 图形样式
                // color支持(rgb(255,255,255)、rgba(255,255,255,1)、#fff,也支持渐变色和纹理填充)
                // 下面就是使用线性渐变
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  type: "linear",
                  global: false,
                  colorStops: [
                    {
                      offset: 0, // 0%处的颜色
                      color: "rgba(0,229,255,0.5)"
                    },
                    {
                      offset: 1, // 100%处的颜色
                      color: "#1F9BFF"
                    }
                  ]
                }
              },
              data: this.amount
            },
            // 顶部的椭圆形(象形柱图):pictorialBar
            {
              type: "pictorialBar",
              symbolSize: [40, 20],
              symbolOffset: [0, -10],
              z: 12,
              symbolPosition: "end",
              itemStyle: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "rgba(31,155,255,1)"
                    },
                    {
                      offset: 1,
                      color: "rgba(0,229,255,1)"
                    }
                  ],
                  false
                )
              },
              data: this.amount
            },

          ]
        });
      });
    },
    getYearSelector() {
      let year = new Date().getFullYear();
      for (let i = 2021; i <= year; i++) {
        this.yearOptions.push({
          label: i,
          value: i
        });
      }
    },
    nullCart() {
      this.applyChart = echarts.init(document.querySelector("#main"));
      this.applyChart.setOption({
        barWidth: 40, // 柱条的宽度,不设时自适应
        barGap: "0%",
        tooltip: {
          // trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value"
        },
        title: { text: "2023年临床支出汇总图表" },
        series: [
          // 底部的椭圆形(象形柱图):pictorialBar
          {
            type: "pictorialBar", // pictorialBar(象形柱图)
            label: {
              // 图形上的文本标签,可用于说明图像的一些数据信息,比如值,名称等
              show: true, //是否显示标签
              position: ["17", "-30"], // 标签的位置(可以是绝对的像素值或者百分比['50%','50%',也可以是top,left等])
              color: "#01E4FF",
              fontSize: 14
            },
            symbolSize: [40, 20], // 图形的大小用数组分别比表示宽和高,也乐意设置成10相当于[10,10]
            symbolOffset: [0, 10], // 图形相对于原本位置的偏移
            z: 12, // 象形柱状图组件的所有图形的 z 值.控制图形的前后顺序.z 值小的图形会被 z 值大的图形覆盖.
            itemStyle: {
              // 图形样式
              // echarts.graphic.LinearGradient(echarts内置的渐变色生成器)
              // 4个参数用于配置渐变色的起止位置,这4个参数依次对应右 下 左 上
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                // 这里 offset: 0 1 ,表示从下往上的渐变色
                {
                  offset: 0, // 0%处的颜色
                  color: "rgba(31,155,255,1)"
                },
                {
                  offset: 1, // 100%处的颜色
                  color: "rgba(0,229,255,1)"
                }
              ])
            },
            data: this.amount
          },
          // 中间的长方形柱状图(柱状图):bar
          {
            type: "bar", // 柱状图
            barWidth: 40, // 柱条的宽度,不设时自适应
            barGap: "0%", // 柱子与柱子之间的距离
            itemStyle: {
              // 图形样式
              // color支持(rgb(255,255,255)、rgba(255,255,255,1)、#fff,也支持渐变色和纹理填充)
              // 下面就是使用线性渐变
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: "linear",
                global: false,
                colorStops: [
                  {
                    offset: 0, // 0%处的颜色
                    color: "rgba(0,229,255,0.5)"
                  },
                  {
                    offset: 1, // 100%处的颜色
                    color: "#1F9BFF"
                  }
                ]
              }
            },
            data: this.amount
          },
          // 顶部的椭圆形(象形柱图):pictorialBar
          {
            type: "pictorialBar",
            symbolSize: [40, 20],
            symbolOffset: [0, -10],
            z: 12,
            symbolPosition: "end",
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(31,155,255,1)"
                  },
                  {
                    offset: 1,
                    color: "rgba(0,229,255,1)"
                  }
                ],
                false
              )
            },
            data: this.amount
          }
        ]
      });
    }
  }
};
</script>

<style>
.form {
  /*width: 500px;*/
  /*height: 50px;*/
  /*position: absolute;*/
  /*top: 50%;*/
  /*left: 50%;*/
  /* border: 6px; */
  /* float:right; */
  /* display:inline-block */
  /* border-color: rgb(57, 105, 189);
  border-style: double; */
}
</style>
