<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="费用承担" prop="expences">
        <el-input
          v-model="queryParams.expences"
          placeholder="请输入费用承担"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合作单位" prop="cooperateUnit">
        <el-input
          v-model="queryParams.cooperateUnit"
          placeholder="请输入合作单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="表单状态" prop="formState">
        <el-input
          v-model="queryParams.formState"
          placeholder="请输入表单状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:clinicalContract:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="clinicalContractList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="创建日期" align="center" prop="dateCreated" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCreated, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="OA单号" align="center" prop="oatrackingNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同编号" align="center" prop="contractNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合作单位" align="center" prop="cooperateUnit" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担" align="center" prop="expences" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="交易内容" align="center" prop="transactionContent" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="合同层级" align="center" prop="contractLevel" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="补充协议是否含对应主协议" align="center" prop="masterAgreement" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="业务类型" align="center" prop="businessType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同类型" align="center" prop="contractType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="委外细分" align="center" prop="outsourcingSubdivision" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="病例数" align="center" prop="casesNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="病例单价" align="center" prop="casePrice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="税率" align="center" prop="taxRate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="交易金额" align="center" prop="transactionAmount" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="质保金" align="center" prop="warranty" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="劳务费" align="center" prop="laborFee" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="表单状态" align="center" prop="formState" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="主协议金额" align="center" prop="mainAgreementAmount" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="合同金额是否单独列明增值税额" align="center" prop="contractTaxAmount" width="300"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="付款里程碑" align="center" prop="paymentMilestones" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="里程碑金额" align="center" prop="milestoneAmount" width="200"
                       :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listClinicalContract} from '@/api/groupFinance/clinicalContract'

export default {
  name: 'ClinicalContract',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床合同跟进表格数据
      clinicalContractList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 里程碑金额时间范围
      daterangeDateCreated: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateCreated: null,
        OATrackingNumber: null,
        applicationSubject: null,
        applicant: null,
        department: null,
        contractNo: null,
        cooperateUnit: null,
        expences: null,
        centerName: null,
        transactionContent: null,
        contractLevel: null,
        masterAgreement: null,
        businessType: null,
        contractType: null,
        outsourcingSubdivision: null,
        currency: null,
        projectNumber: null,
        casesNumber: null,
        casePrice: null,
        taxRate: null,
        transactionAmount: null,
        warranty: null,
        laborFee: null,
        formState: null,
        currentSession: null,
        paymentMilestones: null,
        milestoneAmount: null,
        mainAgreementAmount: null,
        contractTaxAmount: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询临床合同跟进列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params['beginDateCreated'] = this.daterangeDateCreated[0]
        this.queryParams.params['endDateCreated'] = this.daterangeDateCreated[1]
      }
      listClinicalContract(this.queryParams).then(response => {
        this.clinicalContractList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    // 表单重置
    reset() {
      this.form = {
        dateCreated: null,
        OATrackingNumber: null,
        applicationSubject: null,
        applicant: null,
        department: null,
        contractNo: null,
        cooperateUnit: null,
        expences: null,
        centerName: null,
        transactionContent: null,
        contractLevel: null,
        masterAgreement: null,
        businessType: null,
        contractType: null,
        outsourcingSubdivision: null,
        currency: null,
        projectNumber: null,
        casesNumber: null,
        casePrice: null,
        taxRate: null,
        transactionAmount: null,
        warranty: null,
        laborFee: null,
        formState: null,
        currentSession: null,
        paymentMilestones: null,
        milestoneAmount: null,
        mainAgreementAmount: null,
        contractTaxAmount: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDateCreated = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dateCreated)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/clinicalContract/export', {
        ...this.queryParams
      }, `clinicalContract_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
