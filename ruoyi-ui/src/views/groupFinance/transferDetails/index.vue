<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">

      <el-form-item label="收款单位" prop="receivingUnit">
        <el-input
          v-model="queryParams.receivingUnit"
          placeholder="请输入收款单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="转出主体" prop="transferOutSubject">
        <el-input
          v-model="queryParams.transferOutSubject"
          placeholder="请输入转出主体"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转出项目号" prop="transferOutProjectNumber">
        <el-input
          v-model="queryParams.transferOutProjectNumber"
          placeholder="请输入转出项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="转入主体" prop="changeIntoSubject">
        <el-input
          v-model="queryParams.changeIntoSubject"
          placeholder="请输入转入主体"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转入项目号" prop="changeIntoProjectNumber">
        <el-input
          v-model="queryParams.changeIntoProjectNumber"
          placeholder="请输入转入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangeApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:transferDetails:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="transferDetailsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="OA单号" align="center" prop="oANumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人姓名" align="center" prop="reimbursementPerson" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="收款单位" align="center" prop="receivingUnit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商代码" align="center" prop="supplierCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="转出主体" align="center" prop="transferOutSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="转出项目号" align="center" prop="transferOutProjectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="转出成本中心" align="center" prop="transferOutCostCenter" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="转入主体" align="center" prop="changeIntoSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="转入项目号" align="center" prop="changeIntoProjectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="转入成本中心" align="center" prop="changeIntoCostCenter" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款金额(不含税)" align="center" prop="excludingTaxPaymentAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="税额" align="center" prop="taxAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款金额(含税)" align="center" prop="includingTaxPaymentAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商是否给转出方开发票" align="center" prop="invoice" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="发票号码" align="center" prop="invoiceNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="应付凭证号" align="center" prop="payableVoucherNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送时间" align="center" prop="voucherPushTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listTransferDetails } from "@/api/groupFinance/transferDetails";

export default {
  name: "TransferDetails",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 转款明细表格数据
      transferDetailsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前环节时间范围
      daterangeApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationDate: null,
        oANumber: null,
        applicationSubject: null,
        reimbursementPerson: null,
        department: null,
        receivingUnit: null,
        supplierCode: null,
        transferOutSubject: null,
        transferOutProjectNumber: null,
        transferOutCostCenter: null,
        changeIntoSubject: null,
        changeIntoProjectNumber: null,
        changeIntoCostCenter: null,
        excludingTaxPaymentAmount: null,
        taxAmount: null,
        includingTaxPaymentAmount: null,
        invoice: null,
        invoiceNumber: null,
        payableVoucherNumber: null,
        voucherPushTime: null,
        documentStatus: null,
        currentSession: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询转款明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
        this.queryParams.params["beginApplicationDate"] = this.daterangeApplicationDate[0];
        this.queryParams.params["endApplicationDate"] = this.daterangeApplicationDate[1];
      }
      listTransferDetails(this.queryParams).then(response => {
        this.transferDetailsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationDate: null,
        oANumber: null,
        applicationSubject: null,
        reimbursementPerson: null,
        department: null,
        receivingUnit: null,
        supplierCode: null,
        transferOutSubject: null,
        transferOutProjectNumber: null,
        transferOutCostCenter: null,
        changeIntoSubject: null,
        changeIntoProjectNumber: null,
        changeIntoCostCenter: null,
        excludingTaxPaymentAmount: null,
        taxAmount: null,
        includingTaxPaymentAmount: null,
        invoice: null,
        invoiceNumber: null,
        payableVoucherNumber: null,
        voucherPushTime: null,
        documentStatus: null,
        currentSession: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeApplicationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationDate)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/transferDetails/export', {
        ...this.queryParams
      }, `transferDetails_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
