<template>
  <div class="app-container">

  </div>
</template>

<script>
import { chartListExpenseReimbursement } from "@/api/groupFinance/expenseReimbursementChart";

export default {
  name: "ExpenseReimbursement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 费用报销表格数据
      expenseReimbursementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 凭证推送日期时间范围
      daterangeCreatedDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        OANumber: null,
        createdDate: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        jobTitle: null,
        department: null,
        costCenterName: null,
        expenseAccountName: null,
        specialInvoiceTax: null,
        currency: null,
        reimbursementAmount: null,
        projectNumber: null,
        businessType: null,
        businessDescription: null,
        centerName: null,
        presidentBusiness: null,
        costCenterCode: null,
        accountCode: null,
        invoiceNo: null,
        amountWithoutTax: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        applicationSubject: null,
        sapCredentialPushDate: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询费用报销列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      chartListExpenseReimbursement(this.queryParams).then(response => {
        this.expenseReimbursementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
  }
};
</script>
