<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeCreatedDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="报销类别" prop="reimbursementCategory">
        <el-input
          v-model="queryParams.reimbursementCategory"
          placeholder="请输入报销类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报销人" prop="reimbursementPerson">
        <el-input
          v-model="queryParams.reimbursementPerson"
          placeholder="请输入报销人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用科目" prop="expenseAccountName">
        <el-input
          v-model="queryParams.expenseAccountName"
          placeholder="请输入费用科目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="OA编号" prop="OANumber">
        <el-input
          v-model="queryParams.OANumber"
          placeholder="请输入OA编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:expenseReimbursement:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="expenseReimbursementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="OA编号" align="center" prop="oanumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="创建日期" align="center" prop="createdDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销类别" align="center" prop="reimbursementCategory" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="公司代码" align="center" prop="companyCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="公司名称" align="center" prop="companyName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人" align="center" prop="reimbursementPerson" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人工号" align="center" prop="reimbursementPersonNumber" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="职务" align="center" prop="jobTitle" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用科目名称" align="center" prop="expenseAccountName" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="专用发票税额" align="center" prop="specialInvoiceTax" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="reimbursementAmount" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务类型" align="center" prop="businessType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务描述" align="center" prop="businessDescription" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="总裁办业务" align="center" prop="presidentBusiness" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目代码" align="center" prop="accountCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号码" align="center" prop="invoiceNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="amountWithoutTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP应付凭证状态" align="center" prop="sapDocumentStatus" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="sapCredentialPushDate" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="SAP凭证号" align="center" prop="sapVoucherNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务发生日期" align="center" prop="businessOccurrenceDate" width="200"
                       :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listExpenseReimbursement} from "@/api/groupFinance/expenseReimbursement";

export default {
  name: "ExpenseReimbursement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 费用报销表格数据
      expenseReimbursementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 凭证推送日期时间范围
      daterangeCreatedDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        OANumber: null,
        createdDate: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        jobTitle: null,
        department: null,
        costCenterName: null,
        expenseAccountName: null,
        specialInvoiceTax: null,
        currency: null,
        reimbursementAmount: null,
        projectNumber: null,
        businessType: null,
        businessDescription: null,
        centerName: null,
        presidentBusiness: null,
        costCenterCode: null,
        accountCode: null,
        invoiceNo: null,
        amountWithoutTax: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        applicationSubject: null,
        sapCredentialPushDate: null,
        businessOccurrenceDate: null,
        sapVoucherNumber: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    // 初始化默认时间
    this.defaultDate();
    this.getList();
  },
  methods: {
    defaultDate() {
      let today = new Date();
      let year = today.getFullYear();
      let month = today.getMonth() + 1; // JavaScript中月份是从0开始的，所以需要+1
      // 获取当前月份第一天
      let firstDayOfMonth = new Date(year, month - 1, 1);
      // 确保月份和日期前面有零
      let formattedFirstDay = `${year}-${(month < 10 ? '0' : '') + month}-01`;
      // 获取当前月份的最后一天
      // 通过创建下个月的第一天，然后减去一天来获取本月最后一天
      let lastDayOfMonth = new Date(year, month, 0);
      let formattedLastDay = `${year}-${(month < 10 ? '0' : '') + month}-${lastDayOfMonth.getDate()}`;
      this.daterangeCreatedDate = [formattedFirstDay, formattedLastDay];
    },
    /** 查询费用报销列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreatedDate && '' != this.daterangeCreatedDate) {
        this.queryParams.params["beginCreatedDate"] = this.daterangeCreatedDate[0];
        this.queryParams.params["endCreatedDate"] = this.daterangeCreatedDate[1];
      }
      listExpenseReimbursement(this.queryParams).then(response => {
        this.expenseReimbursementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        OANumber: null,
        createdDate: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        jobTitle: null,
        department: null,
        costCenterName: null,
        expenseAccountName: null,
        specialInvoiceTax: null,
        currency: null,
        reimbursementAmount: null,
        projectNumber: null,
        businessType: null,
        businessDescription: null,
        centerName: null,
        presidentBusiness: null,
        costCenterCode: null,
        accountCode: null,
        invoiceNo: null,
        amountWithoutTax: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        applicationSubject: null,
        sapCredentialPushDate: null,
        businessOccurrenceDate: null,
        sapVoucherNumber: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreatedDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.OANumber)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/expenseReimbursement/export', {
        ...this.queryParams
      }, `expenseReimbursement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
