<template>
  <div>
    <div id="main"></div>
    <div id="pie"></div>
  </div>

</template>

<script>
import {
  selectTypeInvoiceCount
} from "@/api/groupFinance/typeInvoiceChart";
import * as echarts from 'echarts';
export default {
  data() {
    return {
      invoiceType: [],
      count: [],
      isChartInitialized: false
    };
  },
  created() {
    this.initData();
  },
  mounted() {
    this.data();
  },
  methods: {
    async data() {
      await selectTypeInvoiceCount().then(response => {
        this.invoiceType = response.data.map(item => {
          return item.invoiceType;
        });
        this.count = response.data.map(item => {
          return item.count;
        });
        // console.log(this.invoiceType)
        // console.log(this.count)
      })
      this.newInvoiceChart();
      this.newInvoicePie();
    },
    initData() {
      this.invoiceType = [];
      this.count = [];
    },
    newInvoiceChart() {
        const chartBox = echarts.init(document.getElementById("main"));
        const option = {
          title: {
            show: true,
            text: "发票图表数据",
            padding: 20,
            textStyle: {
              color: 'rgba(232, 239, 14, 1)',
              fontStyle: 'normal',//'italic'(倾斜) | 'oblique'(倾斜体) ，字体风格
              fontWeight: 'bold',//'bold'(粗体) | 'bolder'(粗体) | 'lighter'(正常粗细) ，字体粗细
              fontFamily: 'sans-serif',//'sans-serif' | 'serif' | 'monospace' | 'Arial' | 'Courier New'
              // 'Microsoft YaHei'(微软雅黑) ，文字字体
              fontSize: 18,//字体大小
              lineHeight: 18,//字体行高,
              textAlign:'auto',//整体（包括 text 和 subtext）的水平对齐
              textVerticalAlign:'auto',//整体（包括 text 和 subtext）的垂直对齐
              padding:0,//[5,10] | [ 5,6, 7, 8] ,标题内边距
              left:'auto',//'5' | '5%'，title 组件离容器左侧的距离
              right:'50%',//'title 组件离容器右侧的距离
              top:'20',//title 组件离容器上侧的距离
              bottom:'auto',//title 组件离容器下侧的距离
            }
          },
          backgroundColor: '#0f375f',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['line', 'bar'],
            textStyle: {
              color: '#ccc'
            }
          },
          xAxis: {
            type: "category",
            data: this.invoiceType,
            axisLabel: {interval: 0, rotate: 30},
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            }
          },
          yAxis: {
            splitLine: { show: false },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            }
          },
          series: [
            {
              name: 'line',
              type: 'line',
              smooth: true,
              showAllSymbol: true,
              symbol: 'emptyCircle',
              symbolSize: 15,
              data: this.count
            },
            {
              name: 'bar',
              type: 'bar',
              barWidth: 10,
              itemStyle: {
                borderRadius: 5,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#14c8d4' },
                  { offset: 1, color: '#43eec6' }
                ])
              },
              data: this.count
            },
            {
              name: 'line',
              type: 'bar',
              barGap: '-100%',
              barWidth: 10,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(20,200,212,0.5)' },
                  { offset: 0.2, color: 'rgba(20,200,212,0.2)' },
                  { offset: 1, color: 'rgba(20,200,212,0)' }
                ])
              },
              z: -12,
              data: this.count
            },
            {
              name: 'dotted',
              type: 'pictorialBar',
              symbol: 'rect',
              itemStyle: {
                color: '#0f375f'
              },
              symbolRepeat: true,
              symbolSize: [12, 4],
              symbolMargin: 1,
              z: -10,
              data: this.count
            }
          ]
        };
        chartBox.setOption(option);
        // 根据页面大小自动响应图表大小
        window.addEventListener("resize", function () {
          chartBox.resize();
        });
    },
    newInvoicePie() {
      const chartBox = this.$echarts.init(document.getElementById("pie"));
      const pieOption = {
        backgroundColor: '#0f375f',
        title: {
          text: '发票饼图数据',
          padding: 20,
          textStyle: {
            color: 'rgba(232, 239, 14, 1)'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '发票类型 <br/>{b} : {c} ({d}%)'
        },
        legend: {
          bottom: -20,
          left: 'center',
          data: [],
          textStyle: {
            color: 'rgba(232, 239, 14, 1)'
          }
        },
        series: [
          {
            type: 'pie',
            radius: '65%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      for (let i = 0; i < this.invoiceType.length; i++) {
        pieOption.series[0].data.push({
          name: this.invoiceType[i],
          value: this.count[i]
        })
        pieOption.legend.data.push(this.invoiceType[i])
      }
      chartBox.setOption(pieOption);
      // 根据页面大小自动响应图表大小
      window.addEventListener("resize", function () {
        chartBox.resize();
      });
    }
  },
};
</script>

<style lang="scss" scoped>
#main {
  min-width: 31.25rem;
  min-height: 31.25rem;
  // max-height: 500px;
}
#pie {
  max-height: 500px;
  // max-height: 400px;
  height: 500px;
}
</style>
