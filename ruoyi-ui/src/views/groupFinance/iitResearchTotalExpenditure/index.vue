<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="付款日期">
        <el-date-picker
          v-model="daterangePaymentApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="收款单位" prop="receivingUnit">
        <el-input
          v-model="queryParams.receivingUnit"
          placeholder="请输入收款单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:iitResearchTotalExpenditure:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="iitResearchTotalExpenditureList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="表单名称" align="center" prop="tableName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA单号" align="center" prop="singleNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款申请日期" align="center" prop="paymentApplicationDate" width="200"
                       :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentApplicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人工号" align="center" prop="applicantJobNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人" align="center" prop="reimbursementPerson" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会议类别/费用类型/报销类型" align="center" prop="meetingCategory" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务类型" align="center" prop="businessType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担部门" align="center" prop="thirdLevelDepartment" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担部门" align="center" prop="secondaryDepartment" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担部门" align="center" prop="endLevelDepartment" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用说明" align="center" prop="costDescription" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remarks"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="系统项目号" align="center" prop="systemProjectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="归集项目号" align="center" prop="collectionProjectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担部门分摊金额" align="center" prop="departmentAllocationAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同编号" align="center" prop="contractNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担公司" align="center" prop="costBearingCompany" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="收款单位" align="center" prop="receivingUnit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目类型" align="center" prop="projectType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同类型" align="center" prop="contractType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="委外细分" align="center" prop="outsourcingSubdivision" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="款项说明" align="center" prop="paymentDescription" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目代码" align="center" prop="accountCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目" align="center" prop="suject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="结算账期" align="center" prop="settlementPeriod" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票类型" align="center" prop="invoiceType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号码" align="center" prop="invoiceNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA收票" align="center" prop="invoice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="税率(%)" align="center" prop="taxRate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="amountWithoutTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="专票税额" align="center" prop="specialTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="amount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="归属部门" align="center" prop="ascriptionDepartment" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="含税金额" align="center" prop="amountIncludingTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="大区经理" align="center" prop="regionalManager" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="部门/销售区总监" align="center" prop="salesAreaDirector" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remark" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP发票号" align="center" prop="sapInvoiceNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP税额" align="center" prop="sapTaxAmount" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {listIitResearchTotalExpenditure} from "@/api/groupFinance/iitResearchTotalExpenditure";

export default {
  name: "IitResearchTotalExpenditure",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // IIT研究总支出表格数据
      iitResearchTotalExpenditureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前环节时间范围
      daterangePaymentApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        paymentApplicationDate: null,
        singleNumber: null,
        applicant: null,
        department: null,
        contractNo: null,
        applicationSubject: null,
        costBearingCompany: null,
        receivingUnit: null,
        centerNo: null,
        centerName: null,
        costCenterCode: null,
        costCenterName: null,
        projectType: null,
        projectNumber: null,
        contractType: null,
        outsourcingSubdivision: null,
        paymentDescription: null,
        accountCode: null,
        suject: null,
        settlementPeriod: null,
        invoiceType: null,
        invoiceNo: null,
        taxRate: null,
        amountWithoutTax: null,
        specialTax: null,
        amount: null,
        ascriptionDepartment: null,
        amountIncludingTax: null,
        regionalManager: null,
        salesAreaDirector: null,
        documentStatus: null,
        currentSession: null,
        invoice: null,
        sapPayNumber: null,
        sapInvoiceNumber: null,
        sapTaxAmount: null,
        tableName: null,
        applicantJobNumber: null,
        reimbursementPerson: null,
        meetingCategory: null,
        businessType: null,
        thirdLevelDepartment: null,
        secondaryDepartment: null,
        endLevelDepartment: null,
        costDescription: null,
        remarks: null,
        systemProjectNumber: null,
        collectionProjectNumber: null,
        departmentAllocationAmount: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询IIT研究总支出列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePaymentApplicationDate && '' != this.daterangePaymentApplicationDate) {
        this.queryParams.params["beginPaymentApplicationDate"] = this.daterangePaymentApplicationDate[0];
        this.queryParams.params["endPaymentApplicationDate"] = this.daterangePaymentApplicationDate[1];
      }
      listIitResearchTotalExpenditure(this.queryParams).then(response => {
        this.iitResearchTotalExpenditureList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        paymentApplicationDate: null,
        singleNumber: null,
        applicant: null,
        department: null,
        contractNo: null,
        applicationSubject: null,
        costBearingCompany: null,
        receivingUnit: null,
        centerNo: null,
        centerName: null,
        costCenterCode: null,
        costCenterName: null,
        projectType: null,
        projectNumber: null,
        contractType: null,
        outsourcingSubdivision: null,
        paymentDescription: null,
        accountCode: null,
        suject: null,
        settlementPeriod: null,
        invoiceType: null,
        invoiceNo: null,
        taxRate: null,
        amountWithoutTax: null,
        specialTax: null,
        amount: null,
        ascriptionDepartment: null,
        amountIncludingTax: null,
        regionalManager: null,
        salesAreaDirector: null,
        remark: null,
        documentStatus: null,
        currentSession: null,
        invoice: null,
        sapPayNumber: null,
        sapInvoiceNumber: null,
        sapTaxAmount: null,
        tableName: null,
        applicantJobNumber: null,
        reimbursementPerson: null,
        meetingCategory: null,
        businessType: null,
        thirdLevelDepartment: null,
        secondaryDepartment: null,
        endLevelDepartment: null,
        costDescription: null,
        remarks: null,
        systemProjectNumber: null,
        collectionProjectNumber: null,
        departmentAllocationAmount: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePaymentApplicationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.paymentApplicationDate)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/iitResearchTotalExpenditure/export', {
        ...this.queryParams
      }, `iitResearchTotalExpenditure_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
