<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangeApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="单号" prop="oddNumbers">
        <el-input
          v-model="queryParams.oddNumbers"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="companyCode">
        <el-input
          v-model="queryParams.companyCode"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入申请人工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="工作日期">
        <el-date-picker
          v-model="daterangeWorkDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="成本中心代码" prop="costCenter">
        <el-input
          v-model="queryParams.costCenter"
          placeholder="请输入成本中心代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本中心名称" prop="costCenterName">
        <el-input
          v-model="queryParams.costCenterName"
          placeholder="请输入成本中心名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程状态" prop="documentStatus">
        <el-input
          v-model="queryParams.documentStatus"
          placeholder="请输入流程状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:scientificTimetable:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportAll"
          v-hasPermi="['groupFinance:scientificTimetable:exportAll']"
        >导出全部</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scientificTimetableList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="单号" align="center" prop="oddNumbers" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公司代码" align="center" prop="companyCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人工号" align="center" prop="jobNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="小组负责人" align="center" prop="teamLeader" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心" align="center" prop="costCenter" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remarks" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="工作日期" align="center" prop="workDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.workDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成本中心明细" align="center" prop="costCenterDetail" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科学时间" align="center" prop="scientificTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listScientificTimetable} from "@/api/groupFinance/scientificTimetable";

export default {
  name: "ScientificTimetable",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 科学时间表格数据
      scientificTimetableList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 流程状态时间范围
      daterangeApplicationDate: [],
      // 流程状态时间范围
      daterangeWorkDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oddNumbers: null,
        applicationDate: null,
        companyCode: null,
        applicant: null,
        jobNumber: null,
        teamLeader: null,
        costCenter: null,
        costCenterName: null,
        remarks: null,
        workDate: null,
        costCenterDetail: null,
        projectNumber: null,
        scientificTime: null,
        documentStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询科学时间列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
        this.queryParams.params["beginApplicationDate"] = this.daterangeApplicationDate[0];
        this.queryParams.params["endApplicationDate"] = this.daterangeApplicationDate[1];
      }
      if (null != this.daterangeWorkDate && '' != this.daterangeWorkDate) {
        this.queryParams.params["beginWorkDate"] = this.daterangeWorkDate[0];
        this.queryParams.params["endWorkDate"] = this.daterangeWorkDate[1];
      }
      listScientificTimetable(this.queryParams).then(response => {
        this.scientificTimetableList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        oddNumbers: null,
        applicationDate: null,
        companyCode: null,
        applicant: null,
        jobNumber: null,
        teamLeader: null,
        costCenter: null,
        costCenterName: null,
        remarks: null,
        workDate: null,
        costCenterDetail: null,
        projectNumber: null,
        scientificTime: null,
        documentStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeApplicationDate = [];
      this.daterangeWorkDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/scientificTimetable/export', {
        ...this.queryParams
      }, `scientificTimetable_${new Date().getTime()}.xlsx`)
    },
    /** 导出按钮操作 */
    handleExportAll() {
      this.download('groupFinance/scientificTimetable/exportAll', {
        ...this.queryParams
      }, `scientificTimetableAll_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
