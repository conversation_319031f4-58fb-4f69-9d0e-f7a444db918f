<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeCreatedDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="OA编号" prop="oANumber">
        <el-input
          v-model="queryParams.oANumber"
          placeholder="请输入OA编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司主体" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司主体"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:conferenceReimbursement:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="conferenceReimbursementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="OA编号" align="center" prop="oANumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="创建日期" align="center" prop="createdDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销类别" align="center" prop="reimbursementCategory" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="公司代码" align="center" prop="companyCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="公司主体" align="center" prop="companyName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人" align="center" prop="reimbursementPerson" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人工号" align="center" prop="reimbursementPersonNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="关联会议申请" align="center" prop="relatedMeetingApplication" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务发生日期" align="center" prop="businessOccurrenceDate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务描述" align="center" prop="businessDescription" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="开始日期" align="center" prop="startDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="endDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议名称" align="center" prop="meetingObjective" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会议类型" align="center" prop="meetingType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会议类别" align="center" prop="meetingCategory" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会议形式" align="center" prop="meetingFormat" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="公司角色" align="center" prop="companyRole" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="面向对象" align="center" prop="objectOriented" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="省份" align="center" prop="province" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="城市" align="center" prop="city" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="覆盖人数" align="center" prop="coverNumberPeople" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="签到人数" align="center" prop="signNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="专家人数" align="center" prop="expertsNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="邀请函" align="center" prop="invitation" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="日程表" align="center" prop="schedule" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="签到表" align="center" prop="attendanceSheet" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="照片" align="center" prop="photo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="支付方式" align="center" prop="paymentRecipient" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目代码" align="center" prop="accountCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用科目名称" align="center" prop="expenseAccountName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号码" align="center" prop="invoiceNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="amountWithoutTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="专用发票税额" align="center" prop="specialTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="reimbursementAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="sapCredentialPushDate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP应付凭证号" align="center" prop="sapPayNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款状态" align="center" prop="sapDocumentStatus" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listConferenceReimbursement} from "@/api/groupFinance/conferenceReimbursement";

export default {
  name: "ConferenceReimbursement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会议报销表格数据
      conferenceReimbursementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 付款状态时间范围
      daterangeCreatedDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oANumber: null,
        createdDate: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        department: null,
        relatedMeetingApplication: null,
        costCenterCode: null,
        costCenterName: null,
        businessOccurrenceDate: null,
        businessDescription: null,
        startDate: null,
        endDate: null,
        meetingObjective: null,
        meetingType: null,
        meetingCategory: null,
        meetingFormat: null,
        companyRole: null,
        objectOriented: null,
        province: null,
        city: null,
        coverNumberPeople: null,
        signNumber: null,
        expertsNumber: null,
        invitation: null,
        schedule: null,
        attendanceSheet: null,
        photo: null,
        paymentRecipient: null,
        accountCode: null,
        expenseAccountName: null,
        invoiceNo: null,
        amountWithoutTax: null,
        specialTax: null,
        reimbursementAmount: null,
        currency: null,
        projectNumber: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapCredentialPushDate: null,
        sapPayNumber: null,
        sapDocumentStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会议报销列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreatedDate && '' != this.daterangeCreatedDate) {
        this.queryParams.params["beginCreatedDate"] = this.daterangeCreatedDate[0];
        this.queryParams.params["endCreatedDate"] = this.daterangeCreatedDate[1];
      }
      listConferenceReimbursement(this.queryParams).then(response => {
        this.conferenceReimbursementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        oANumber: null,
        createdDate: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        department: null,
        relatedMeetingApplication: null,
        costCenterCode: null,
        costCenterName: null,
        businessOccurrenceDate: null,
        businessDescription: null,
        startDate: null,
        endDate: null,
        meetingObjective: null,
        meetingType: null,
        meetingCategory: null,
        meetingFormat: null,
        companyRole: null,
        objectOriented: null,
        province: null,
        city: null,
        coverNumberPeople: null,
        signNumber: null,
        expertsNumber: null,
        invitation: null,
        schedule: null,
        attendanceSheet: null,
        photo: null,
        paymentRecipient: null,
        accountCode: null,
        expenseAccountName: null,
        invoiceNo: null,
        amountWithoutTax: null,
        specialTax: null,
        reimbursementAmount: null,
        currency: null,
        projectNumber: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapCredentialPushDate: null,
        sapPayNumber: null,
        sapDocumentStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreatedDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oANumber)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/conferenceReimbursement/export', {
        ...this.queryParams
      }, `conferenceReimbursement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
