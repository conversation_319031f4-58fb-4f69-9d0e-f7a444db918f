<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangeApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="公司主体" prop="companyEntity">
        <el-input
          v-model="queryParams.companyEntity"
          placeholder="请输入公司主体"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="OA单号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入OA单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:logisticsCostDetails:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logisticsCostDetailsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="公司主体" align="center" prop="companyEntity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="工厂" align="center" prop="factory" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA单号" align="center" prop="orderNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="采购订单号" align="center" prop="purchaseOrderNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="费用发生年份" align="center" prop="expenseYear" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用发生月份" align="center" prop="expenseMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商代码" align="center" prop="supplierCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商名称" align="center" prop="supplierName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心" align="center" prop="costCenterCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心描述" align="center" prop="costCenterName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="内部订单号" align="center" prop="internalOrderNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="一级分类" align="center" prop="firstLevelClassification" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="二级分类" align="center" prop="secondaryClassification" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="服务代码" align="center" prop="serviceCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="服务名称" align="center" prop="serviceName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会计科目" align="center" prop="accountingSubjectCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="会计科目描述" align="center" prop="accountingSubjectName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="功能范围" align="center" prop="functionalScope" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="功能范围描述" align="center" prop="functionalScopeName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="quantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单价" align="center" prop="unitPrice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="money" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="税率" align="center" prop="taxRate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remarks" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listLogisticsCostDetails,
  getLogisticsCostDetails,
  delLogisticsCostDetails,
  addLogisticsCostDetails,
  updateLogisticsCostDetails
} from "@/api/groupFinance/logisticsCostDetails";

export default {
  name: "LogisticsCostDetails",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物流费用采购申请明细表格数据
      logisticsCostDetailsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 申请日期时间范围
      daterangeApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyEntity: null,
        factory: null,
        orderNumber: null,
        documentStatus: null,
        currentProcessor: null,
        currentSession: null,
        purchaseOrderNumber: null,
        applicationDate: null,
        expenseYear: null,
        expenseMonth: null,
        applicant: null,
        supplierCode: null,
        supplierName: null,
        costCenterCode: null,
        costCenterName: null,
        projectNumber: null,
        internalOrderNumber: null,
        firstLevelClassification: null,
        secondaryClassification: null,
        serviceCode: null,
        serviceName: null,
        accountingSubjectCode: null,
        accountingSubjectName: null,
        functionalScope: null,
        functionalScopeName: null,
        quantity: null,
        unitPrice: null,
        money: null,
        taxRate: null,
        remarks: null,
        deleteStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物流费用采购申请明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
        this.queryParams.params["beginApplicationDate"] = this.daterangeApplicationDate[0];
        this.queryParams.params["endApplicationDate"] = this.daterangeApplicationDate[1];
      }
      listLogisticsCostDetails(this.queryParams).then(response => {
        this.logisticsCostDetailsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyEntity: null,
        factory: null,
        orderNumber: null,
        documentStatus: null,
        currentProcessor: null,
        currentSession: null,
        purchaseOrderNumber: null,
        applicationDate: null,
        expenseYear: null,
        expenseMonth: null,
        applicant: null,
        supplierCode: null,
        supplierName: null,
        costCenterCode: null,
        costCenterName: null,
        projectNumber: null,
        internalOrderNumber: null,
        firstLevelClassification: null,
        secondaryClassification: null,
        serviceCode: null,
        serviceName: null,
        accountingSubjectCode: null,
        accountingSubjectName: null,
        functionalScope: null,
        functionalScopeName: null,
        quantity: null,
        unitPrice: null,
        money: null,
        taxRate: null,
        remarks: null,
        deleteStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeApplicationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物流费用采购申请明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLogisticsCostDetails(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物流费用采购申请明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLogisticsCostDetails(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLogisticsCostDetails(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除物流费用采购申请明细编号为"' + ids + '"的数据项？').then(function () {
        return delLogisticsCostDetails(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/logisticsCostDetails/export', {
        ...this.queryParams
      }, `logisticsCostDetails_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
