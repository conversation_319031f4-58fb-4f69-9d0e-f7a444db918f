<template>
  <div class="app-container">
    <el-row>
      <div id="echart" ref="echart" >数据加载中...</div>
    </el-row>
  </div>
</template>

<script>
import { listSupplyChainChart} from "@/api/groupFinance/supplyChainChart";

export default {
  name: "SupplyChain",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应链总支出表格数据
      supplyChainList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: new Date().getFullYear()
      },
      //成本中心集合
      costCenterList:[],
      //成本中心对应年月总金额集合
      totalMap:{},
      // 月份总数
      monthlyLength: 0,
      dataMap:{},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.initParam();
  },
  mounted() {
    this.getCharts();
  },
  methods: {
    /** 查询供应链总支出列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      listSupplyChainChart(this.queryParams).then(response => {
        this.supplyChainList = response.rows;
        console.log(this.supplyChainList)
        this.loading = false;
        //获取月份总数
        console.log(this.monthlyLength)
        this.monthlyLength = this.supplyChainList[0].costCenterVoList.length;
        console.log(this.monthlyLength)
        this.initDataMap(this.monthlyLength)
        for (let i = 0; i < this.supplyChainList.length; i++) {
          this.costCenterList.push(this.supplyChainList[i].costCenter);
          for (let j = 0; j < this.monthlyLength; j++) {
            this.totalMap[j + ''].push(this.supplyChainList[i].costCenterVoList[j].total);
          }
        }
        console.log(this.costCenterList)
        console.log(this.totalMap)
        this.getCharts()
      });
    },
    // 初始化数据Map
    initDataMap(monthlyLength) {
      this.option = {}
      this.costCenterList = []
      for (let i = 0; i < monthlyLength; i++) {
        this.totalMap[i + ''] = []
      }
      this.dataMap.total = this.dataFormatter(this.totalMap)
    },
    // 格式化数据
    dataFormatter(obj) {
      let pList = this.costCenterList;
      let temp;
      for (let month = 0; month < this.monthlyLength; month++) {
        let max = 0;
        let sum = 0;
        temp = obj[month];
        for (let i = 0, l = temp.length; i < l; i++) {
          max = Math.max(max, temp[i]);
          sum += temp[i];
          obj[month][i] = {
            name: pList[i],
            value: temp[i]
          };
        }
        obj[month + 'max'] = Math.floor(max / 100) * 100;
        obj[month + 'sum'] = sum;
      }
      return obj;
    },
    // 更新图表
    getCharts() {
      this.updateChartOption()
      //基于准备好的dom，初始化echarts实例
      let departOvertimeChart = this.$echarts.init(this.$refs.echart)
      this.dataMap.total = this.dataFormatter(this.totalMap)
      // 根据部门数量动态设置图表的高度
      departOvertimeChart.setOption(this.option)
      let tempHeight = this.costCenterList.length * 50 + 200
      departOvertimeChart.getDom().style.height = tempHeight + "px";
      departOvertimeChart.resize();
    },
    // 初始化图表参数
    updateChartOption() {
      this.option = {
        baseOption: {
          timeline: {
            axisType: 'category',
            // realtime: false,
            loop: false,
            autoPlay: false,
            // currentIndex: 2,
            playInterval: 5000,
            controlStyle: {
              position: 'right',
              show: false
            },
            data: [
              "1月",
            ],
            label: {
              formatter: function (s) {
                let tempYear = s.substr(0, 4)
                s = s.replace("-", "");
                s = s.replace(tempYear, "")
                s = Number(s)+1 + '月'
                return s;
              }
            }
          },
          title: {
            subtext: '数据来源于OA系统'
          },
          tooltip: {},
          legend: {
            left: 'right',
            data: [],
            selected: {
            }
          },
          calculable: true,
          grid: {
            top: 80,
            bottom: 100,
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
                label: {
                  show: true,
                  formatter: function (params) {
                    return params.value.replace('\n', '');
                  }
                }
              }
            }
          },
          yAxis: [
            {
              type: 'category',
              axisLabel: {interval: 0, rotate: 30,},
              data: this.costCenterList,
              splitLine: {show: false}
            }
          ],
          xAxis: [
            {
              type: 'value',
              name: '金额(元)',
              nameLocation: 'end',
            }
          ],
          series: [
            {name: '月合计', type: 'bar'}
          ]
        },
        options: [
          {
            title: {text:'2023年供应链总支出1月汇总数据'},
            series: [
              {data: this.dataMap.total['0']},
            ]
          },
        ]
      };
      console.log(this.option)
      for (let i = 1; i < this.monthlyLength; i++) {
        if (i < 10) {
          this.option.baseOption.timeline.data.push(this.queryParams.year + "-0" + i)
        } else {
          this.option.baseOption.timeline.data.push(this.queryParams.year + "-" + i)
        }
        let tempOption = {
          title: {text: '2023年供应链总支出'+(i+1)+'月汇总数据'},
          series: [
            {data: this.dataMap.total[i + '']}
          ]
        }
        this.option.options.push(tempOption)
      }
    },
  }
};
</script>
<style lang="scss" scoped>
#echart {
  height: 700px;
  margin: 50px;
}

.dept-selector-btn {
  width: 59px;
  margin: 5px 5px 5px 10px;
}
</style>
