<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="付款日期">
        <el-date-picker
          v-model="daterangePaymentApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收款单位" prop="receivingUnit">
        <el-input
          v-model="queryParams.receivingUnit"
          placeholder="请输入收款单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="付款状态" prop="sapDocumentStatus">
        <el-input
          v-model="queryParams.sapDocumentStatus"
          placeholder="请输入付款状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:presidentOfficeBusiness:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="presidentOfficeBusinessList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="表单名称" align="center" prop="fromName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="OA编号" align="center" prop="oanumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担公司" align="center" prop="costBearingCompany" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款申请日期" align="center" prop="paymentApplicationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentApplicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同号" align="center" prop="contractNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心" align="center" prop="costCenter" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="收款单位" align="center" prop="receivingUnit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="合同类型" align="center" prop="contractType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="款项说明" align="center" prop="paymentDescription" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目代码" align="center" prop="accountCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科目" align="center" prop="suject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="结算账期" align="center" prop="settlementPeriod" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="汇率" align="center" prop="exchangeRate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="amountWithoutTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="专票税额" align="center" prop="specialTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="amount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="付款状态" align="center" prop="sapDocumentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="sapCredentialPushDate" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listPresidentOfficeBusiness, getPresidentOfficeBusiness, delPresidentOfficeBusiness, addPresidentOfficeBusiness, updatePresidentOfficeBusiness } from "@/api/groupFinance/presidentOfficeBusiness";

export default {
  name: "PresidentOfficeBusiness",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 总裁办业务总支出表格数据
      presidentOfficeBusinessList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 凭证推送日期时间范围
      daterangePaymentApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fromName: null,
        OANumber: null,
        costBearingCompany: null,
        paymentApplicationDate: null,
        applicant: null,
        department: null,
        contractNo: null,
        costCenterCode: null,
        costCenter: null,
        projectNumber: null,
        receivingUnit: null,
        contractType: null,
        paymentDescription: null,
        accountCode: null,
        suject: null,
        settlementPeriod: null,
        currency: null,
        exchangeRate: null,
        amountWithoutTax: null,
        specialTax: null,
        amount: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        sapCredentialPushDate: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询总裁办业务总支出列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePaymentApplicationDate && '' != this.daterangePaymentApplicationDate) {
        this.queryParams.params["beginPaymentApplicationDate"] = this.daterangePaymentApplicationDate[0];
        this.queryParams.params["endPaymentApplicationDate"] = this.daterangePaymentApplicationDate[1];
      }
      listPresidentOfficeBusiness(this.queryParams).then(response => {
        this.presidentOfficeBusinessList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        fromName: null,
        OANumber: null,
        costBearingCompany: null,
        paymentApplicationDate: null,
        applicant: null,
        department: null,
        contractNo: null,
        costCenterCode: null,
        costCenter: null,
        projectNumber: null,
        receivingUnit: null,
        contractType: null,
        paymentDescription: null,
        accountCode: null,
        suject: null,
        settlementPeriod: null,
        currency: null,
        exchangeRate: null,
        amountWithoutTax: null,
        specialTax: null,
        amount: null,
        currentSession: null,
        documentStatus: null,
        sapDocumentStatus: null,
        sapCredentialPushDate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePaymentApplicationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fromName)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/presidentOfficeBusiness/export', {
        ...this.queryParams
      }, `presidentOfficeBusiness_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
