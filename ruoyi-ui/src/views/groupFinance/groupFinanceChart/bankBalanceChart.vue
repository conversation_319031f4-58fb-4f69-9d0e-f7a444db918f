<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.date"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="历史余额日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="币别" prop="currencyType">
            <el-select
              v-model="queryParams.currencyType "
              multiple
              style="margin-left: 20px;width:350px"
              placeholder="请选择">
              <el-option v-for="item in currencyOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="bankbalanceChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 10px">
        <div id="bankHistoryBalanceChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {getBankBalanceChart, getBankHistoryBalanceChart} from "@/api/groupFinance/fundGroup";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //银行余额 集合
      bankBalanceVoList: [],
      //银行历史余额 集合
      bankHistoryBalanceVoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        date: "",
        currencyType: ['人民币']
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        currencyType: [{required: true, message: '请选择币别', trigger: 'change'}],
      },
      currencyOptions: [
        {'label': '人民币', 'value': '人民币'},
        {'label': '美元', 'value': '美元'},
        {'label': '欧元', 'value': '欧元'},
        {'label': '港币', 'value': '港币'},
        {'label': '澳币', 'value': '澳币'}
      ]

    };
  },
  created() {
    this.getDate()
    this.getBankBalanceVoList();
    this.getBankHistoryBalanceVoList();
  },
  mounted() {
  },
  methods: {
    getDate() {
      let date = this.$moment(new Date().setDate(new Date().getDate() - 1)).format('YYYY-MM-DD');
      this.queryParams.date = date
    },
    /** 查询 CBS 银行余额  图表数据 */
    async getBankBalanceVoList() {
      this.loading = true;
      this.queryParams.params = {};
      console.log(this.queryParams.date)
      getBankBalanceChart(this.queryParams).then(response => {
        this.bankBalanceVoList = response.rows;
        var a = this.bankBalanceVoList[0].listLegend;
        var b = this.bankBalanceVoList[0].xaxisList;
        var c = this.bankBalanceVoList[0].mapListData;
        this.getBankBalanceVoListChart(a, b, c);
      });
    },
    /** 查询 CBS 银行历史余额  图表数据 */
    async getBankHistoryBalanceVoList() {
      this.loading = true;
      this.queryParams.params = {};
      console.log(this.queryParams)
      // console.log(this.$moment(new Date()).subtract(1, "days").calendar().format('YYYY-MM-DD'))
      getBankHistoryBalanceChart(this.queryParams).then(response => {
        this.bankHistoryBalanceVoList = response.rows;
        var a = this.bankHistoryBalanceVoList[0].listLegend;
        var b = this.bankHistoryBalanceVoList[0].xaxisList;
        var c = this.bankHistoryBalanceVoList[0].mapListData;
        this.getBankHistoryBalanceVoListChart(a, b, c);
      });
    },
    getBankBalanceVoListChart(a, b, c) {
      var chartDom = document.getElementById('bankbalanceChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      const seriesLabel = {
        show: true
      };
      option = {
        title: [{
          text: '银行实时余额',
          subtext: '中山康方（含北京和上海分公司）,康融广东（含康融广州）'
        }, {
          text: '单位: 万元',
          right: '10%'
        }],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: a
        },
        grid: {
          left: 100
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          inverse: true,
          data: b
        },
        yAxis: {
          type: 'value',
          name: '',
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          var x = {};
          let d = map[key];
          x.name = key;
          x.type = 'bar';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (params) {
                if (params.value > 0) {
                  if (params.seriesName == '人民币') {
                    return '¥' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '美元') {
                    return '$' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '欧元') {
                    return '€' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '澳币') {
                    return 'A$ ' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '港币') {
                    return 'HK$ ' + echarts.format.addCommas(params.value);
                  } else {
                    return echarts.format.addCommas(params.value);
                  }
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    getBankHistoryBalanceVoListChart(a, b, c) {
      var chartDom = document.getElementById('bankHistoryBalanceChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      const seriesLabel = {
        show: true
      };
      option = {
        title: [{
          text: '银行历史余额',
          subtext: '中山康方（含北京和上海分公司）,康融广东（含康融广州）'
        }, {
          text: '单位: 万元',
          right: '10%'
        }],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: a
        },
        grid: {
          left: 100
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          inverse: true,
          data: b
        },
        yAxis: {
          type: 'value',
          name: '',
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          var x = {};
          x.name = key;
          x.type = 'bar';
          //x.label=seriesLabel;
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (params) {
                if (params.value > 0) {
                  if (params.seriesName == '人民币') {
                    return '¥' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '美元') {
                    return '$' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '欧元') {
                    return '€' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '澳币') {
                    return 'A$ ' + echarts.format.addCommas(params.value);
                  } else if (params.seriesName == '港币') {
                    return 'HK$ ' + echarts.format.addCommas(params.value);
                  } else {
                    return echarts.format.addCommas(params.value);
                  }
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getBankBalanceVoList();
      this.getBankHistoryBalanceVoList();
    }
  }
};
</script>

