<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">

          <el-form-item label="年月1">
            <el-date-picker
              v-model="queryParams.yearMonth1"
              type="month"
              value-format="yyyy-MM"
              placeholder="请选择年月">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="年月2">
            <el-date-picker
              v-model="queryParams.yearMonth2"
              type="month"
              placeholder="请选择年月"
              value-format="yyyy-MM"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 80px">
        <div id="financialBriefingChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {getFinancialBriefingChart} from "@/api/groupFinance/accountingGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //财务简报 集合
      financialBriefingList: [],
      // 查询参数
      queryParams: {
        yearMonth1: "2023-12",
        yearMonth2: this.$moment(new Date()).format("YYYY-MM")
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getFinancialBriefingChart();
  },
  mounted() {
  },
  methods: {
    async getFinancialBriefingChart() {
      this.loading = true;
      this.queryParams.params = {};
      getFinancialBriefingChart(this.queryParams).then(response => {
        this.financialBriefingList = response.rows;
        var a = this.financialBriefingList[0].projectList;
        var b = this.financialBriefingList[0].mapListData;
        this.getFinancialBriefingChartVoList(a, b);
      });
    },
    getFinancialBriefingChartVoList(a, b) {
      let chartDom = document.getElementById('financialBriefingChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: "财务简报",
          subtext: '金额（百万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
         /* x.barWidth = '50%';
          x.barMaxWidth = '30';*/
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getFinancialBriefingChart();
    }
  }
};
</script>

