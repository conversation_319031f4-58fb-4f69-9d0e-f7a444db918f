<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="110px" :rules="rules">
          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择">
              <el-option
                v-for="item in yearData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="适应症">
            <el-select
              v-model="value"
              :multiple="false"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="remoteMethod"
              :loading="loading"
              style="width: 200px">
              <el-option
                v-for="item in options"
                :label="item.label"
                :value="item.value">
                <!--                <span style="float: left">{{ item.value }}</span>-->
                <!--                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.label }}</span>-->
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="是否肿瘤项目" prop="isProjectList">
            <el-select
              v-model="queryParams.isProjectList"
              multiple
              style="margin-left: 20px;"
              placeholder="请选择">
              <el-option
                v-for="item in isProjectListOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="indicationNumberSummaryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="paymentPeriodNumberAmountChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="roadProjectAmountNumberChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="indicationsProjectNumberSummaryChart" style="width: 100%;height:800px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="yearTop10indicationChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="indicationRfExpenditureComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {
  getIndicationNumberSummaryChart,
  getIndicationsProjectNumberSummaryChart,
  getPaymentPeriodNumberAmountChart,
  getRoadProjectAmountNumberChart,
  getYearTop10indicationChart,
  getIndicationRfExpenditureComparisonChart,
  getIndicationInfo
} from "@/api/groupFinance/feeGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //适应症例数汇总 集合
      indicationNumberSummaryList: [],
      //付款期间例数金额月平均单价 集合
      paymentPeriodNumberAmountList: [],
      //途径项目金额例数 集合
      roadProjectAmountNumberList: [],
      //适应症项目号汇总 集合
      indicationsProjectNumberSummaryList: [],
      //年度前十大支出适应症项目号 集合
      yearTop10indicationList: [],
      //适应症号RF支出对比 集合
      indicationRfExpenditureComparisonList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        year: new Date().getFullYear() - 1,
        indication: '',
        isProjectList: ["肿瘤", "非肿瘤"]
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        isProjectList: [{required: true, message: '请选择是否肿瘤项目', trigger: 'change'}]
      },
      isProjectListOptions: [{label: '肿瘤', value: '肿瘤'}, {label: '非肿瘤', value: '非肿瘤'}],
      options: [],
      states: [],
      value: 'AK101'
    }
  },
  created() {
    this.getYear();
    this.getIndicationNumberSummaryVoList();
    this.getPaymentPeriodNumberAmountVoList();
    this.getRoadProjectAmountNumberVoList();
    this.getIndicationsProjectNumberSummaryVoList();
    this.getYearTop10indicationVoList();
    this.getIndicationRfExpenditureComparisonVoList();
    this.getAllIndication();
  },
  mounted() {
  },
  methods: {
    /** 年份数据 */
    getYear() {
      let year = new Date().getFullYear();
      for (let i = 2022; i < year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 获取适应症选项 */
    getAllIndication() {
      this.loading = true;
      this.queryParams.params = {};
      getIndicationInfo(this.queryParams).then(response => {
        this.states = response.rows;
        this.options = this.states.map(item => {
          return {value: `${item.indication}`, label: `${item.indication}`}
        });
      })
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        this.options = [];
        this.queryParams.indication = query;
        getIndicationInfo(this.queryParams).then(response => {
          this.states = response.rows;
          for (let responseElement of response.rows) {
            this.options.push(
              {
                label: responseElement.indication,
                value: responseElement.indication
              }
            )
          }
          this.loading = false;
        });
      } else {
        this.options = [];
      }
    },
    /** 查询适应症例数汇总 (适应症例数) 图表数据 */
    async getIndicationNumberSummaryVoList() {
      this.loading = true;
      this.queryParams.params = {};
      getIndicationNumberSummaryChart(this.queryParams).then(response => {
        this.indicationNumberSummaryList = response.rows;
        var a = this.indicationNumberSummaryList[0].indicationList;
        var b = this.indicationNumberSummaryList[0].numberList;
        this.getIndicationNumberSummaryVoListChart(a, b);
      });
    },
    /** 查询付款期间例数金额月平均单价 （RF支出情况）  图表数据 */
    async getPaymentPeriodNumberAmountVoList() {
      this.loading = true;
      this.queryParams.params = {};
      getPaymentPeriodNumberAmountChart(this.queryParams).then(response => {
        this.paymentPeriodNumberAmountList = response.rows;
        let listMonth = this.paymentPeriodNumberAmountList[0].listMonth;
        let listLegend = this.paymentPeriodNumberAmountList[0].listLegend;
        var yAxisLeft = this.paymentPeriodNumberAmountList[0].yaxisLeft;
        let yAxisRight = this.paymentPeriodNumberAmountList[0].yaxisRight;
        var amount = this.paymentPeriodNumberAmountList[0].mapAmountList;
        var number = this.paymentPeriodNumberAmountList[0].mapNumberList;
        var unitPrice = this.paymentPeriodNumberAmountList[0].mapUnitPriceList;
        var proportion = this.paymentPeriodNumberAmountList[0].mapProportionList;
        var aKey;
        var aValue;
        var bKey;
        var bValue;
        var cKey;
        var cValue;
        var dKey;
        var dVlaue;
        for (var a in amount) {
          aKey = a;
          aValue = amount[a];
        }
        for (var b in number) {
          bKey = b;
          bValue = number[b];
        }
        for (var c in unitPrice) {
          cKey = c;
          cValue = unitPrice[c];
        }
        for (var d in proportion) {
          dKey = d;
          dVlaue = proportion[d];
        }
        this.getPaymentPeriodNumberAmountVoChart(listMonth, listLegend, yAxisLeft, yAxisRight, aKey, aValue, bKey, bValue, cKey, cValue, dKey, dVlaue);
      });
    },
    /** 查询途径项目金额例数 （各种招募途径的RF支出情况） 图表数据 */
    async getRoadProjectAmountNumberVoList() {
      this.loading = true;
      this.queryParams.params = {};
      getRoadProjectAmountNumberChart(this.queryParams).then(response => {
        this.roadProjectAmountNumberList = response.rows;
        var a = this.roadProjectAmountNumberList[0].listLegend;
        var b = this.roadProjectAmountNumberList[0].xaxisList;
        var c = this.roadProjectAmountNumberList[0].mapListData;
        this.getRoadProjectAmountNumberVoListChart(a, b, c);
      });
    },
    /** 查询适应症项目号汇总 （年度肿瘤、非肿瘤各项目RF支出情况）   图表数据 */
    async getIndicationsProjectNumberSummaryVoList() {
      this.loading = true;
      this.queryParams.params = {};
      getIndicationsProjectNumberSummaryChart(this.queryParams).then(response => {
        this.indicationsProjectNumberSummaryList = response.rows;
        var a = this.indicationsProjectNumberSummaryList[0].listLegend;
        var b = this.indicationsProjectNumberSummaryList[0].xaxisList;
        var c = this.indicationsProjectNumberSummaryList[0].mapListData;
        this.getIndicationsProjectNumberSummaryVoListChart(a, b, c);
      });
    },
    /** 查询年度前十大支出适应症  图表数据 */
    async getYearTop10indicationVoList() {
      this.loading = true;
      this.queryParams.params = {};
      getYearTop10indicationChart(this.queryParams).then(response => {
        this.yearTop10indicationList = response.rows;
        let listProject = this.yearTop10indicationList[0].listProject;
        let listLegend = this.yearTop10indicationList[0].listLegend;
        var yAxisLeft = this.yearTop10indicationList[0].yaxisLeft;
        let yAxisRight = this.yearTop10indicationList[0].yaxisRight;
        var amount = this.yearTop10indicationList[0].mapAmountList;
        var number = this.yearTop10indicationList[0].mapNumberList;
        var unitPrice = this.yearTop10indicationList[0].mapUnitPriceList;
        var aKey;
        var aValue;
        var bKey;
        var bValue;
        var cKey;
        var cValue;
        for (var a in amount) {
          aKey = a;
          aValue = amount[a];
        }
        for (var b in number) {
          bKey = b;
          bValue = number[b];
        }
        for (var c in unitPrice) {
          cKey = c;
          cValue = unitPrice[c];
        }
        this.getYearTop10indicationVoListChart(listProject, listLegend, yAxisLeft, yAxisRight, aKey, aValue, bKey, bValue, cKey, cValue);
      });
    },
    /** 查询适应症号RF支出对比  图表数据 */
    async getIndicationRfExpenditureComparisonVoList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.indication = this.value;
      getIndicationRfExpenditureComparisonChart(this.queryParams).then(response => {
        this.indicationRfExpenditureComparisonList = response.rows;
        let listProject = this.indicationRfExpenditureComparisonList[0].listProject;
        let listLegend = this.indicationRfExpenditureComparisonList[0].listLegend;
        var yAxisLeft = this.indicationRfExpenditureComparisonList[0].yaxisLeft;
        let yAxisRight = this.indicationRfExpenditureComparisonList[0].yaxisRight;
        var amount = this.indicationRfExpenditureComparisonList[0].mapAmountList;
        var number = this.indicationRfExpenditureComparisonList[0].mapNumberList;
        var unitPrice = this.indicationRfExpenditureComparisonList[0].mapUnitPriceList;
        var aKey;
        var aValue;
        var bKey;
        var bValue;
        var cKey;
        var cValue;
        for (var a in amount) {
          aKey = a;
          aValue = amount[a];
        }
        for (var b in number) {
          bKey = b;
          bValue = number[b];
        }
        for (var c in unitPrice) {
          cKey = c;
          cValue = unitPrice[c];
        }
        this.getIndicationRfExpenditureComparisonVoListChart(listProject, listLegend, yAxisLeft, yAxisRight, aKey, aValue, bKey, bValue, cKey, cValue);
      });
    },
    getIndicationNumberSummaryVoListChart(a, b) {
      var chartDom = document.getElementById('indicationNumberSummaryChart');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        title: {
          text: this.queryParams.year + '年适应症例数',
          subtext: '',
          /* textStyle:{
             color: '#333',
             fontSize: 30,
             fontWeight: 'bold'
           }*/
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a,
          axisLabel: {
            interval: 0,
            rotate: 40
          },
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: b,
            type: 'bar',
            label: {
              show: true
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    getPaymentPeriodNumberAmountVoChart(listMonth, listLegend, yAxisLeft, yAxisRight, aKey, aValue, bKey, bValue, cKey, cValue, dKey, dVlaue) {
      let chartDom = document.getElementById('paymentPeriodNumberAmountChart');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: this.queryParams.year + '年RF支出情况',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: {show: true, readOnly: false},
            magicType: {show: true, type: ['line', 'bar']},
            restore: {show: true},
            saveAsImage: {show: true}
          }
        },
        legend: {
          data: listLegend
        },
        xAxis: [
          {
            type: 'category',
            data: listMonth,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: yAxisLeft,
            // min: 0,
            // max: 150000,
            // interval: 10000,
            axisLabel: {
              formatter: '{value}' + ' 万元'
            }
          },
          {
            type: 'value',
            name: yAxisRight,
            // min: 0,
            // max: 150,
            // interval: 10,
            axisLabel: {
              formatter: '{value}' + ' '
            }
          }
        ],
        series: [
          {
            name: aKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 万元';
              }
            },
            data: aValue,
            label: {
              show: true
            }
          },
          {
            name: dKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %';
              }
            },
            data: dVlaue,
            label: {
              show: true
            }
          },
          {
            name: cKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 万元';
              }
            },
            data: cValue,
            label: {
              show: true
            }
          },
          {
            name: bKey,
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' ';
              }
            },
            data: bValue,
            label: {
              show: true
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    getRoadProjectAmountNumberVoListChart(a, b, c) {
      var chartDom = document.getElementById('roadProjectAmountNumberChart');
      var myChart = echarts.init(chartDom);
      var option;

      const seriesLabel = {
        show: true
      };
      option = {
        title: {
          text: this.queryParams.year + '年各种招募途径的RF支出情况',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: a
        },
        grid: {
          left: 100
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          inverse: true,
          data: b
        },
        yAxis: {
          type: 'value',
          name: '',
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          var x = {};
          x.name = key;
          x.type = 'bar';
          x.label = seriesLabel;
          x.data = map[key];
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    getIndicationsProjectNumberSummaryVoListChart(a, b, c) {
      var chartDom = document.getElementById('indicationsProjectNumberSummaryChart');
      var myChart = echarts.init(chartDom);
      var option;
      const seriesLabel = {
        show: true
      };
      option = {
        title: {
          text: this.queryParams.year + '年度肿瘤、非肿瘤各项目RF支出情况',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: a
        },
        grid: {
          left: 100
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: {}
          }
        },
        // 交换X轴和Y轴的角色
        xAxis: {
          type: 'value', // 现在Y轴为数值轴
          name: '',
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          type: 'category', // 现在X轴为分类轴
          data: b, // 分类数据
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        series: [],
        // 添加水平滚动条
        dataZoom: [{
          type: 'slider',
          show: true,
          orient: 'vertical', // 水平方向
          start: 0, // 初始位置
          end: 40, // 初始位置
          handleSize: '80%',
          backgroundColor: 'rgba(0,0,0,0)',
          fillerColor: 'rgba(181, 195, 52, 0.2)',
          dataBackground: {
            lineStyle: {opacity: 0.3},
            areaStyle: {opacity: 0.3}
          },
          handleStyle: {
            color: '#333',
            borderColor: '#999',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          }
        }]
      };

      // 处理数据并生成系列
      for (var i = 0; i < a.length; i++) {
        var key = a[i];
        var series = {
          name: key,
          type: 'bar',
          label: seriesLabel,
          data: c[0][key] || [] // 直接使用分类数据对应的数值数组
        };
        option.series.push(series);
      }
      option && myChart.setOption(option);
    },
    getYearTop10indicationVoListChart(listProject, listLegend, yAxisLeft, yAxisRight, aKey, aValue, bKey, bValue, cKey, cValue) {
      let chartDom = document.getElementById('yearTop10indicationChart');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: this.queryParams.year + '年度前十大支出适应症',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: {show: true, readOnly: false},
            magicType: {show: true, type: ['line', 'bar']},
            restore: {show: true},
            saveAsImage: {show: true}
          }
        },
        legend: {
          data: listLegend
        },
        xAxis: [
          {
            type: 'category',
            data: listProject,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: yAxisLeft,
            // min: 0,
            // max: 150000,
            // interval: 10000,
            axisLabel: {
              formatter: '{value}' + ' 万元'
            }
          },
          {
            type: 'value',
            name: yAxisRight,
            // min: 0,
            // max: 150,
            // interval: 10,
            axisLabel: {
              formatter: '{value}' + ' '
            }
          }
        ],
        series: [
          {
            name: aKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 万元';
              }
            },
            data: aValue,
            label: {
              show: true
            }
          },
          {
            name: cKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 万元';
              }
            },
            data: cValue,
            label: {
              show: true
            }
          },
          {
            name: bKey,
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' ';
              }
            },
            data: bValue,
            label: {
              show: true
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    getIndicationRfExpenditureComparisonVoListChart(listProject, listLegend, yAxisLeft, yAxisRight, aKey, aValue, bKey, bValue, cKey, cValue) {
      let chartDom = document.getElementById('indicationRfExpenditureComparisonChart');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: this.queryParams.year + '年适应症号RF支出对比',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: {show: true, readOnly: false},
            magicType: {show: true, type: ['line', 'bar']},
            restore: {show: true},
            saveAsImage: {show: true}
          }
        },
        legend: {
          data: listLegend
        },
        xAxis: [
          {
            type: 'category',
            data: listProject,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: yAxisLeft,
            // min: 0,
            // max: 150000,
            // interval: 10000,
            axisLabel: {
              formatter: '{value}' + ' 万元'
            }
          },
          {
            type: 'value',
            name: yAxisRight,
            // min: 0,
            // max: 150,
            // interval: 10,
            axisLabel: {
              formatter: '{value}' + ' '
            }
          }
        ],
        series: [
          {
            name: aKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 万元';
              }
            },
            data: aValue,
            label: {
              show: true
            }
          },
          {
            name: cKey,
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 万元';
              }
            },
            data: cValue,
            label: {
              show: true
            }
          },
          {
            name: bKey,
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' ';
              }
            },
            data: bValue,
            label: {
              show: true
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    // 生成随机的渐变色理
    getRandomGradientColor() {
      return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {offset: 0, color: 'rgb(255,162,0)'}, // 渐变开始颜色（红色）
        {offset: 1, color: 'rgb(128,0,255)'}  // 渐变结束颜色（蓝色）
      ]);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getIndicationNumberSummaryVoList();
      this.getPaymentPeriodNumberAmountVoList();
      this.getRoadProjectAmountNumberVoList();
      this.getIndicationsProjectNumberSummaryVoList();
      this.getIndicationRfExpenditureComparisonVoList();
    }
  }
};
</script>

