<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择" style="margin-left: 20px;width:250px">
              <el-option v-for="item in yearData"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="公司主体" prop="companyName">
            <el-select
              v-model="queryParams.companyName"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllCompany">全选</el-button>
                <!--            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>-->
                <el-button type="info" class="dept-selector-btn" @click="clearSelectCompany">清空</el-button>
              </el-row>
              <el-option
                v-for="item in companyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="科目类型" prop="subjectType">
            <el-select
              v-model="queryParams.subjectType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllSubject">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectSubject">清空</el-button>
              </el-row>
              <el-option v-for="item in subjectOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="二级部门" prop="departmentType">
            <el-select
              v-model="queryParams.departmentType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllDepartment">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectDepartment">清空</el-button>
              </el-row>
              <el-option v-for="item in departmentOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="月份" prop="monthlyType">
            <el-select
              v-model="queryParams.monthlyType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllMonthly">全选</el-button>
                <!--            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>-->
                <el-button type="info" class="dept-selector-btn" @click="clearSelectMonthly">清空</el-button>
              </el-row>
              <el-option
                v-for="item in monthlyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="salesExpensesCompanyComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="salesExpensesSubjectContemporaneousComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="salesExpensesSubjectComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="salesExpensesDepartmentContrastChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="salesExpensesDepartmentMonthComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {
  getSalesExpensesSubjectComparisonChart,
  getSalesExpensesDepartmentContrastChart,
  getSalesExpensesDepartmentMonthComparisonChart,
  getSalesExpensesCompanyComparisonChart,
  getSalesExpensesSubjectContemporaneousComparisonChart
} from "@/api/groupFinance/accountingGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //销售费用各科目对比 集合
      salesExpensesSubjectComparisonList: [],
      //销售费用各部门对比 集合
      salesExpensesDepartmentContrastList: [],
      //销售费用各部门月份对比 集合
      salesExpensesDepartmentMonthComparisonList: [],
      //销售费用各公司同期对比 集合
      salesExpensesCompanyComparisonList: [],
      //销售费用各科目同期对比 集合
      salesExpensesSubjectContemporaneousComparisonList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        monthlyType: [],
        companyName: [],
        subjectType: [],
        departmentType: [],
        year: new Date().getFullYear()
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        monthlyType: [{required: true, message: '请选择月份', trigger: 'change'}],
        companyName: [{required: true, message: '请选择公司主体', trigger: 'change'}],
        subjectType: [{required: true, message: '请选择科目类型', trigger: 'change'}],
        departmentType: [{required: true, message: '请选择二级部门', trigger: 'change'}]
      },
      companyOptions: [
        {value: '中山康方生物医药有限公司', label: '中山康方生物医药有限公司'},
        {value: '康方天成（广东）制药有限公司', label: '康方天成（广东）制药有限公司'},
        {value: '康方赛诺医药有限公司', label: '康方赛诺医药有限公司'},
        {value: '康方药业有限公司', label: '康方药业有限公司'},
        {value: '康融东方（广东）医药有限公司', label: '康融东方（广东）医药有限公司'},
        {value: '康融东方（广州）生物医药有限公司', label: '康融东方（广州）生物医药有限公司'},
        {value: '康方隆跃（广东）科技有限公司', label: '康方隆跃（广东）科技有限公司'},
        {value: '中山康方生物医药有限公司北京分公司', label: '中山康方生物医药有限公司北京分公司'},
        {value: '康方添成科技（上海）有限公司', label: '康方添成科技（上海）有限公司'},
        {value: '中山康方生物医药有限公司上海分公司', label: '中山康方生物医药有限公司上海分公司'},
        {value: '康方汇科（上海）生物有限公司', label: '康方汇科（上海）生物有限公司'},
        {value: '康方汇科（广州）生物有限公司', label: '康方汇科（广州）生物有限公司'},
        {value: '康方中国有限公司', label: '康方中国有限公司'},
        {value: '泽昇医药（广东）有限公司', label: '泽昇医药（广东）有限公司'},
        {value: '康方生物医药（美国）有限公司', label: '康方生物医药（美国）有限公司'},
        {value: '康方生物医药（澳大利亚）有限公司', label: '康方生物医药（澳大利亚）有限公司'},
        {value: '康方生物科技（开曼）有限公司', label: '康方生物科技（开曼）有限公司'},
        {value: 'AKESO (BVI), INC.', label: 'AKESO (BVI), INC.'}],
      subjectOptions: [
        {value: '职工薪酬', label: '职工薪酬'},
        {value: '会议费', label: '会议费'},
        {value: '业务招待费', label: '业务招待费'},
        {value: '差旅费', label: '差旅费'},
        {value: '服务费', label: '服务费'},
        {value: '办公费 ', label: '办公费'},
        {value: '折旧摊销', label: '折旧摊销'},
        {value: '租赁费用', label: '租赁费用'},
        {value: '水电物管费', label: '水电物管费'},
        {value: '赠药费用', label: '赠药费用'},
        {value: '产能调整', label: '产能调整'},
        {value: '其他', label: '其他'}
      ],
      departmentOptions: [
        {value: '首席执行官 ', label: '首席执行官 '},
        {value: '首席财务官 ', label: '首席财务官 '},
        {value: '企业运营', label: '企业运营'},
        {value: '独立董事', label: '独立董事'},
        {value: '商业运营', label: '商业运营'},
        {value: '商务拓展', label: '商务拓展'},
        {value: '生产质量', label: '生产质量'},
        {value: 'CMC', label: 'CMC'},
        {value: '临床开发一部门', label: '临床开发一部门'},
        {value: '临床开发二部门', label: '临床开发二部门'},
        {value: '临床开发三部门', label: '临床开发三部门'},
        {value: '临床开发五部门', label: '临床开发五部门'},
        {value: '药物警戒', label: '药物警戒'},
        {value: '临床运行', label: '临床运行'},
        {value: '外联事务', label: '外联事务'},
        {value: '药物研发', label: '药物研发'},
        {value: '临床策略', label: '临床策略'},
        {value: '生物统计学', label: '生物统计学'},
        {value: '临床质量控制', label: '临床质量控制'},
        {value: '生产部结账专用成本中心', label: '生产部结账专用成本中心'},
        {value: '研发费用的分摊分配', label: '研发费用的分摊分配'},
        {value: '管理费用的分摊分配', label: '管理费用的分摊分配'},
        {value: '制造费用的分摊分配', label: '制造费用的分摊分配'},
        {value: '1000中山康方公摊费用', label: '1000中山康方公摊费用'}
      ],
      monthlyOptions: [
        {value: '1月', label: '1月'},
        {value: '2月', label: '2月'},
        {value: '3月', label: '3月'},
        {value: '4月', label: '4月'},
        {value: '5月', label: '5月'},
        {value: '6月', label: '6月'},
        {value: '7月', label: '7月'},
        {value: '8月', label: '8月'},
        {value: '9月', label: '9月'},
        {value: '10月', label: '10月'},
        {value: '11月', label: '11月'},
        {value: '12月', label: '12月'}
      ]
    };
  },
  created() {
    this.getYear();
    this.selectAllMonthly();
    this.selectAllCompany();
    this.selectAllSubject();
    this.selectAllDepartment();
    this.getSalesExpensesSubjectComparisonChart();
    this.getSalesExpensesDepartmentContrastChart();
    this.getSalesExpensesDepartmentMonthComparisonChart();
    this.getSalesExpensesCompanyComparisonChart();
    this.getSalesExpensesSubjectContemporaneousComparisonChart();
  },
  mounted() {
  },
  methods: {
    /** 全选月份 */
    selectAllMonthly() {
      this.queryParams.monthlyType = this.monthlyOptions.map(a => a.value);
    },
    /** 清空月份 */
    clearSelectMonthly() {
      this.queryParams.monthlyType = [];
    },
    /** 全选公司主体 */
    selectAllCompany() {
      this.queryParams.companyName = this.companyOptions.map(a => a.value)
    },
    /** 清空公司主体 */
    clearSelectCompany() {
      this.queryParams.companyName = []
    },
    /** 全选科目类型 */
    selectAllSubject() {
      this.queryParams.subjectType = this.subjectOptions.map(a => a.value)
    },
    /** 清空科目类型 */
    clearSelectSubject() {
      this.queryParams.subjectType = []
    },
    /** 全选二级部门 */
    selectAllDepartment() {
      this.queryParams.departmentType = this.departmentOptions.map(a => a.value)
    },
    /** 清空二级部门 */
    clearSelectDepartment() {
      this.queryParams.departmentType = []
    },
    /** 获取当前年份 */
    async getYear() {
      let year = new Date().getFullYear();
      for (let i = 2022; i <= year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询销售费用各科目对比   图表数据 */
    async getSalesExpensesSubjectComparisonChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSalesExpensesSubjectComparisonChart(this.queryParams).then(response => {
        this.salesExpensesSubjectComparisonList = response.rows;
        var a = this.salesExpensesSubjectComparisonList[0].companyList;
        var b = this.salesExpensesSubjectComparisonList[0].subjectList;
        var c = this.salesExpensesSubjectComparisonList[0].mapListData;
        this.getSalesExpensesSubjectComparisonChartVoList(a, b, c);
      });
    },
    getSalesExpensesSubjectComparisonChartVoList(a, b, c) {
      let chartDom = document.getElementById('salesExpensesSubjectComparisonChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '销售费用各科目对比',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
          // min: 0,
          // max: 5000,
          // interval:1000,
          // axisLabel: {
          //   formatter: '{value}'
          // }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          console.log(key + ': ' + map[key]);
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询销售费用各部门对比   图表数据 */
    async getSalesExpensesDepartmentContrastChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSalesExpensesDepartmentContrastChart(this.queryParams).then(response => {
        this.salesExpensesDepartmentContrastList = response.rows;
        var a = this.salesExpensesDepartmentContrastList[0].departmentList;
        var b = this.salesExpensesDepartmentContrastList[0].subjectList;
        var c = this.salesExpensesDepartmentContrastList[0].mapListData;
        this.getSalesExpensesDepartmentContrastChartVoList(a, b, c);
      });
    },
    getSalesExpensesDepartmentContrastChartVoList(a, b, c) {
      let chartDom = document.getElementById('salesExpensesDepartmentContrastChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '销售费用各部门对比',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
          // min: 0,
          // max: 5000,
          // interval:1000,
          // axisLabel: {
          //   formatter: '{value}'
          // }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          console.log(key + ': ' + map[key]);
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询销售费用各部门月份对比   图表数据 */
    async getSalesExpensesDepartmentMonthComparisonChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSalesExpensesDepartmentMonthComparisonChart(this.queryParams).then(response => {
        this.salesExpensesDepartmentMonthComparisonList = response.rows;
        var a = this.salesExpensesDepartmentMonthComparisonList[0].departmentList;
        var b = this.salesExpensesDepartmentMonthComparisonList[0].monthlyList;
        var c = this.salesExpensesDepartmentMonthComparisonList[0].mapListData;
        this.getSalesExpensesDepartmentMonthComparisonChartVoList(a, b, c);
      });
    },
    getSalesExpensesDepartmentMonthComparisonChartVoList(a, b, c) {
      let chartDom = document.getElementById('salesExpensesDepartmentMonthComparisonChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '销售费用各部门月份对比',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%',
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          console.log(key + ': ' + map[key]);
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询销售费用各公司同期对比   图表数据 */
    async getSalesExpensesCompanyComparisonChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSalesExpensesCompanyComparisonChart(this.queryParams).then(response => {
        this.salesExpensesCompanyComparisonList = response.rows;
        var a = this.salesExpensesCompanyComparisonList[0].listLegend;
        var b = this.salesExpensesCompanyComparisonList[0].companyList;
        var c = this.salesExpensesCompanyComparisonList[0].mapListData;
        this.getSalesExpensesCompanyComparisonChartVoList(a, b, c);
      });
    },
    getSalesExpensesCompanyComparisonChartVoList(a, b, c) {
      let chartDom = document.getElementById('salesExpensesCompanyComparisonChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: "销售费用各公司同期对比",
          subtext: '中山康方（含北京和上海分公司），康融广东（含康融广州），境外公司（含康方中国、开曼、澳洲、美国、BVI）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: b
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < c.length; i++) {
        var map = c[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          x.barWidth = '50%';
          x.barMaxWidth = '30';
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    /** 查询销售费用各科目同期对比   图表数据 */
    async getSalesExpensesSubjectContemporaneousComparisonChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSalesExpensesSubjectContemporaneousComparisonChart(this.queryParams).then(response => {
        this.salesExpensesSubjectContemporaneousComparisonList = response.rows;
        var a = this.salesExpensesSubjectContemporaneousComparisonList[0].subjectList;
        var b = this.salesExpensesSubjectContemporaneousComparisonList[0].mapListData;
        this.getSalesExpensesSubjectContemporaneousComparisonChartVoList(a, b);
      });
    },
    getSalesExpensesSubjectContemporaneousComparisonChartVoList(a, b) {
      let chartDom = document.getElementById('salesExpensesSubjectContemporaneousComparisonChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: "销售费用各科目同期对比",
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          x.barWidth = '50%';
          x.barMaxWidth = '30';
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getSalesExpensesSubjectComparisonChart();
      this.getSalesExpensesDepartmentContrastChart();
      this.getSalesExpensesDepartmentMonthComparisonChart();
      this.getSalesExpensesCompanyComparisonChart();
      this.getSalesExpensesSubjectContemporaneousComparisonChart();
    }
  }
};
</script>

