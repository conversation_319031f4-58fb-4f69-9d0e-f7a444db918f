<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择">
              <el-option
                v-for="item in yearData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="一级部门" prop="departmentType">
            <el-select
              v-model="queryParams.departmentType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllDepartment">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectDepartment">清空</el-button>
              </el-row>
              <el-option v-for="item in departmentOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="groupCompanyLoanChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="departmentMonthLoanChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="departmentSummaryLoanChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {getDepartmentMonthLoanChart, getGroupCompanyLoanChart,getDepartmentSummaryLoanChart} from "@/api/groupFinance/feeGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //集团分公司各月借款情况 集合
      groupCompanyLoanList: [],
      //各部门各月借款情况 集合
      departmentMonthLoanList: [],
      //各部门借款汇总情况 集合
      departmentSummaryLoanList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        year: new Date().getFullYear(),
        departmentType: []
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        departmentType: [{required: true, message: '请选择一级部门', trigger: 'change'}]
      },
      departmentOptions: [
        {label: '总裁办', value: '总裁办'},
        {label: '运营部门', value: '运营部门'},
        {label: '财务部', value: '财务部'},
        {label: '人力资源部', value: '人力资源部'},
        {label: '外联事务部门', value: '外联事务部门'},
        {label: '药物研发部门', value: '药物研发部门'},
        {label: '生产与质量部门', value: '生产与质量部门'},
        {label: '工艺开发与生产科学技术部', value: '工艺开发与生产科学技术部'},
        {label: '临床运行部门', value: '临床运行部门'},
        {label: '药物警戒部', value: '药物警戒部'},
        {label: '生物统计学部', value: '生物统计学部'},
        {label: '临床开发三部', value: '临床开发三部'},
        {label: '临床开发二部', value: '临床开发二部'},
        {label: '临床开发五部', value: '临床开发五部'},
        {label: '临床开发一部', value: '临床开发一部'},
        {label: '临床质量控制部', value: '临床质量控制部'},
        {label: '商业运营部', value: '商业运营部'},
        {label: '审计部', value: '审计部'}
      ]
    };
  },
  created() {
    this.getYear();
    this.selectAllDepartment();
    this.getGroupCompanyLoanVoList();
    this.getDepartmentMonthLoanChart();
    this.getDepartmentSummaryLoanChart();
  },
  mounted() {
  },
  methods: {
    /** 全选一级部门 */
    selectAllDepartment() {
      this.queryParams.departmentType = this.departmentOptions.map(a => a.value)
    },
    /** 清空一级部门 */
    clearSelectDepartment() {
      this.queryParams.departmentType = []
    },
    getYear() {
      let year = new Date().getFullYear();
      for (let i = 2022; i <= year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询 集团分公司各月借款情况  图表数据 */
    async getGroupCompanyLoanVoList() {
      this.loading = true;
      this.queryParams.params = {};
      getGroupCompanyLoanChart(this.queryParams).then(response => {
        this.groupCompanyLoanList = response.rows;
        var a = this.groupCompanyLoanList[0].companyList;
        var b = this.groupCompanyLoanList[0].listMonth;
        var c = this.groupCompanyLoanList[0].mapListData;
        this.getGroupCompanyLoanVoListChart(a, b, c);
      });
    },
    getGroupCompanyLoanVoListChart(a, b, c) {
      let chartDom = document.getElementById('groupCompanyLoanChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '集团分公司各月借款情况',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let total = params.reduce((acc, curr) => acc + curr.data, 0).toFixed(2);
            let result = `${params[0].axisValue}<br/>`;
            params.forEach(item => {
              if (item.value != 0) {
                result += `${item.marker} ${item.seriesName}: ${item.data}<br/>`;
              }
            });
            if (total != 0) {
              result += `合计金额: ${total}`;
              return result;
            } else {
              return '';
            }
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            interval: 0,
            rotate: 40
          },
          data: b
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询 各部门各月借款情况  图表数据 */
    async getDepartmentMonthLoanChart() {
      this.loading = true;
      this.queryParams.params = {};
      getDepartmentMonthLoanChart(this.queryParams).then(response => {
        this.departmentMonthLoanList = response.rows;
        var a = this.departmentMonthLoanList[0].departmentList;
        var b = this.departmentMonthLoanList[0].listMonth;
        var c = this.departmentMonthLoanList[0].mapListData;
        this.getDepartmentMonthLoanChartVoList(a, b, c);
      });
    },
    getDepartmentMonthLoanChartVoList(a, b, c) {
      let chartDom = document.getElementById('departmentMonthLoanChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '集团各部门各月借款情况',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let total = params.reduce((acc, curr) => acc + curr.data, 0).toFixed(2);
            let result = `${params[0].axisValue}<br/>`;
            params.forEach(item => {
              if (item.value != 0) {
                result += `${item.marker} ${item.seriesName}: ${item.data}<br/>`;
              }
            });
            if (total != 0) {
              result += `合计金额: ${total}`;
              return result;
            } else {
              return '';
            }
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "25%",
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            interval: 0,
            rotate: 40
          },
          data: b
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'left',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询 各部门借款汇总情况 图表数据 */
    async getDepartmentSummaryLoanChart() {
      this.loading = true;
      this.queryParams.params = {};
      getDepartmentSummaryLoanChart(this.queryParams).then(response => {
        this.departmentSummaryLoanList = response.rows;
        let a = this.departmentSummaryLoanList[0].departmentList;
        let b = this.departmentSummaryLoanList[0].mapListData;
        console.log(a)
        console.log(b)
        this.getDepartmentSummaryLoanChartVoList(a, b);
      });
    },
    getDepartmentSummaryLoanChartVoList(a, b) {
      let chartDom = document.getElementById('departmentSummaryLoanChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;
      option = {
        title: {
          text: '集团各部门借款汇总情况',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          x: 'center',
          y: 'bottom',
          /* bottom: 0,*/
          data: a
        },
        series: [
          {
            name: ' ',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: b,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontWeight: 200,
                    fontsize: 16
                  },
                  formatter: function (params){
                    return echarts.format.addCommas(params.value)+'万元('+ params.percent+'%)';
                  }
                },
                labelLine: {
                  show: true,
                  smooth: 0.2,
                  length: 10,
                  length2: 20
                }
              }
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getGroupCompanyLoanVoList();
      this.getDepartmentMonthLoanChart();
      this.getDepartmentSummaryLoanChart();
    }
  }
};
</script>

