<template>
  <div>
    <div class="app-container" style="height: 20px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
          <el-form-item label="物料描述">
            <el-select
              v-model="value"
              :multiple="false"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="remoteMethod"
              :loading="loading"
              style="width: 400px">
              <el-option
                v-for="item in options"
                :label="item.label"
                :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="库存日期">
            <el-date-picker
              v-model="queryParams.date"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择库存日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 20px">
        <div id="materialInventoryChart1" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 10px">
        <div id="materialInventoryChart2" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {getMaterialInventoryChart, getMaterialInfo} from "@/api/groupFinance/researchDevelopmentGroup";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //物料库存 集合
      materialInventoryVoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        materialDescription:'',
        materialCode:'',
        date: this.$moment().format('YYYY-MM-DD')
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      options: [],
      states: [],
      value:[]
    };
  },
  created() {
    this.getMaterialInventoryVoList();
    this.loadAll();
  },
  mounted() {

  },
  methods: {
    loadAll() {
      this.loading = true;
      this.queryParams.params = {};
      getMaterialInfo(this.queryParams).then(response => {
        this.states = response.rows;
        this.options = this.states.map(item => {
          return {value: `${item.materialNo}`, label: `${item.materialDescription}`};
        });
      });
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        this.options=[];
        // setTimeout(() => {
        //   this.loading = false;
        //   this.options = this.options.filter(item => {
        //     return item.label.toLowerCase()
        //       .indexOf(query.toLowerCase()) > -1;
        //   });
        // }, 200);
        this.queryParams.materialDescription=query;
        getMaterialInfo(this.queryParams).then(response => {
          this.states = response.rows;
          for (let responseElement of response.rows) {
            this.options.push(
              {
                value:responseElement.materialNo,
                label:responseElement.materialDescription
              }
            )
          }
          this.loading = false;
        });
      } else {
        this.options = [];
      }
    },
    /** 查询 研发组  物料库存 图表数据 */
    async getMaterialInventoryVoList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.materialCode=this.value;
      console.log(this.queryParams)
      getMaterialInventoryChart(this.queryParams).then(response => {
        this.materialInventoryVoList = response.rows;
        var a = this.materialInventoryVoList[0].companyList;
        var b = this.materialInventoryVoList[0].inventoryQuantityMapList;
        var c = this.materialInventoryVoList[0].inventoryAmountMapList;
        var d = this.materialInventoryVoList[0].transitQuantityMapList;
        var e = this.materialInventoryVoList[0].transitAmountMapList;
        var f = this.materialInventoryVoList[0].expiredQuantityMapList;
        var g = this.materialInventoryVoList[0].expiredAmountMapList;
        this.getMaterialInventoryVoListChart1(a, b, d, f);
        this.getMaterialInventoryVoListChart2(a, c, e, g);
      });
    },
    getMaterialInventoryVoListChart1(a, b, d, f) {
      var bKey;
      var bValue;
      var dKey;
      var dValue;
      var fKey;
      var fValue;
      for (var key in b) {
        bKey = key;
        bValue = b[key]
      }
      for (var key in d) {
        dKey = key;
        dValue = d[key]
      }
      for (var key in f) {
        fKey = key;
        fValue = f[key]
      }
      var chartDom = document.getElementById('materialInventoryChart1');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        title: {
          text: '临床外购药库存情况（数量）'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: a
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: fKey,
            type: 'bar',
            stack: 'number',
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return echarts.format.addCommas(params.value);
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: fValue
          },
          {
            name: bKey,
            type: 'bar',
            stack: 'number',
            barWidth: 50,
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return echarts.format.addCommas(params.value);
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: bValue
          },
          {
            name: dKey,
            type: 'bar',
            stack: 'number',
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return echarts.format.addCommas(params.value);
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: dValue
          }
        ]
      };
      option && myChart.setOption(option);
    },
    getMaterialInventoryVoListChart2(a, c, e, g) {
      var cKey;
      var cValue;
      var eKey;
      var eValue;
      var gKey;
      var gValue;
      for (var key in c) {
        cKey = key;
        cValue = c[key]
      }
      for (var key in e) {
        eKey = key;
        eValue = e[key]
      }
      for (var key in g) {
        gKey = key;
        gValue = g[key]
      }
      var chartDom = document.getElementById('materialInventoryChart2');
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        title: {
          text: '临床外购药库存情况（金额）'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: a
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: gKey,
            type: 'bar',
            stack: 'money',
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return echarts.format.addCommas(params.value);
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: gValue
          },
          {
            name: cKey,
            type: 'bar',
            stack: 'money',
            barWidth: 50,
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return echarts.format.addCommas(params.value);
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: cValue
          },
          {
            name: eKey,
            type: 'bar',
            stack: 'money',
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: function (params) {
                  return echarts.format.addCommas(params.value);
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: eValue
          }
        ]
      };
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getMaterialInventoryVoList();
    },
    // 表单重置
    reset() {
      this.form = {
        value: null,
        date: null
      };
      this.resetForm("form");
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.value=[];
      this.queryParams={};
      this.handleQuery();
    },
  }
};
</script>
