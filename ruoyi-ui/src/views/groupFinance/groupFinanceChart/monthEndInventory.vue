<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择" style="margin-left: 20px;width:250px">
              <el-option v-for="item in yearData"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="年月">
            <el-date-picker
              v-model="queryParams.yearMonth"
              type="month"
              value-format="yyyy-MM"
              placeholder="请选择年月">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="月份" prop="monthlyType">
            <el-select
              v-model="queryParams.monthlyType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllMonthly">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectMonthly">清空</el-button>
              </el-row>
              <el-option
                v-for="item in monthlyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="公司主体" prop="companyName">
            <el-select
              v-model="queryParams.companyName"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllCompany">全选</el-button>
                <!--            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>-->
                <el-button type="info" class="dept-selector-btn" @click="clearSelectCompany">清空</el-button>
              </el-row>
              <el-option
                v-for="item in companyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="科目类型" prop="subjectType">
            <el-select
              v-model="queryParams.subjectType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllSubject">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectSubject">清空</el-button>
              </el-row>
              <el-option v-for="item in subjectOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="companyMonthlyEndingInventoryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="companyEndPeriodInventoryAmountSummaryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="top5CompanyEndPeriodInventoryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="companyMonthlyEndingInventoryQOQChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="inventoryYearContrastChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="nearTwelveMonthEndingInventoryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {
  getCompanyMonthlyEndingInventoryChart,
  getCompanyEndPeriodInventoryAmountSummaryChart,
  getTop5CompanyEndPeriodInventoryChart,
  getCompanyMonthlyEndingInventoryQOQChart,
  getInventoryYearContrastChart,
  getNearTwelveMonthEndingInventoryChart, getCompanyOperatingExpensesChart
} from "@/api/groupFinance/supplyChainGroup";
import {getDebtPayingAbilityChart} from "@/api/groupFinance/accountingGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //公司月度期末库存 集合
      companyMonthlyEndingInventoryList: [],
      //公司期末库存金额汇总 集合
      companyEndPeriodInventoryAmountSummaryList: [],
      //公司前五大期末库存 集合
      top5CompanyEndPeriodInventoryList: [],
      //公司月度期末库存环比变动 集合
      companyMonthlyEndingInventoryQOQList: [],
      //库存年度对比 集合
      inventoryYearContrastList: [],
      //康方集团近12个月期末库存 集合
      nearTwelveMonthEndingInventoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        monthlyType: [],
        companyName: [],
        subjectType: [],
        year: new Date().getFullYear(),
        yearMonth: this.$moment(new Date()).format("YYYY-MM")
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        monthlyType: [{required: true, message: '请选择月份', trigger: 'change'}],
        companyName: [{required: true, message: '请选择公司主体', trigger: 'change'}],
        subjectType: [{required: true, message: '请选择科目类型', trigger: 'change'}],
        yearMonth: [{required: true, message: '请选择年月', trigger: 'change'}],
      },
      companyOptions: [
        {value: '中山康方生物医药有限公司', label: '中山康方生物医药有限公司'},
        {value: '中山康方生物医药有限公司北京分公司', label: '中山康方生物医药有限公司北京分公司'},
        {value: '中山康方生物医药有限公司上海分公司', label: '中山康方生物医药有限公司上海分公司'},
        {value: '康方天成（广东）制药有限公司', label: '康方天成（广东）制药有限公司'},
        {value: '康方赛诺医药有限公司', label: '康方赛诺医药有限公司'},
        {value: '康方药业有限公司', label: '康方药业有限公司'},
        {value: '康融东方（广东）医药有限公司', label: '康融东方（广东）医药有限公司'},
        {value: '康融东方（广州）生物医药有限公司', label: '康融东方（广州）生物医药有限公司'},
        {value: '正大天晴康方（上海）生物医药科技有限公司', label: '正大天晴康方（上海）生物医药科技有限公司'},
        {value: '康方汇科（上海）生物有限公司', label: '康方汇科（上海）生物有限公司'},
        {value: '康方汇科（广州）生物有限公司', label: '康方汇科（广州）生物有限公司'},
        {value: '康方隆跃（广东）科技有限公司', label: '康方隆跃（广东）科技有限公司'},
        {value: '泽昇医药 (广东)有限公司', label: '泽昇医药 (广东)有限公司'},
        {value: '康方中国有限公司', label: '康方中国有限公司'},
        {value: '康方生物医药（美国）有限公司', label: '康方生物医药（美国）有限公司'},
        {value: '康方生物医药（澳大利亚）有限公司', label: '康方生物医药（澳大利亚）有限公司'},
        {value: '康方生物科技（开曼）有限公司', label: '康方生物科技（开曼）有限公司'},
        {value: 'AKESO (BVI), INC.', label: 'AKESO (BVI), INC.'}],
      subjectOptions: [
        {value: '在途物资', label: '在途物资'},
        {value: '原材料-原料', label: '原材料-原料'},
        {value: '原材料-辅材', label: '原材料-辅材'},
        {value: '原材料-包材', label: '原材料-包材'},
        {value: '低值耗材', label: '低值耗材'},
        {value: '生产耗材', label: '生产耗材'},
        {value: '外购临床药品', label: '外购临床药品'},
        {value: '临床物料', label: '临床物料'},
        {value: '研发物料', label: '研发物料'},
        {value: '备品备件', label: '备品备件'},
        {value: '循环使用原材料', label: '循环使用原材料'},
        {value: '在制品', label: '在制品'},
        {value: '半成品', label: '半成品'},
        {value: '产成品', label: '产成品'}
      ],
      monthlyOptions: [
        {value: '1月', label: '1月'},
        {value: '2月', label: '2月'},
        {value: '3月', label: '3月'},
        {value: '4月', label: '4月'},
        {value: '5月', label: '5月'},
        {value: '6月', label: '6月'},
        {value: '7月', label: '7月'},
        {value: '8月', label: '8月'},
        {value: '9月', label: '9月'},
        {value: '10月', label: '10月'},
        {value: '11月', label: '11月'},
        {value: '12月', label: '12月'}
      ]
    };
  },
  created() {
    this.getYear();
    this.selectAllMonthly();
    this.selectAllCompany();
    this.selectAllSubject();
    this.getCompanyMonthlyEndingInventoryChart();
    this.getCompanyEndPeriodInventoryAmountSummaryChart();
    this.getTop5CompanyEndPeriodInventoryChart();
    this.getCompanyMonthlyEndingInventoryQOQChart();
    this.getInventoryYearContrastChart();
    this.getNearTwelveMonthEndingInventoryChart();
  },
  mounted() {
  },
  methods: {
    /** 全选月份 */
    selectAllMonthly() {
      this.queryParams.monthlyType = this.monthlyOptions.map(a => a.value);
    },
    /** 清空月份 */
    clearSelectMonthly() {
      this.queryParams.monthlyType = [];
    },
    /** 全选公司主体 */
    selectAllCompany() {
      this.queryParams.companyName = this.companyOptions.map(a => a.value)
    },
    /** 清空公司主体 */
    clearSelectCompany() {
      this.queryParams.companyName = []
    },
    /** 全选科目类型 */
    selectAllSubject() {
      this.queryParams.subjectType = this.subjectOptions.map(a => a.value)
    },
    /** 清空科目类型 */
    clearSelectSubject() {
      this.queryParams.subjectType = []
    },
    /** 获取当前年份 */
    async getYear() {
      let year = new Date().getFullYear();
      for (let i = 2022; i <= year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询公司月度期末库存   图表数据 */
    async getCompanyMonthlyEndingInventoryChart() {
      this.loading = true;
      this.queryParams.params = {};
      getCompanyMonthlyEndingInventoryChart(this.queryParams).then(response => {
        this.companyMonthlyEndingInventoryList = response.rows;
        var a = this.companyMonthlyEndingInventoryList[0].mapList;
        this.getCompanyMonthlyEndingInventoryChartVoList(a);
      });
    },
    getCompanyMonthlyEndingInventoryChartVoList(a) {
      let chartDom = document.getElementById('companyMonthlyEndingInventoryChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: this.queryParams.year + '年公司月度期末库存',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
        },
        legend: {
          bottom: '0%',
          left: 'center'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            data: a,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: 'outside',
                  formatter: function (params) {
                    return echarts.format.addCommas(params.value) + '万元(' + params.percent + '%)';
                  }
                },
                labelLine: {
                  show: true,
                  smooth: 0.2,
                  length: 10,
                  length2: 20
                }
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
          }
        ]
      };
      option && myChart.setOption(option);
    },
    /** 查询公司期末库存金额汇总   图表数据 */
    async getCompanyEndPeriodInventoryAmountSummaryChart() {
      this.loading = true;
      this.queryParams.params = {};
      getCompanyEndPeriodInventoryAmountSummaryChart(this.queryParams).then(response => {
        this.companyEndPeriodInventoryAmountSummaryList = response.rows;
        var a = this.companyEndPeriodInventoryAmountSummaryList[0].mapList;
        this.getCompanyEndPeriodInventoryAmountSummaryChartVoList(a);
      });
    },
    getCompanyEndPeriodInventoryAmountSummaryChartVoList(a) {
      let chartDom = document.getElementById('companyEndPeriodInventoryAmountSummaryChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: this.queryParams.year + '公司期末库存金额汇总',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          x: 'center',
          y: 'bottom',
          /* bottom: 0,*/
        },
        series: [
          {
            name: ' ',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: a,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontWeight: 200,
                    fontsize: 16
                  },
                  formatter: function (params) {
                    return echarts.format.addCommas(params.value) + '万元(' + params.percent + '%)';
                  }
                  /*formatter: '{c}万元-{d}%',*/
                },
                labelLine: {
                  show: true,
                  smooth: 0.2,
                  length: 10,
                  length2: 20
                }
              }
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    /** 查询公司前五大期末库存   图表数据 */
    async getTop5CompanyEndPeriodInventoryChart() {
      this.loading = true;
      this.queryParams.params = {};
      getTop5CompanyEndPeriodInventoryChart(this.queryParams).then(response => {
        this.top5CompanyEndPeriodInventoryList = response.rows;
        var a = this.top5CompanyEndPeriodInventoryList[0].subjectListMap;
        var b = this.top5CompanyEndPeriodInventoryList[0].otherListMap;
        this.getTop5CompanyEndPeriodInventoryChartVoList(a, b);
      });
    },
    getTop5CompanyEndPeriodInventoryChartVoList(a, b) {
      let chartDom = document.getElementById('top5CompanyEndPeriodInventoryChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: this.queryParams.year + '年公司前五大期末库存',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          x: 'center',
          y: 'bottom',
          /* bottom: 0,*/
        },
        series: [
          {
            name: ' ',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: a,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontWeight: 200,
                    fontsize: 16
                  },
                  formatter: function (params) {
                    return echarts.format.addCommas(params.value) + '万元(' + params.percent + '%)';
                  }
                  /*formatter: '{c}万元-{d}%',*/
                },
                labelLine: {
                  show: true,
                  smooth: 0.2,
                  length: 10,
                  length2: 20
                }
              }
            }
          }
        ]
      };
      let subtextData = b.map(item => {
        for (let key in item) {
          return key + ': ' + item[key].toFixed(0) + '万元';
        }
      }).join(', ');

      // 更新option中的subtext属性
      option.title.subtext = subtextData;


      option && myChart.setOption(option);
    },
    /** 查询公司月度期末库存环比变动   图表数据 */
    async getCompanyMonthlyEndingInventoryQOQChart() {
      this.loading = true;
      this.queryParams.params = {};
      getCompanyMonthlyEndingInventoryQOQChart(this.queryParams).then(response => {
        this.companyMonthlyEndingInventoryQOQList = response.rows;
        var a = this.companyMonthlyEndingInventoryQOQList[0].listLegend;
        var b = this.companyMonthlyEndingInventoryQOQList[0].monthlyList;
        var c = this.companyMonthlyEndingInventoryQOQList[0].mapListData;
        this.getCompanyMonthlyEndingInventoryQOQChartVoList(a, b, c);
      });
    },
    getCompanyMonthlyEndingInventoryQOQChartVoList(a, b, c) {
      let chartDom = document.getElementById('companyMonthlyEndingInventoryQOQChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      const seriesLabel = {
        show: true
      };
      option = {
        title: {
          text: this.queryParams.year + '年公司月度期末库存环比变动',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach(item => {
              if (item.value != 0) {
                result += `${item.marker} ${item.seriesName}: ${item.data}<br/>`;
              }
            });
            return result;
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          inverse: true,
          data: b
        },
        yAxis: {
          type: 'value',
          name: '',
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          var x = {};
          let d = map[key];
          x.name = key;
          x.type = 'bar';
          x.stack = 'bar';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (params) {
                if (params.value != 0) {
                  return echarts.format.addCommas(params.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 库存年度对比  图表数据 */
    async getInventoryYearContrastChart() {
      this.loading = true;
      this.queryParams.params = {};
      getInventoryYearContrastChart(this.queryParams).then(response => {
        this.inventoryYearContrastList = response.rows;
        var a = this.inventoryYearContrastList[0].monthlyList;
        var b = this.inventoryYearContrastList[0].mapListData;
        this.getInventoryYearContrastChartVoList(a, b);
      });
    },
    getInventoryYearContrastChartVoList(a, b) {
      let chartDom = document.getElementById('inventoryYearContrastChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: this.queryParams.year - 1 + '年-' + this.queryParams.year + "库存年度对比",
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          /* x.barWidth = '50%';
           x.barMaxWidth = '30';*/
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    /** 康方集团近12个月期末库存   图表数据 */
    async getNearTwelveMonthEndingInventoryChart() {
      this.loading = true;
      this.queryParams.params = {};
      getNearTwelveMonthEndingInventoryChart(this.queryParams).then(response => {
        this.nearTwelveMonthEndingInventoryList = response.rows;
        var a = this.nearTwelveMonthEndingInventoryList[0].subjectTypeList;
        var b = this.nearTwelveMonthEndingInventoryList[0].yearMonthList;
        var c = this.nearTwelveMonthEndingInventoryList[0].mapListData;
        this.getNearTwelveMonthEndingInventoryChartVoList(a, b, c);
      });
    },
    getNearTwelveMonthEndingInventoryChartVoList(a, b, c) {
      let chartDom = document.getElementById('nearTwelveMonthEndingInventoryChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;
      option = {
        title: {
          text: this.queryParams.yearMonth + '康方集团近12个月期末库存',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function (params) {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach(item => {
              if (item.value != 0) {
                result += `${item.marker} ${item.seriesName}: ${item.data}<br/>`;
              }
            });
            return result;
          }
        },
        toolbox: {
          feature: {
            dataView: {show: true, readOnly: false},
            magicType: {show: true, type: ['line', 'bar']},
            restore: {show: true},
            saveAsImage: {show: true}
          }
        },
        legend: {
          data: a
        },
        xAxis: [
          {
            type: 'category',
            data: b,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '期末库存金额（万元）',
            axisLabel: {
              formatter: '{value}' + ' '
            }
          },
          {
            type: 'value',
            name: '合计金额（万元）',
            axisLabel: {
              formatter: '{value}' + ' '
            }
          }
        ],
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          if (key == '期末库存') {
            x.type = 'line';
            x.label = {
              show: true,
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          } else {
            x.type = 'bar';
            x.yAxisIndex = 1;
            x.stack = 'total';
            x.label = {
              show: true,
              position: 'inside',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            };
          }
          x.data = map[key];
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getCompanyMonthlyEndingInventoryChart();
      this.getCompanyEndPeriodInventoryAmountSummaryChart();
      this.getTop5CompanyEndPeriodInventoryChart();
      this.getCompanyMonthlyEndingInventoryQOQChart();
      this.getInventoryYearContrastChart();
      this.getNearTwelveMonthEndingInventoryChart();
    }
  }
};
</script>

