<template>
  <div>
    <!--    <div class="app-container" style="height: 15px">
          <el-row>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
              <el-form-item label="业务类型">
                <el-select v-model="queryParams.businessType" placeholder="请选择">
                  <el-option
                    v-for="item in businessTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </el-form>
          </el-row>
        </div>-->

    <div style="display: flex">
      <div style="flex: 1;margin-top: 50px">
        <div id="loanUsageChart" style="width: 100%;height: 800px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {getLoanUsageChart} from "@/api/groupFinance/fundGroup";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 融资情况 集合
      loanUsageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // year:new Date().getFullYear()
        // businessType: '项目贷款'
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      businessTypeOptions: [{label: '项目贷款', value: '项目贷款'}, {label: '流动资金贷款', value: '流动资金贷款'}]
    };
  },
  created() {
    //this.getYear();
    this.getloanUsageChart();
  },
  mounted() {
  },
  methods: {
    getYear() {
      let year = new Date().getFullYear();
      for (let i = 2022; i <= year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询融资情况 图表数据 */
    async getloanUsageChart() {
      this.loading = true;
      this.queryParams.params = {};
      getLoanUsageChart(this.queryParams).then(response => {
        this.loanUsageList = response.rows;
        let a = this.loanUsageList[0].listLegend;
        let b = this.loanUsageList[0].companyList;
        let c = this.loanUsageList[0].mapListData;
        this.getLoanUsageChartVoList(a, b, c);
      });
    },
    getLoanUsageChartVoList(a, b, c) {
      let chartDom = document.getElementById('loanUsageChart');
      let myChart = echarts.init(chartDom);
      let option;
      const seriesLabel = {
        show: true
      };
      option = {
        title: {
          text: '融资情况详情',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach(item => {
              if (item.value != 0) {
                result += `${item.marker} ${item.seriesName}: ${item.data}<br/>`;
              }
            });
            return result;
          }
        },
        legend: {
          data: a
        },
        grid: {
          left: 100
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'value',
          name: '',
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          type: 'category',
          inverse: true,
          data: b
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          var x = {};
          let d = map[key];
          x.name = key;
          x.type = 'bar';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (params) {
                if (params.value != 0) {
                  return echarts.format.addCommas(params.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
// 搜索按钮操作
    handleQuery() {
      this.getloanUsageChart();
    }
  }
}
;
</script>

