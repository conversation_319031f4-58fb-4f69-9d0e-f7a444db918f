<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择" style="margin-left: 20px;width:250px">
              <el-option v-for="item in yearData"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="公司主体" prop="companyName">
            <el-select
              v-model="queryParams.companyName"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllCompany">全选</el-button>
                <!--            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>-->
                <el-button type="info" class="dept-selector-btn" @click="clearSelectCompany">清空</el-button>
              </el-row>
              <el-option
                v-for="item in companyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="科目类型" prop="subjectType">
            <el-select
              v-model="queryParams.subjectType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllSubject">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectSubject">清空</el-button>
              </el-row>
              <el-option v-for="item in subjectOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="月份" prop="monthlyType">
            <el-select
              v-model="queryParams.monthlyType"
              multiple
              collapse-tags
              style="margin-left: 20px;width:250px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllMonthly">全选</el-button>
                <!--            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>-->
                <el-button type="info" class="dept-selector-btn" @click="clearSelectMonthly">清空</el-button>
              </el-row>
              <el-option
                v-for="item in monthlyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 80px">
        <div id="financeExpensesCompanyComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="financeExpensesSubjectContemporaneousComparisonChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="financeExpensesCompanyMonthChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="financeExpensesCompanySubjectChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 30px">
        <div id="financeExpensesSubjectMonthChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {
  getFinanceExpensesCompanyComparisonChart,
  getFinanceExpensesCompanyMonthChart,
  getFinanceExpensesCompanySubjectChart,
  getFinanceExpensesSubjectContemporaneousComparisonChart,
  getFinanceExpensesSubjectMonthChart
} from "@/api/groupFinance/accountingGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //财务费用公司月份对比 集合
      financeExpensesCompanyMonthList: [],
      //财务费用公司科目对比 集合
      financeExpensesCompanySubjectList: [],
      //财务费用科目月份对比 集合
      financeExpensesSubjectMonthList: [],
      //财务费用各公司同期对比 集合
      financeExpensesCompanyComparisonList: [],
      //财务费用各科目同期对比 集合
      financeExpensesSubjectContemporaneousComparisonList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        monthlyType: [],
        companyName: [],
        subjectType: [],
        year: new Date().getFullYear()
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        monthlyType: [{required: true, message: '请选择月份', trigger: 'change'}],
        companyName: [{required: true, message: '请选择公司主体', trigger: 'change'}],
        subjectType: [{required: true, message: '请选择科目类型', trigger: 'change'}]
      },
      companyOptions: [
        {value: '中山康方生物医药有限公司', label: '中山康方生物医药有限公司'},
        {value: '康方天成（广东）制药有限公司', label: '康方天成（广东）制药有限公司'},
        {value: '康方赛诺医药有限公司', label: '康方赛诺医药有限公司'},
        {value: '康方药业有限公司', label: '康方药业有限公司'},
        {value: '康融东方（广东）医药有限公司', label: '康融东方（广东）医药有限公司'},
        {value: '康融东方（广州）生物医药有限公司', label: '康融东方（广州）生物医药有限公司'},
        {value: '康方隆跃（广东）科技有限公司', label: '康方隆跃（广东）科技有限公司'},
        {value: '中山康方生物医药有限公司北京分公司', label: '中山康方生物医药有限公司北京分公司'},
        {value: '康方添成科技（上海）有限公司', label: '康方添成科技（上海）有限公司'},
        {value: '中山康方生物医药有限公司上海分公司', label: '中山康方生物医药有限公司上海分公司'},
        {value: '康方汇科（上海）生物有限公司', label: '康方汇科（上海）生物有限公司'},
        {value: '康方汇科（广州）生物有限公司', label: '康方汇科（广州）生物有限公司'},
        {value: '康方中国有限公司', label: '康方中国有限公司'},
        {value: '泽昇医药（广东）有限公司', label: '泽昇医药（广东）有限公司'},
        {value: '康方生物医药（美国）有限公司', label: '康方生物医药（美国）有限公司'},
        {value: '康方生物医药（澳大利亚）有限公司', label: '康方生物医药（澳大利亚）有限公司'},
        {value: '康方生物科技（开曼）有限公司', label: '康方生物科技（开曼）有限公司'},
        {value: 'AKESO (BVI), INC.', label: 'AKESO (BVI), INC.'}],
      subjectOptions: [
        {value: '财务费用-手续费', label: '财务费用-手续费'},
        {value: '财务费用-利息支出-关联方', label: '财务费用-利息支出-关联方'},
        {value: '财务费用-利息支出-非关联方', label: '财务费用-利息支出-非关联方'},
        {value: '财务费用-汇兑损失', label: '财务费用-汇兑损失'},
        {value: '财务费用-汇兑收益', label: '财务费用-汇兑收益'},
        {value: '财务费用-利息收入-关联方', label: '财务费用-利息收入-关联方'},
        {value: '财务费用-利息收入-非关联方', label: '财务费用-利息收入-非关联方'}
      ],
      monthlyOptions: [
        {value: '1月', label: '1月'},
        {value: '2月', label: '2月'},
        {value: '3月', label: '3月'},
        {value: '4月', label: '4月'},
        {value: '5月', label: '5月'},
        {value: '6月', label: '6月'},
        {value: '7月', label: '7月'},
        {value: '8月', label: '8月'},
        {value: '9月', label: '9月'},
        {value: '10月', label: '10月'},
        {value: '11月', label: '11月'},
        {value: '12月', label: '12月'}
      ]
    };
  },
  created() {
    this.getYear();
    this.selectAllMonthly();
    this.selectAllCompany();
    this.selectAllSubject();
    this.getFinanceExpensesCompanyMonthChart();
    this.getFinanceExpensesCompanySubjectChart();
    this.getFinanceExpensesSubjectMonthChart();
    this.getFinanceExpensesCompanyComparisonChart();
    this.getFinanceExpensesSubjectContemporaneousComparisonChart();
  },
  mounted() {
  },
  methods: {
    /** 全选月份 */
    selectAllMonthly() {
      this.queryParams.monthlyType = this.monthlyOptions.map(a => a.value);
    },
    /** 清空月份 */
    clearSelectMonthly() {
      this.queryParams.monthlyType = [];
    },
    /** 全选公司主体 */
    selectAllCompany() {
      this.queryParams.companyName = this.companyOptions.map(a => a.value)
    },
    /** 清空公司主体 */
    clearSelectCompany() {
      this.queryParams.companyName = []
    },
    /** 全选科目类型 */
    selectAllSubject() {
      this.queryParams.subjectType = this.subjectOptions.map(a => a.value)
    },
    /** 清空科目类型 */
    clearSelectSubject() {
      this.queryParams.subjectType = []
    },
    /** 获取当前年份 */
    async getYear() {
      let year = new Date().getFullYear();
      for (let i = 2022; i <= year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询财务费用公司月份对比    图表数据 */
    async getFinanceExpensesCompanyMonthChart() {
      this.loading = true;
      this.queryParams.params = {};
      getFinanceExpensesCompanyMonthChart(this.queryParams).then(response => {
        this.financeExpensesCompanyMonthList = response.rows;
        var a = this.financeExpensesCompanyMonthList[0].companyList;
        var b = this.financeExpensesCompanyMonthList[0].monthlyList;
        var c = this.financeExpensesCompanyMonthList[0].mapListData;
        this.getFinanceExpensesCompanyMonthChartVoList(a, b, c);
      });
    },
    getFinanceExpensesCompanyMonthChartVoList(a, b, c) {
      let chartDom = document.getElementById('financeExpensesCompanyMonthChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '财务费用公司月份对比',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
          // min: 0,
          // max: 5000,
          // interval:1000,
          // axisLabel: {
          //   formatter: '{value}'
          // }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          console.log(key + ': ' + map[key]);
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询财务费用公司科目对比   图表数据 */
    async getFinanceExpensesCompanySubjectChart() {
      this.loading = true;
      this.queryParams.params = {};
      getFinanceExpensesCompanySubjectChart(this.queryParams).then(response => {
        this.financeExpensesCompanySubjectList = response.rows;
        var a = this.financeExpensesCompanySubjectList[0].subjectList;
        var b = this.financeExpensesCompanySubjectList[0].companyList;
        var c = this.financeExpensesCompanySubjectList[0].mapListData;
        this.getFinanceExpensesCompanySubjectChartVoList(a, b, c);
      });
    },
    getFinanceExpensesCompanySubjectChartVoList(a, b, c) {
      let chartDom = document.getElementById('financeExpensesCompanySubjectChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '财务费用公司科目对比',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%'
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
          // min: 0,
          // max: 5000,
          // interval:1000,
          // axisLabel: {
          //   formatter: '{value}'
          // }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          console.log(key + ': ' + map[key]);
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询财务费用科目月份对比   图表数据 */
    async getFinanceExpensesSubjectMonthChart() {
      this.loading = true;
      this.queryParams.params = {};
      getFinanceExpensesSubjectMonthChart(this.queryParams).then(response => {
        this.financeExpensesSubjectMonthList = response.rows;
        var a = this.financeExpensesSubjectMonthList[0].subjectList;
        var b = this.financeExpensesSubjectMonthList[0].monthlyList;
        var c = this.financeExpensesSubjectMonthList[0].mapListData;
        this.getFinanceExpensesSubjectMonthChartVoList(a, b, c);
      });
    },
    getFinanceExpensesSubjectMonthChartVoList(a, b, c) {
      let chartDom = document.getElementById('financeExpensesSubjectMonthChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '财务费用科目月份对比',
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%',
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          console.log(key + ': ' + map[key]);
          let x = {};
          x.name = key;
          x.type = 'line';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询财务费用各公司同期对比   图表数据 */
    async getFinanceExpensesCompanyComparisonChart() {
      this.loading = true;
      this.queryParams.params = {};
      getFinanceExpensesCompanyComparisonChart(this.queryParams).then(response => {
        this.financeExpensesCompanyComparisonList = response.rows;
        var a = this.financeExpensesCompanyComparisonList[0].companyList;
        var b = this.financeExpensesCompanyComparisonList[0].mapListData;
        this.getFinanceExpensesCompanyComparisonChartVoList(a, b);
      });
    },
    getFinanceExpensesCompanyComparisonChartVoList(a, b) {
      let chartDom = document.getElementById('financeExpensesCompanyComparisonChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: "财务费用各公司同期对比",
          subtext: '中山康方（含北京和上海分公司），康融广东（含康融广州）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          x.barWidth = '50%';
          x.barMaxWidth = '30';
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    /** 查询财务费用各科目同期对比   图表数据 */
    async getFinanceExpensesSubjectContemporaneousComparisonChart() {
      this.loading = true;
      this.queryParams.params = {};
      getFinanceExpensesSubjectContemporaneousComparisonChart(this.queryParams).then(response => {
        this.financeExpensesSubjectContemporaneousComparisonList = response.rows;
        var a = this.financeExpensesSubjectContemporaneousComparisonList[0].subjectList;
        var b = this.financeExpensesSubjectContemporaneousComparisonList[0].mapListData;
        this.getFinanceExpensesSubjectContemporaneousComparisonChartVoList(a, b);
      });
    },
    getFinanceExpensesSubjectContemporaneousComparisonChartVoList(a, b) {
      let chartDom = document.getElementById('financeExpensesSubjectContemporaneousComparisonChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: "财务费用各科目同期对比",
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          x.barWidth = '50%';
          x.barMaxWidth = '30';
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getFinanceExpensesCompanyMonthChart();
      this.getFinanceExpensesCompanySubjectChart();
      this.getFinanceExpensesSubjectMonthChart();
      this.getFinanceExpensesCompanyComparisonChart();
      this.getFinanceExpensesSubjectContemporaneousComparisonChart();
    }
  }
};
</script>

