<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择" style="margin-left: 20px;">
              <el-option v-for="item in yearData"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="年月">
            <el-date-picker
              v-model="queryParams.yearMonth"
              type="month"
              value-format="yyyy-MM"
              placeholder="请选择年月"
              style="margin-left: 20px;">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="月份" prop="month">
            <el-select
              v-model="queryParams.month"
              multiple
              collapse-tags
              style="margin-left: 20px;"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllMonth">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectMonth">清空</el-button>
              </el-row>
              <el-option
                v-for="item in monthOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="成本分类" prop="costType">
            <el-select
              v-model="queryParams.costType"
              multiple
              collapse-tags
              style="margin-left: 20px;"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllCostType">全选</el-button>
                <el-button type="info" class="dept-selector-btn" @click="clearSelectCostType">清空</el-button>
              </el-row>
              <el-option v-for="item in costTypeOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="单管线" prop="singlePipeline">
            <el-select
              v-model="queryParams.singlePipeline"
              style="margin-left: 20px;"
              placeholder="请选择">
              <el-option v-for="item in pipelineOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="单项目号" prop="singleProjectNumber">
            <el-select
              v-model="queryParams.singleProjectNumber"
              style="margin-left: 20px;"
              placeholder="请选择">
              <el-option v-for="item in projectNumberOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="多管线" prop="multiplePipeline">
            <el-select
              v-model="queryParams.multiplePipeline"
              multiple
              collapse-tags
              style="margin-left: 20px;"
              placeholder="请选择">
              <el-option v-for="item in pipelineOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="多项目号" prop="projectNumberList">
            <el-select
              v-model="queryParams.projectNumberList"
              multiple
              collapse-tags
              style="margin-left: 20px;"
              placeholder="请选择">
              <!--              <el-row>
                              <el-button type="primary" class="dept-selector-btn" @click="selectAllCostType">全选</el-button>
                              <el-button type="info" class="dept-selector-btn" @click="clearSelectCostType">清空</el-button>
                            </el-row>-->
              <el-option v-for="item in projectNumberOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="公司主体" prop="companyName">
            <el-select
              v-model="queryParams.companyName"
              multiple
              collapse-tags
              style="margin-left: 20px; width: 350px"
              placeholder="请选择">
              <el-row>
                <el-button type="primary" class="dept-selector-btn" @click="selectAllCompany">全选</el-button>
                <!--            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>-->
                <el-button type="info" class="dept-selector-btn" @click="clearSelectCompany">清空</el-button>
              </el-row>
              <el-option
                v-for="item in companyOptions"
                :key="item.value"
                :labei="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 140px">
        <div id="researchDevelopmentExpenseYearClassificationSummaryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="costClassificationSummaryProportionChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="companyResearchDevelopmentExpenseSummaryChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="costClassificationSummaryYOYChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="singlePipelineProjectSummaryYOYChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="multiplePipelineResearchDevelopmentExpenseSummaryYOYChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;margin-top: 80px">
        <div id="singleProjectYearCostChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {
  getResearchDevelopmentExpenseYearClassificationSummaryChart,
  getCostClassificationSummaryProportionChart,
  getCompanyResearchDevelopmentExpenseSummaryChart,
  getCostClassificationSummaryYOYChart,
  getSinglePipelineProjectSummaryYOYChart,
  getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart,
  getSingleProjectYearCostChart
} from "@/api/groupFinance/researchDevelopmentGroup";
import {getFinancialBriefingChart} from "@/api/groupFinance/accountingGroupChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //各年度研发费用分类汇总 集合
      researchDevelopmentExpenseYearClassificationSummaryList: [],
      //成本分类汇总占比 集合
      costClassificationSummaryProportionList: [],
      //各公司研发费用汇总 集合
      companyResearchDevelopmentExpenseSummaryList: [],
      //成本分类汇总同比 集合
      costClassificationSummaryYOYList: [],
      //单管线、项目研发费用汇总同比 集合
      singlePipelineProjectSummaryYOYList: [],
      //多管线、研发费用汇总同比 集合
      multiplePipelineResearchDevelopmentExpenseSummaryYOYList: [],
      //单项目各年度成本类别 集合
      singleProjectYearCostList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        companyName: [],
        costType: [],
        year: new Date().getFullYear(),
        yearMonth: this.$moment(new Date()).format("YYYY-MM"),
        singlePipeline: 'AK101',
        multiplePipeline: ['AK101', 'AK102', 'AK104', 'AK105', 'AK109', 'AK111', 'AK112', 'AK117', 'AK120', 'AK127'],
        singleProjectNumber: "AK101",
        projectNumberList: ['AK101', 'AK101-101', 'AK101-102', 'AK101-103', 'AK101-201', 'AK101-301', 'AK101-302', 'AK101-303'],
        month: []
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        companyName: [{required: true, message: '请选择公司主体', trigger: 'change'}],
        costType: [{required: true, message: '请选择成本分类', trigger: 'change'}],
        singlePipeline: [{required: true, message: '请选择单管线', trigger: 'change'}],
        multiplePipeline: [{required: true, message: '请选择多管线', trigger: 'change'}],
        projectNumberList: [{required: true, message: '请选择项目号', trigger: 'change'}],
        singleProjectNumber: [{required: true, message: '请选择单项目号', trigger: 'change'}],
        month: [{required: true, message: '请选择月份', trigger: 'change'}]
      },
      companyOptions: [
        {value: '中山康方生物医药有限公司', label: '中山康方生物医药有限公司'},
        {value: '中山康方生物医药有限公司北京分公司', label: '中山康方生物医药有限公司北京分公司'},
        {value: '中山康方生物医药有限公司上海分公司', label: '中山康方生物医药有限公司上海分公司'},
        {value: '康方天成（广东）制药有限公司', label: '康方天成（广东）制药有限公司'},
        {value: '康方赛诺医药有限公司', label: '康方赛诺医药有限公司'},
        {value: '康方药业有限公司', label: '康方药业有限公司'},
        {value: '康融东方（广东）医药有限公司', label: '康融东方（广东）医药有限公司'},
        {value: '康融东方（广州）生物医药有限公司', label: '康融东方（广州）生物医药有限公司'},
        {value: '正大天晴康方（上海）生物医药科技有限公司', label: '正大天晴康方（上海）生物医药科技有限公司'},
        {value: '康方汇科（上海）生物有限公司', label: '康方汇科（上海）生物有限公司'},
        {value: '康方汇科（广州）生物有限公司', label: '康方汇科（广州）生物有限公司'},
        {value: '康方隆跃（广东）科技有限公司', label: '康方隆跃（广东）科技有限公司'},
        {value: '泽昇医药 (广东)有限公司', label: '泽昇医药 (广东)有限公司'},
        {value: '康方中国有限公司', label: '康方中国有限公司'},
        {value: '康方生物医药（美国）有限公司', label: '康方生物医药（美国）有限公司'},
        {value: '康方生物医药（澳大利亚）有限公司', label: '康方生物医药（澳大利亚）有限公司'},
        {value: '康方生物科技（开曼）有限公司', label: '康方生物科技（开曼）有限公司'},
        {value: 'AKESO (BVI), INC.', label: 'AKESO (BVI), INC.'}],
      costTypeOptions: [
        {value: '材料成本', label: '材料成本'},
        {value: '人工成本', label: '人工成本'},
        {value: '运行成本', label: '运行成本'},
        {value: '折旧及摊销', label: '折旧及摊销'},
        {value: '直接成本', label: '直接成本'}],
      pipelineOptions: [
        {value: 'AK101', label: 'AK101'},
        {value: 'AK102', label: 'AK102'},
        {value: 'AK103', label: 'AK103'},
        {value: 'AK104', label: 'AK104'},
        {value: 'AK104-IIS', label: 'AK104-IIS'},
        {value: 'AK104-IIT', label: 'AK104-IIT'},
        {value: 'AK104-IIT-C', label: 'AK104-IIT-C'},
        {value: 'AK104-RC48', label: 'AK104-RC48'},
        {value: 'AK104ZY', label: 'AK104ZY'},
        {value: 'AK105', label: 'AK105'},
        {value: 'AK106', label: 'AK106'},
        {value: 'AK107', label: 'AK107'},
        {value: 'AK109', label: 'AK109'},
        {value: 'AK111', label: 'AK111'},
        {value: 'AK112', label: 'AK112'},
        {value: 'AK112-3003', label: 'AK112-3003'},
        {value: 'SM112-301', label: 'SM112-301'},
        {value: 'AK112-IIT', label: 'AK112-IIT'},
        {value: 'AK112-IIT-C', label: 'AK112-IIT-C'},
        {value: 'AK112ZY', label: 'AK112ZY'},
        {value: 'AK114', label: 'AK114'},
        {value: 'AK115', label: 'AK115'},
        {value: 'KF项目', label: 'KF项目'},
        {value: 'AK117', label: 'AK117'},
        {value: 'AK117ZY', label: 'AK117ZY'},
        {value: 'AK119', label: 'AK119'},
        {value: 'AK120', label: 'AK120'},
        {value: 'AK127', label: 'AK127'},
        {value: 'AK127ZY', label: 'AK127ZY'},
        {value: 'AK129', label: 'AK129'},
        {value: 'AK130', label: 'AK130'},
        {value: 'AK131', label: 'AK131'},
        {value: 'AK132', label: 'AK132'},
        {value: 'AK140N-IIT', label: 'AK140N-IIT'},
        {value: 'CT', label: 'CT'},
        {value: 'SPH4336-201', label: 'SPH4336-201'},
      ],
      projectNumberOptions: [
        {value: 'AK101', label: 'AK101'},
        {value: 'AK101-101', label: 'AK101-101'},
        {value: 'AK101-102', label: 'AK101-102'},
        {value: 'AK101-103', label: 'AK101-103'},
        {value: 'AK101-201', label: 'AK101-201'},
        {value: 'AK101-301', label: 'AK101-301'},
        {value: 'AK101-302', label: 'AK101-302'},
        {value: 'AK101-303', label: 'AK101-303'},
        {value: 'KF003', label: 'KF003'},
        {value: 'AK102', label: 'AK102'},
        {value: 'AK102-101', label: 'AK102-101'},
        {value: 'AK102-201', label: 'AK102-201'},
        {value: 'AK102-202', label: 'AK102-202'},
        {value: 'AK102-203', label: 'AK102-203'},
        {value: 'AK102-204', label: 'AK102-204'},
        {value: 'AK102-301', label: 'AK102-301'},
        {value: 'AK102-302', label: 'AK102-302'},
        {value: 'AK102-303', label: 'AK102-303'},
        {value: 'KF011', label: 'KF011'},
        {value: 'AK103', label: 'AK103'},
        {value: 'KF007', label: 'KF007'},
        {value: 'AK104', label: 'AK104'},
        {value: 'AK104-101', label: 'AK104-101'},
        {value: 'AK104-102', label: 'AK104-102'},
        {value: 'AK104-201', label: 'AK104-201'},
        {value: 'AK104-201-AU', label: 'AK104-201-AU'},
        {value: 'AK104-201A', label: 'AK104-201A'},
        {value: 'AK104-201B', label: 'AK104-201B'},
        {value: 'AK104-202', label: 'AK104-202'},
        {value: 'AK104-203', label: 'AK104-203'},
        {value: 'AK104-204', label: 'AK104-204'},
        {value: 'AK104-205', label: 'AK104-205'},
        {value: 'AK104-206', label: 'AK104-206'},
        {value: 'AK104-207', label: 'AK104-207'},
        {value: 'AK104-208', label: 'AK104-208'},
        {value: 'AK104-209', label: 'AK104-209'},
        {value: 'AK104-210', label: 'AK104-210'},
        {value: 'AK104-211', label: 'AK104-211'},
        {value: 'AK104-212', label: 'AK104-212'},
        {value: 'AK104-213', label: 'AK104-213'},
        {value: 'AK104-214', label: 'AK104-214'},
        {value: 'AK104-215', label: 'AK104-215'},
        {value: 'AK104-216', label: 'AK104-216'},
        {value: 'AK104-217', label: 'AK104-217'},
        {value: 'AK104-218', label: 'AK104-218'},
        {value: 'AK104-219', label: 'AK104-219'},
        {value: 'AK104-220', label: 'AK104-220'},
        {value: 'AK104-221', label: 'AK104-221'},
        {value: 'AK104-222', label: 'AK104-222'},
        {value: 'AK104-301', label: 'AK104-301'},
        {value: 'AK104-302', label: 'AK104-302'},
        {value: 'AK104-303', label: 'AK104-303'},
        {value: 'AK104-304', label: 'AK104-304'},
        {value: 'AK104-305', label: 'AK104-305'},
        {value: 'AK104-306', label: 'AK104-306'},
        {value: 'AK104-307', label: 'AK104-307'},
        {value: 'AK104-308', label: 'AK104-308'},
        {value: 'AK104-309', label: 'AK104-309'},
        {value: 'AK104-SAS', label: 'AK104-SAS'},
        {value: 'KF030', label: 'KF030'},
        {value: 'AK104-IIS-001(澳洲)', label: 'AK104-IIS-001(澳洲)'},
        {value: 'AK104-IIS-002(澳洲)', label: 'AK104-IIS-002(澳洲)'},
        {value: 'AK104-IIT-001', label: 'AK104-IIT-001'},
        {value: 'AK104-IIT-002', label: 'AK104-IIT-002'},
        {value: 'AK104-IIT-003', label: 'AK104-IIT-003'},
        {value: 'AK104-IIT-004', label: 'AK104-IIT-004'},
        {value: 'AK104-IIT-005', label: 'AK104-IIT-005'},
        {value: 'AK104-IIT-006', label: 'AK104-IIT-006'},
        {value: 'AK104-IIT-007', label: 'AK104-IIT-007'},
        {value: 'AK104-IIT-008', label: 'AK104-IIT-008'},
        {value: 'AK104-IIT-009', label: 'AK104-IIT-009'},
        {value: 'AK104-IIT-010', label: 'AK104-IIT-010'},
        {value: 'AK104-IIT-011', label: 'AK104-IIT-011'},
        {value: 'AK104-IIT-012', label: 'AK104-IIT-012'},
        {value: 'AK104-IIT-013', label: 'AK104-IIT-013'},
        {value: 'AK104-IIT-014', label: 'AK104-IIT-014'},
        {value: 'AK104-IIT-015', label: 'AK104-IIT-015'},
        {value: 'AK104-IIT-016', label: 'AK104-IIT-016'},
        {value: 'AK104-IIT-017', label: 'AK104-IIT-017'},
        {value: 'AK104-IIT-018', label: 'AK104-IIT-018'},
        {value: 'AK104-IIT-019', label: 'AK104-IIT-019'},
        {value: 'AK104-IIT-020', label: 'AK104-IIT-020'},
        {value: 'AK104-IIT-021', label: 'AK104-IIT-021'},
        {value: 'AK104-IIT-024', label: 'AK104-IIT-024'},
        {value: 'AK104-IIT-025', label: 'AK104-IIT-025'},
        {value: 'AK104-IIT-026', label: 'AK104-IIT-026'},
        {value: 'AK104-IIT-027', label: 'AK104-IIT-027'},
        {value: 'AK104-IIT-028', label: 'AK104-IIT-028'},
        {value: 'AK104-IIT-029', label: 'AK104-IIT-029'},
        {value: 'AK104-IIT-030', label: 'AK104-IIT-030'},
        {value: 'AK104-IIT-031', label: 'AK104-IIT-031'},
        {value: 'AK104-IIT-032', label: 'AK104-IIT-032'},
        {value: 'AK104-IIT-033', label: 'AK104-IIT-033'},
        {value: 'AK104-IIT-034', label: 'AK104-IIT-034'},
        {value: 'AK104-IIT-035', label: 'AK104-IIT-035'},
        {value: 'AK104-IIT-036', label: 'AK104-IIT-036'},
        {value: 'AK104-IIT-037', label: 'AK104-IIT-037'},
        {value: 'AK104-IIT-038', label: 'AK104-IIT-038'},
        {value: 'AK104-IIT-039', label: 'AK104-IIT-039'},
        {value: 'AK104-IIT-040', label: 'AK104-IIT-040'},
        {value: 'AK104-IIT-041', label: 'AK104-IIT-041'},
        {value: 'AK104-IIT-042', label: 'AK104-IIT-042'},
        {value: 'AK104-IIT-043', label: 'AK104-IIT-043'},
        {value: 'AK104-IIT-C-E-0001', label: 'AK104-IIT-C-E-0001'},
        {value: 'AK104-IIT-C-E-0002', label: 'AK104-IIT-C-E-0002'},
        {value: 'AK104-IIT-C-E-0018', label: 'AK104-IIT-C-E-0018'},
        {value: 'AK104-IIT-C-E-0021', label: 'AK104-IIT-C-E-0021'},
        {value: 'AK104-IIT-C-E-0022', label: 'AK104-IIT-C-E-0022'},
        {value: 'AK104-IIT-C-E-0023', label: 'AK104-IIT-C-E-0023'},
        {value: 'AK104-IIT-C-E-0024', label: 'AK104-IIT-C-E-0024'},
        {value: 'AK104-IIT-C-E-0026', label: 'AK104-IIT-C-E-0026'},
        {value: 'AK104-IIT-C-E-0027', label: 'AK104-IIT-C-E-0027'},
        {value: 'AK104-IIT-C-E-0028', label: 'AK104-IIT-C-E-0028'},
        {value: 'AK104-IIT-C-E-0029', label: 'AK104-IIT-C-E-0029'},
        {value: 'AK104-IIT-C-E-0031', label: 'AK104-IIT-C-E-0031'},
        {value: 'AK104-IIT-C-E-0032', label: 'AK104-IIT-C-E-0032'},
        {value: 'AK104-IIT-C-E-0033', label: 'AK104-IIT-C-E-0033'},
        {value: 'AK104-IIT-C-E-0034', label: 'AK104-IIT-C-E-0034'},
        {value: 'AK104-IIT-C-E-0036', label: 'AK104-IIT-C-E-0036'},
        {value: 'AK104-IIT-C-E-0037', label: 'AK104-IIT-C-E-0037'},
        {value: 'AK104-IIT-C-E-0038', label: 'AK104-IIT-C-E-0038'},
        {value: 'AK104-IIT-C-E-0039', label: 'AK104-IIT-C-E-0039'},
        {value: 'AK104-IIT-C-E-0042', label: 'AK104-IIT-C-E-0042'},
        {value: 'AK104-IIT-C-E-0043', label: 'AK104-IIT-C-E-0043'},
        {value: 'AK104-IIT-C-E-0044', label: 'AK104-IIT-C-E-0044'},
        {value: 'AK104-IIT-C-E-0045', label: 'AK104-IIT-C-E-0045'},
        {value: 'AK104-IIT-C-E-0046', label: 'AK104-IIT-C-E-0046'},
        {value: 'AK104-IIT-C-E-0047', label: 'AK104-IIT-C-E-0047'},
        {value: 'AK104-IIT-C-E-0048', label: 'AK104-IIT-C-E-0048'},
        {value: 'AK104-IIT-C-E-0049', label: 'AK104-IIT-C-E-0049'},
        {value: 'AK104-IIT-C-E-0050', label: 'AK104-IIT-C-E-0050'},
        {value: 'AK104-IIT-C-E-0051', label: 'AK104-IIT-C-E-0051'},
        {value: 'AK104-IIT-C-E-0052', label: 'AK104-IIT-C-E-0052'},
        {value: 'AK104-IIT-C-E-0053', label: 'AK104-IIT-C-E-0053'},
        {value: 'AK104-IIT-C-E-0104', label: 'AK104-IIT-C-E-0104'},
        {value: 'AK104-IIT-C-E-0105', label: 'AK104-IIT-C-E-0105'},
        {value: 'AK104-IIT-C-E-0106', label: 'AK104-IIT-C-E-0106'},
        {value: 'AK104-IIT-C-E-0107', label: 'AK104-IIT-C-E-0107'},
        {value: 'AK104-IIT-C-E-0108', label: 'AK104-IIT-C-E-0108'},
        {value: 'AK104-IIT-C-E-0111', label: 'AK104-IIT-C-E-0111'},
        {value: 'AK104-IIT-C-E-0112', label: 'AK104-IIT-C-E-0112'},
        {value: 'AK104-IIT-C-E-0113', label: 'AK104-IIT-C-E-0113'},
        {value: 'AK104-IIT-C-E-0114', label: 'AK104-IIT-C-E-0114'},
        {value: 'AK104-IIT-C-E-0115', label: 'AK104-IIT-C-E-0115'},
        {value: 'AK104-IIT-C-E-0116', label: 'AK104-IIT-C-E-0116'},
        {value: 'AK104-IIT-C-E-0117', label: 'AK104-IIT-C-E-0117'},
        {value: 'AK104-IIT-C-E-0118', label: 'AK104-IIT-C-E-0118'},
        {value: 'AK104-IIT-C-E-0119', label: 'AK104-IIT-C-E-0119'},
        {value: 'AK104-IIT-C-E-0120', label: 'AK104-IIT-C-E-0120'},
        {value: 'AK104-IIT-C-E-0121', label: 'AK104-IIT-C-E-0121'},
        {value: 'AK104-IIT-C-E-0122', label: 'AK104-IIT-C-E-0122'},
        {value: 'AK104-IIT-C-E-0123', label: 'AK104-IIT-C-E-0123'},
        {value: 'AK104-IIT-C-E-0124', label: 'AK104-IIT-C-E-0124'},
        {value: 'AK104-IIT-C-E-0125', label: 'AK104-IIT-C-E-0125'},
        {value: 'AK104-IIT-C-E-0126', label: 'AK104-IIT-C-E-0126'},
        {value: 'AK104-IIT-C-E-0128', label: 'AK104-IIT-C-E-0128'},
        {value: 'AK104-IIT-C-E-0129', label: 'AK104-IIT-C-E-0129'},
        {value: 'AK104-IIT-C-E-0130', label: 'AK104-IIT-C-E-0130'},
        {value: 'AK104-IIT-C-E-0131', label: 'AK104-IIT-C-E-0131'},
        {value: 'AK104-IIT-C-E-0132', label: 'AK104-IIT-C-E-0132'},
        {value: 'AK104-IIT-C-E-0133', label: 'AK104-IIT-C-E-0133'},
        {value: 'AK104-IIT-C-E-0135', label: 'AK104-IIT-C-E-0135'},
        {value: 'AK104-IIT-C-E-0136', label: 'AK104-IIT-C-E-0136'},
        {value: 'AK104-IIT-C-E-0137', label: 'AK104-IIT-C-E-0137'},
        {value: 'AK104-IIT-C-E-0139', label: 'AK104-IIT-C-E-0139'},
        {value: 'AK104-IIT-C-E-0140', label: 'AK104-IIT-C-E-0140'},
        {value: 'AK104-IIT-C-E-0141', label: 'AK104-IIT-C-E-0141'},
        {value: 'AK104-IIT-C-E-0142', label: 'AK104-IIT-C-E-0142'},
        {value: 'AK104-IIT-C-E-0143', label: 'AK104-IIT-C-E-0143'},
        {value: 'AK104-IIT-C-E-0144', label: 'AK104-IIT-C-E-0144'},
        {value: 'AK104-IIT-C-E-0145', label: 'AK104-IIT-C-E-0145'},
        {value: 'AK104-IIT-C-E-0147', label: 'AK104-IIT-C-E-0147'},
        {value: 'AK104-IIT-C-E-0148', label: 'AK104-IIT-C-E-0148'},
        {value: 'AK104-IIT-C-E-0151', label: 'AK104-IIT-C-E-0151'},
        {value: 'AK104-IIT-C-E-0152', label: 'AK104-IIT-C-E-0152'},
        {value: 'AK104-IIT-C-E-0153', label: 'AK104-IIT-C-E-0153'},
        {value: 'AK104-IIT-C-E-0156', label: 'AK104-IIT-C-E-0156'},
        {value: 'AK104-IIT-C-E-0157', label: 'AK104-IIT-C-E-0157'},
        {value: 'AK104-IIT-C-E-0160', label: 'AK104-IIT-C-E-0160'},
        {value: 'AK104-IIT-C-E-0161', label: 'AK104-IIT-C-E-0161'},
        {value: 'AK104-IIT-C-E-0162', label: 'AK104-IIT-C-E-0162'},
        {value: 'AK104-IIT-C-E-0164', label: 'AK104-IIT-C-E-0164'},
        {value: 'AK104-IIT-C-E-0165', label: 'AK104-IIT-C-E-0165'},
        {value: 'AK104-IIT-C-E-0166', label: 'AK104-IIT-C-E-0166'},
        {value: 'AK104-IIT-C-E-0167', label: 'AK104-IIT-C-E-0167'},
        {value: 'AK104-IIT-C-E-0168', label: 'AK104-IIT-C-E-0168'},
        {value: 'AK104-IIT-C-E-0170', label: 'AK104-IIT-C-E-0170'},
        {value: 'AK104-IIT-C-M-0018', label: 'AK104-IIT-C-M-0018'},
        {value: 'AK104-IIT-C-M-0019', label: 'AK104-IIT-C-M-0019'},
        {value: 'AK104-IIT-C-M-0020', label: 'AK104-IIT-C-M-0020'},
        {value: 'AK104-IIT-C-M-0021', label: 'AK104-IIT-C-M-0021'},
        {value: 'AK104-IIT-C-M-0022', label: 'AK104-IIT-C-M-0022'},
        {value: 'AK104-IIT-C-M-0023', label: 'AK104-IIT-C-M-0023'},
        {value: 'AK104-IIT-C-M-0024', label: 'AK104-IIT-C-M-0024'},
        {value: 'AK104-IIT-C-M-0027', label: 'AK104-IIT-C-M-0027'},
        {value: 'AK104-IIT-C-M-0029', label: 'AK104-IIT-C-M-0029'},
        {value: 'AK104-IIT-C-M-0030', label: 'AK104-IIT-C-M-0030'},
        {value: 'AK104-IIT-C-M-0031', label: 'AK104-IIT-C-M-0031'},
        {value: 'AK104-IIT-C-M-0032', label: 'AK104-IIT-C-M-0032'},
        {value: 'AK104-IIT-C-M-0035', label: 'AK104-IIT-C-M-0035'},
        {value: 'AK104-IIT-C-M-0037', label: 'AK104-IIT-C-M-0037'},
        {value: 'AK104-IIT-C-M-0038', label: 'AK104-IIT-C-M-0038'},
        {value: 'AK104-IIT-C-M-0039', label: 'AK104-IIT-C-M-0039'},
        {value: 'AK104-IIT-C-M-0040', label: 'AK104-IIT-C-M-0040'},
        {value: 'AK104-IIT-C-M-0041', label: 'AK104-IIT-C-M-0041'},
        {value: 'AK104-IIT-C-M-0042', label: 'AK104-IIT-C-M-0042'},
        {value: 'AK104-IIT-C-M-0043', label: 'AK104-IIT-C-M-0043'},
        {value: 'AK104-IIT-C-M-0044', label: 'AK104-IIT-C-M-0044'},
        {value: 'AK104-IIT-C-M-0045', label: 'AK104-IIT-C-M-0045'},
        {value: 'AK104-IIT-C-M-0047', label: 'AK104-IIT-C-M-0047'},
        {value: 'AK104-IIT-C-M-0104', label: 'AK104-IIT-C-M-0104'},
        {value: 'AK104-IIT-C-M-0105', label: 'AK104-IIT-C-M-0105'},
        {value: 'AK104-IIT-C-M-0106', label: 'AK104-IIT-C-M-0106'},
        {value: 'AK104-IIT-C-M-0109', label: 'AK104-IIT-C-M-0109'},
        {value: 'AK104-IIT-C-M-0110', label: 'AK104-IIT-C-M-0110'},
        {value: 'AK104-IIT-C-M-0111', label: 'AK104-IIT-C-M-0111'},
        {value: 'AK104-IIT-C-M-0112', label: 'AK104-IIT-C-M-0112'},
        {value: 'AK104-IIT-C-M-0113', label: 'AK104-IIT-C-M-0113'},
        {value: 'AK104-IIT-C-M-0114', label: 'AK104-IIT-C-M-0114'},
        {value: 'AK104-IIT-C-M-0115', label: 'AK104-IIT-C-M-0115'},
        {value: 'AK104-IIT-C-M-0116', label: 'AK104-IIT-C-M-0116'},
        {value: 'AK104-IIT-C-M-0118', label: 'AK104-IIT-C-M-0118'},
        {value: 'AK104-IIT-C-M-0119', label: 'AK104-IIT-C-M-0119'},
        {value: 'AK104-IIT-C-N1-0001', label: 'AK104-IIT-C-N1-0001'},
        {value: 'AK104-IIT-C-N1-0002', label: 'AK104-IIT-C-N1-0002'},
        {value: 'AK104-IIT-C-N1-0004', label: 'AK104-IIT-C-N1-0004'},
        {value: 'AK104-IIT-C-N1-0005', label: 'AK104-IIT-C-N1-0005'},
        {value: 'AK104-IIT-C-N1-0006', label: 'AK104-IIT-C-N1-0006'},
        {value: 'AK104-IIT-C-N1-0007', label: 'AK104-IIT-C-N1-0007'},
        {value: 'AK104-IIT-C-N1-0009', label: 'AK104-IIT-C-N1-0009'},
        {value: 'AK104-IIT-C-N1-0018', label: 'AK104-IIT-C-N1-0018'},
        {value: 'AK104-IIT-C-N1-0019', label: 'AK104-IIT-C-N1-0019'},
        {value: 'AK104-IIT-C-N1-0022', label: 'AK104-IIT-C-N1-0022'},
        {value: 'AK104-IIT-C-N1-0023', label: 'AK104-IIT-C-N1-0023'},
        {value: 'AK104-IIT-C-N1-0024', label: 'AK104-IIT-C-N1-0024'},
        {value: 'AK104-IIT-C-N1-0029', label: 'AK104-IIT-C-N1-0029'},
        {value: 'AK104-IIT-C-N1-0030', label: 'AK104-IIT-C-N1-0030'},
        {value: 'AK104-IIT-C-N1-0031', label: 'AK104-IIT-C-N1-0031'},
        {value: 'AK104-IIT-C-N1-0032', label: 'AK104-IIT-C-N1-0032'},
        {value: 'AK104-IIT-C-N1-0033', label: 'AK104-IIT-C-N1-0033'},
        {value: 'AK104-IIT-C-N1-0034', label: 'AK104-IIT-C-N1-0034'},
        {value: 'AK104-IIT-C-N1-0036', label: 'AK104-IIT-C-N1-0036'},
        {value: 'AK104-IIT-C-N1-0037', label: 'AK104-IIT-C-N1-0037'},
        {value: 'AK104-IIT-C-N1-0038', label: 'AK104-IIT-C-N1-0038'},
        {value: 'AK104-IIT-C-N1-0039', label: 'AK104-IIT-C-N1-0039'},
        {value: 'AK104-IIT-C-N1-0042', label: 'AK104-IIT-C-N1-0042'},
        {value: 'AK104-IIT-C-N1-0043', label: 'AK104-IIT-C-N1-0043'},
        {value: 'AK104-IIT-C-N1-0045', label: 'AK104-IIT-C-N1-0045'},
        {value: 'AK104-IIT-C-N1-0046', label: 'AK104-IIT-C-N1-0046'},
        {value: 'AK104-IIT-C-N1-0047', label: 'AK104-IIT-C-N1-0047'},
        {value: 'AK104-IIT-C-N1-0048', label: 'AK104-IIT-C-N1-0048'},
        {value: 'AK104-IIT-C-N1-0051', label: 'AK104-IIT-C-N1-0051'},
        {value: 'AK104-IIT-C-N1-0052', label: 'AK104-IIT-C-N1-0052'},
        {value: 'AK104-IIT-C-N1-0053', label: 'AK104-IIT-C-N1-0053'},
        {value: 'AK104-IIT-C-N1-0054', label: 'AK104-IIT-C-N1-0054'},
        {value: 'AK104-IIT-C-N1-0055', label: 'AK104-IIT-C-N1-0055'},
        {value: 'AK104-IIT-C-N1-0057', label: 'AK104-IIT-C-N1-0057'},
        {value: 'AK104-IIT-C-N1-0058', label: 'AK104-IIT-C-N1-0058'},
        {value: 'AK104-IIT-C-N1-0064', label: 'AK104-IIT-C-N1-0064'},
        {value: 'AK104-IIT-C-N1-0065', label: 'AK104-IIT-C-N1-0065'},
        {value: 'AK104-IIT-C-N1-0068', label: 'AK104-IIT-C-N1-0068'},
        {value: 'AK104-IIT-C-N1-0069', label: 'AK104-IIT-C-N1-0069'},
        {value: 'AK104-IIT-C-N1-0104', label: 'AK104-IIT-C-N1-0104'},
        {value: 'AK104-IIT-C-N1-0105', label: 'AK104-IIT-C-N1-0105'},
        {value: 'AK104-IIT-C-N1-0106', label: 'AK104-IIT-C-N1-0106'},
        {value: 'AK104-IIT-C-N1-0108', label: 'AK104-IIT-C-N1-0108'},
        {value: 'AK104-IIT-C-N1-0109', label: 'AK104-IIT-C-N1-0109'},
        {value: 'AK104-IIT-C-N1-0110', label: 'AK104-IIT-C-N1-0110'},
        {value: 'AK104-IIT-C-N1-0111', label: 'AK104-IIT-C-N1-0111'},
        {value: 'AK104-IIT-C-N1-0112', label: 'AK104-IIT-C-N1-0112'},
        {value: 'AK104-IIT-C-N1-0113', label: 'AK104-IIT-C-N1-0113'},
        {value: 'AK104-IIT-C-N1-0115', label: 'AK104-IIT-C-N1-0115'},
        {value: 'AK104-IIT-C-N1-0116', label: 'AK104-IIT-C-N1-0116'},
        {value: 'AK104-IIT-C-N1-0117', label: 'AK104-IIT-C-N1-0117'},
        {value: 'AK104-IIT-C-N1-0118', label: 'AK104-IIT-C-N1-0118'},
        {value: 'AK104-IIT-C-N2-0002', label: 'AK104-IIT-C-N2-0002'},
        {value: 'AK104-IIT-C-N2-0018', label: 'AK104-IIT-C-N2-0018'},
        {value: 'AK104-IIT-C-N2-0019', label: 'AK104-IIT-C-N2-0019'},
        {value: 'AK104-IIT-C-N2-0020', label: 'AK104-IIT-C-N2-0020'},
        {value: 'AK104-IIT-C-N2-0023', label: 'AK104-IIT-C-N2-0023'},
        {value: 'AK104-IIT-C-N2-0024', label: 'AK104-IIT-C-N2-0024'},
        {value: 'AK104-IIT-C-N2-0025', label: 'AK104-IIT-C-N2-0025'},
        {value: 'AK104-IIT-C-N2-0026', label: 'AK104-IIT-C-N2-0026'},
        {value: 'AK104-IIT-C-N2-0028', label: 'AK104-IIT-C-N2-0028'},
        {value: 'AK104-IIT-C-N2-0029', label: 'AK104-IIT-C-N2-0029'},
        {value: 'AK104-IIT-C-N2-0030', label: 'AK104-IIT-C-N2-0030'},
        {value: 'AK104-IIT-C-N2-0035', label: 'AK104-IIT-C-N2-0035'},
        {value: 'AK104-IIT-C-N2-0037', label: 'AK104-IIT-C-N2-0037'},
        {value: 'AK104-IIT-C-N2-0038', label: 'AK104-IIT-C-N2-0038'},
        {value: 'AK104-IIT-C-N2-0039', label: 'AK104-IIT-C-N2-0039'},
        {value: 'AK104-IIT-C-N2-0040', label: 'AK104-IIT-C-N2-0040'},
        {value: 'AK104-IIT-C-N2-0044', label: 'AK104-IIT-C-N2-0044'},
        {value: 'AK104-IIT-C-N2-0045', label: 'AK104-IIT-C-N2-0045'},
        {value: 'AK104-IIT-C-N2-0046', label: 'AK104-IIT-C-N2-0046'},
        {value: 'AK104-IIT-C-N2-0047', label: 'AK104-IIT-C-N2-0047'},
        {value: 'AK104-IIT-C-N2-0049', label: 'AK104-IIT-C-N2-0049'},
        {value: 'AK104-IIT-C-N2-0104', label: 'AK104-IIT-C-N2-0104'},
        {value: 'AK104-IIT-C-N2-0105', label: 'AK104-IIT-C-N2-0105'},
        {value: 'AK104-IIT-C-N2-0106', label: 'AK104-IIT-C-N2-0106'},
        {value: 'AK104-IIT-C-N2-0107', label: 'AK104-IIT-C-N2-0107'},
        {value: 'AK104-IIT-C-N2-0108', label: 'AK104-IIT-C-N2-0108'},
        {value: 'AK104-IIT-C-N2-0110', label: 'AK104-IIT-C-N2-0110'},
        {value: 'AK104-IIT-C-N2-0111', label: 'AK104-IIT-C-N2-0111'},
        {value: 'AK104-IIT-C-N2-0112', label: 'AK104-IIT-C-N2-0112'},
        {value: 'AK104-IIT-C-N2-0113', label: 'AK104-IIT-C-N2-0113'},
        {value: 'AK104-IIT-C-N2-0114', label: 'AK104-IIT-C-N2-0114'},
        {value: 'AK104-IIT-C-N2-0115', label: 'AK104-IIT-C-N2-0115'},
        {value: 'AK104-IIT-C-N2-0119', label: 'AK104-IIT-C-N2-0119'},
        {value: 'AK104-IIT-C-N2-0120', label: 'AK104-IIT-C-N2-0120'},
        {value: 'AK104-IIT-C-N2-0121', label: 'AK104-IIT-C-N2-0121'},
        {value: 'AK104-IIT-C-N2-0122', label: 'AK104-IIT-C-N2-0122'},
        {value: 'AK104-IIT-C-N2-0123', label: 'AK104-IIT-C-N2-0123'},
        {value: 'AK104-IIT-C-N2-0124', label: 'AK104-IIT-C-N2-0124'},
        {value: 'AK104-IIT-C-N2-0125', label: 'AK104-IIT-C-N2-0125'},
        {value: 'AK104-IIT-C-N2-0126', label: 'AK104-IIT-C-N2-0126'},
        {value: 'AK104-IIT-C-N2-0127', label: 'AK104-IIT-C-N2-0127'},
        {value: 'AK104-IIT-C-N2-0129', label: 'AK104-IIT-C-N2-0129'},
        {value: 'AK104-IIT-C-N2-0130', label: 'AK104-IIT-C-N2-0130'},
        {value: 'AK104-IIT-C-N2-0131', label: 'AK104-IIT-C-N2-0131'},
        {value: 'AK104-IIT-C-N2-0133', label: 'AK104-IIT-C-N2-0133'},
        {value: 'AK104-IIT-C-N2-0134', label: 'AK104-IIT-C-N2-0134'},
        {value: 'AK104-IIT-C-N2-0137', label: 'AK104-IIT-C-N2-0137'},
        {value: 'AK104-IIT-C-N2-0138', label: 'AK104-IIT-C-N2-0138'},
        {value: 'AK104-IIT-C-N2-0139', label: 'AK104-IIT-C-N2-0139'},
        {value: 'AK104-IIT-C-N2-0143', label: 'AK104-IIT-C-N2-0143'},
        {value: 'AK104-IIT-C-N2-0144', label: 'AK104-IIT-C-N2-0144'},
        {value: 'AK104-IIT-C-N2-0145', label: 'AK104-IIT-C-N2-0145'},
        {value: 'AK104-IIT-C-N2-0146', label: 'AK104-IIT-C-N2-0146'},
        {value: 'AK104-IIT-C-S-0002', label: 'AK104-IIT-C-S-0002'},
        {value: 'AK104-IIT-C-S-0004', label: 'AK104-IIT-C-S-0004'},
        {value: 'AK104-IIT-C-S-0005', label: 'AK104-IIT-C-S-0005'},
        {value: 'AK104-IIT-C-S-0007', label: 'AK104-IIT-C-S-0007'},
        {value: 'AK104-IIT-C-S-0008', label: 'AK104-IIT-C-S-0008'},
        {value: 'AK104-IIT-C-S-0010', label: 'AK104-IIT-C-S-0010'},
        {value: 'AK104-IIT-C-S-0011', label: 'AK104-IIT-C-S-0011'},
        {value: 'AK104-IIT-C-S-0012', label: 'AK104-IIT-C-S-0012'},
        {value: 'AK104-IIT-C-S-0013', label: 'AK104-IIT-C-S-0013'},
        {value: 'AK104-IIT-C-S-0014', label: 'AK104-IIT-C-S-0014'},
        {value: 'AK104-IIT-C-S-0015', label: 'AK104-IIT-C-S-0015'},
        {value: 'AK104-IIT-C-S-0018', label: 'AK104-IIT-C-S-0018'},
        {value: 'AK104-IIT-C-S-0019', label: 'AK104-IIT-C-S-0019'},
        {value: 'AK104-IIT-C-S-0021', label: 'AK104-IIT-C-S-0021'},
        {value: 'AK104-IIT-C-S-0022', label: 'AK104-IIT-C-S-0022'},
        {value: 'AK104-IIT-C-S-0023', label: 'AK104-IIT-C-S-0023'},
        {value: 'AK104-IIT-C-S-0024', label: 'AK104-IIT-C-S-0024'},
        {value: 'AK104-IIT-C-S-0025', label: 'AK104-IIT-C-S-0025'},
        {value: 'AK104-IIT-C-S-0026', label: 'AK104-IIT-C-S-0026'},
        {value: 'AK104-IIT-C-S-0027', label: 'AK104-IIT-C-S-0027'},
        {value: 'AK104-IIT-C-S-0029', label: 'AK104-IIT-C-S-0029'},
        {value: 'AK104-IIT-C-S-0030', label: 'AK104-IIT-C-S-0030'},
        {value: 'AK104-IIT-C-S-0032', label: 'AK104-IIT-C-S-0032'},
        {value: 'AK104-IIT-C-S-0034', label: 'AK104-IIT-C-S-0034'},
        {value: 'AK104-IIT-C-S-0035', label: 'AK104-IIT-C-S-0035'},
        {value: 'AK104-IIT-C-S-0041', label: 'AK104-IIT-C-S-0041'},
        {value: 'AK104-IIT-C-S-0043', label: 'AK104-IIT-C-S-0043'},
        {value: 'AK104-IIT-C-S-0044', label: 'AK104-IIT-C-S-0044'},
        {value: 'AK104-IIT-C-S-0045', label: 'AK104-IIT-C-S-0045'},
        {value: 'AK104-IIT-C-S-0050', label: 'AK104-IIT-C-S-0050'},
        {value: 'AK104-IIT-C-S-0051', label: 'AK104-IIT-C-S-0051'},
        {value: 'AK104-IIT-C-S-0052', label: 'AK104-IIT-C-S-0052'},
        {value: 'AK104-IIT-C-S-0053', label: 'AK104-IIT-C-S-0053'},
        {value: 'AK104-IIT-C-S-0055', label: 'AK104-IIT-C-S-0055'},
        {value: 'AK104-IIT-C-S-0056', label: 'AK104-IIT-C-S-0056'},
        {value: 'AK104-IIT-C-S-0057', label: 'AK104-IIT-C-S-0057'},
        {value: 'AK104-IIT-C-S-0059', label: 'AK104-IIT-C-S-0059'},
        {value: 'AK104-IIT-C-S-0061', label: 'AK104-IIT-C-S-0061'},
        {value: 'AK104-IIT-C-S-0062', label: 'AK104-IIT-C-S-0062'},
        {value: 'AK104-IIT-C-S-0063', label: 'AK104-IIT-C-S-0063'},
        {value: 'AK104-IIT-C-S-0064', label: 'AK104-IIT-C-S-0064'},
        {value: 'AK104-IIT-C-S-0065', label: 'AK104-IIT-C-S-0065'},
        {value: 'AK104-IIT-C-S-0068', label: 'AK104-IIT-C-S-0068'},
        {value: 'AK104-IIT-C-S-0070', label: 'AK104-IIT-C-S-0070'},
        {value: 'AK104-IIT-C-S-0071', label: 'AK104-IIT-C-S-0071'},
        {value: 'AK104-IIT-C-S-0074', label: 'AK104-IIT-C-S-0074'},
        {value: 'AK104-IIT-C-S-0075', label: 'AK104-IIT-C-S-0075'},
        {value: 'AK104-IIT-C-S-0076', label: 'AK104-IIT-C-S-0076'},
        {value: 'AK104-IIT-C-S-0078', label: 'AK104-IIT-C-S-0078'},
        {value: 'AK104-IIT-C-S-0082', label: 'AK104-IIT-C-S-0082'},
        {value: 'AK104-IIT-C-S-0083', label: 'AK104-IIT-C-S-0083'},
        {value: 'AK104-IIT-C-S-0085', label: 'AK104-IIT-C-S-0085'},
        {value: 'AK104-IIT-C-S-0086', label: 'AK104-IIT-C-S-0086'},
        {value: 'AK104-IIT-C-S-0087', label: 'AK104-IIT-C-S-0087'},
        {value: 'AK104-IIT-C-S-0088', label: 'AK104-IIT-C-S-0088'},
        {value: 'AK104-IIT-C-S-0090', label: 'AK104-IIT-C-S-0090'},
        {value: 'AK104-IIT-C-S-0092', label: 'AK104-IIT-C-S-0092'},
        {value: 'AK104-IIT-C-S-0093', label: 'AK104-IIT-C-S-0093'},
        {value: 'AK104-IIT-C-S-0096', label: 'AK104-IIT-C-S-0096'},
        {value: 'AK104-IIT-C-S-0097', label: 'AK104-IIT-C-S-0097'},
        {value: 'AK104-IIT-C-S-0098', label: 'AK104-IIT-C-S-0098'},
        {value: 'AK104-IIT-C-S-0099', label: 'AK104-IIT-C-S-0099'},
        {value: 'AK104-IIT-C-S-0100', label: 'AK104-IIT-C-S-0100'},
        {value: 'AK104-IIT-C-S-0101', label: 'AK104-IIT-C-S-0101'},
        {value: 'AK104-IIT-C-S-0102', label: 'AK104-IIT-C-S-0102'},
        {value: 'AK104-IIT-C-S-0103', label: 'AK104-IIT-C-S-0103'},
        {value: 'AK104-IIT-C-S-0105', label: 'AK104-IIT-C-S-0105'},
        {value: 'AK104-IIT-C-S-0106', label: 'AK104-IIT-C-S-0106'},
        {value: 'AK104-IIT-C-S-0108', label: 'AK104-IIT-C-S-0108'},
        {value: 'AK104-IIT-C-S-0110', label: 'AK104-IIT-C-S-0110'},
        {value: 'AK104-IIT-C-S-0112', label: 'AK104-IIT-C-S-0112'},
        {value: 'AK104-IIT-C-S-0114', label: 'AK104-IIT-C-S-0114'},
        {value: 'AK104-IIT-C-S-0117', label: 'AK104-IIT-C-S-0117'},
        {value: 'AK104-IIT-C-S-0118', label: 'AK104-IIT-C-S-0118'},
        {value: 'AK104-IIT-C-S-0120', label: 'AK104-IIT-C-S-0120'},
        {value: 'AK104-IIT-C-S-0122', label: 'AK104-IIT-C-S-0122'},
        {value: 'AK104-IIT-C-S-0123', label: 'AK104-IIT-C-S-0123'},
        {value: 'AK104-IIT-C-S-0124', label: 'AK104-IIT-C-S-0124'},
        {value: 'AK104-IIT-C-S-0125', label: 'AK104-IIT-C-S-0125'},
        {value: 'AK104-IIT-C-S-0128', label: 'AK104-IIT-C-S-0128'},
        {value: 'AK104-IIT-C-S-0129', label: 'AK104-IIT-C-S-0129'},
        {value: 'AK104-IIT-C-W-0019', label: 'AK104-IIT-C-W-0019'},
        {value: 'AK104-IIT-C-W-0021', label: 'AK104-IIT-C-W-0021'},
        {value: 'AK104-IIT-C-W-0024', label: 'AK104-IIT-C-W-0024'},
        {value: 'AK104-IIT-C-W-0025', label: 'AK104-IIT-C-W-0025'},
        {value: 'AK104-IIT-C-W-0026', label: 'AK104-IIT-C-W-0026'},
        {value: 'AK104-IIT-C-W-0027', label: 'AK104-IIT-C-W-0027'},
        {value: 'AK104-IIT-C-W-0029', label: 'AK104-IIT-C-W-0029'},
        {value: 'AK104-IIT-C-W-0030', label: 'AK104-IIT-C-W-0030'},
        {value: 'AK104-IIT-C-W-0032', label: 'AK104-IIT-C-W-0032'},
        {value: 'AK104-IIT-C-W-0035', label: 'AK104-IIT-C-W-0035'},
        {value: 'AK104-IIT-C-W-0036', label: 'AK104-IIT-C-W-0036'},
        {value: 'AK104-IIT-C-W-0037', label: 'AK104-IIT-C-W-0037'},
        {value: 'AK104-IIT-C-W-0040', label: 'AK104-IIT-C-W-0040'},
        {value: 'AK104-IIT-C-W-0041', label: 'AK104-IIT-C-W-0041'},
        {value: 'AK104-IIT-C-W-0042', label: 'AK104-IIT-C-W-0042'},
        {value: 'AK104-IIT-C-W-0043', label: 'AK104-IIT-C-W-0043'},
        {value: 'AK104-IIT-C-W-0044', label: 'AK104-IIT-C-W-0044'},
        {value: 'AK104-IIT-C-W-0045', label: 'AK104-IIT-C-W-0045'},
        {value: 'AK104-IIT-C-W-0047', label: 'AK104-IIT-C-W-0047'},
        {value: 'AK104-IIT-C-W-0048', label: 'AK104-IIT-C-W-0048'},
        {value: 'AK104-IIT-C-W-0049', label: 'AK104-IIT-C-W-0049'},
        {value: 'AK104-IIT-C-W-0050', label: 'AK104-IIT-C-W-0050'},
        {value: 'AK104-IIT-C-W-0052', label: 'AK104-IIT-C-W-0052'},
        {value: 'AK104-IIT-C-W-0053', label: 'AK104-IIT-C-W-0053'},
        {value: 'AK104-IIT-C-W-0054', label: 'AK104-IIT-C-W-0054'},
        {value: 'AK104-IIT-C-W-0055', label: 'AK104-IIT-C-W-0055'},
        {value: 'AK104-IIT-C-W-0056', label: 'AK104-IIT-C-W-0056'},
        {value: 'AK104-IIT-C-W-0057', label: 'AK104-IIT-C-W-0057'},
        {value: 'AK104-IIT-C-W-0058', label: 'AK104-IIT-C-W-0058'},
        {value: 'AK104-IIT-C-W-0059', label: 'AK104-IIT-C-W-0059'},
        {value: 'AK104-IIT-C-W-0106', label: 'AK104-IIT-C-W-0106'},
        {value: 'AK104-IIT-C-W-0107', label: 'AK104-IIT-C-W-0107'},
        {value: 'AK104-IIT-C-W-0108', label: 'AK104-IIT-C-W-0108'},
        {value: 'AK104-IIT-C-W-0109', label: 'AK104-IIT-C-W-0109'},
        {value: 'AK104-IIT-C-W-0110', label: 'AK104-IIT-C-W-0110'},
        {value: 'AK104-IIT-C-W-0111', label: 'AK104-IIT-C-W-0111'},
        {value: 'AK104-IIT-C-W-0115', label: 'AK104-IIT-C-W-0115'},
        {value: 'AK104-IIT-C-W-0117', label: 'AK104-IIT-C-W-0117'},
        {value: 'AK104-IIT-C-W-0120', label: 'AK104-IIT-C-W-0120'},
        {value: 'AK104-IIT-C-W-0122', label: 'AK104-IIT-C-W-0122'},
        {value: 'AK104-IIT-C-W-0123', label: 'AK104-IIT-C-W-0123'},
        {value: 'AK104-IIT-C-W-0125', label: 'AK104-IIT-C-W-0125'},
        {value: 'AK104-IIT-C-W-0127', label: 'AK104-IIT-C-W-0127'},
        {value: 'AK104-IIT-C-W-0128', label: 'AK104-IIT-C-W-0128'},
        {value: 'AK104-IIT-C-W-0129', label: 'AK104-IIT-C-W-0129'},
        {value: 'AK104-IIT-C-W-0132', label: 'AK104-IIT-C-W-0132'},
        {value: 'AK104-IIT-C-W-0133', label: 'AK104-IIT-C-W-0133'},
        {value: 'AK104-RC48', label: 'AK104-RC48'},
        {value: 'AK104ZY001', label: 'AK104ZY001'},
        {value: 'AK105', label: 'AK105'},
        {value: 'AK105-101', label: 'AK105-101'},
        {value: 'AK105-201', label: 'AK105-201'},
        {value: 'AK105-202', label: 'AK105-202'},
        {value: 'AK105-203', label: 'AK105-203'},
        {value: 'AK105-204', label: 'AK105-204'},
        {value: 'AK105-205', label: 'AK105-205'},
        {value: 'AK105-301', label: 'AK105-301'},
        {value: 'AK105-302', label: 'AK105-302'},
        {value: 'AK105-303', label: 'AK105-303'},
        {value: 'AK105-304', label: 'AK105-304'},
        {value: 'KF051', label: 'KF051'},
        {value: 'AK105-SAS', label: 'AK105-SAS'},
        {value: 'AK106', label: 'AK106'},
        {value: 'KF025', label: 'KF025'},
        {value: 'AK107', label: 'AK107'},
        {value: 'KF001', label: 'KF001'},
        {value: 'AK109', label: 'AK109'},
        {value: 'AK109-101', label: 'AK109-101'},
        {value: 'AK109-102', label: 'AK109-102'},
        {value: 'AK109-103', label: 'AK109-103'},
        {value: 'AK109-201', label: 'AK109-201'},
        {value: 'AK109-202', label: 'AK109-202'},
        {value: 'AK109-301', label: 'AK109-301'},
        {value: 'KF031', label: 'KF031'},
        {value: 'AK111', label: 'AK111'},
        {value: 'AK111-101', label: 'AK111-101'},
        {value: 'AK111-102', label: 'AK111-102'},
        {value: 'AK111-103', label: 'AK111-103'},
        {value: 'AK111-201', label: 'AK111-201'},
        {value: 'AK111-202', label: 'AK111-202'},
        {value: 'AK111-203', label: 'AK111-203'},
        {value: 'AK111-301', label: 'AK111-301'},
        {value: 'AK111-302', label: 'AK111-302'},
        {value: 'AK111-303', label: 'AK111-303'},
        {value: 'KF053', label: 'KF053'},
        {value: 'AK112', label: 'AK112'},
        {value: 'AK112-101', label: 'AK112-101'},
        {value: 'AK112-102', label: 'AK112-102'},
        {value: 'AK112-103', label: 'AK112-103'},
        {value: 'AK112-104', label: 'AK112-104'},
        {value: 'AK112-201', label: 'AK112-201'},
        {value: 'AK112-202', label: 'AK112-202'},
        {value: 'AK112-203', label: 'AK112-203'},
        {value: 'AK112-204', label: 'AK112-204'},
        {value: 'AK112-205', label: 'AK112-205'},
        {value: 'AK112-206', label: 'AK112-206'},
        {value: 'AK112-207', label: 'AK112-207'},
        {value: 'AK112-208', label: 'AK112-208'},
        {value: 'AK112-209', label: 'AK112-209'},
        {value: 'AK112-210', label: 'AK112-210'},
        {value: 'AK112-301', label: 'AK112-301'},
        {value: 'AK112-302', label: 'AK112-302'},
        {value: 'AK112-303', label: 'AK112-303'},
        {value: 'AK112-305', label: 'AK112-305'},
        {value: 'AK112-306', label: 'AK112-306'},
        {value: 'AK112-307', label: 'AK112-307'},
        {value: 'AK112-308', label: 'AK112-308'},
        {value: 'AK112-309', label: 'AK112-309'},
        {value: 'AK112-310', label: 'AK112-310'},
        {value: 'AK112-SAS', label: 'AK112-SAS'},
        {value: 'KF064', label: 'KF064'},
        {value: 'AK112-3003', label: 'AK112-3003'},
        {value: 'SM112-301', label: 'SM112-301'},
        {value: 'AK112-IIT-001', label: 'AK112-IIT-001'},
        {value: 'AK112-IIT-002', label: 'AK112-IIT-002'},
        {value: 'AK112-IIT-004', label: 'AK112-IIT-004'},
        {value: 'AK112-IIT-006', label: 'AK112-IIT-006'},
        {value: 'AK112-IIT-007', label: 'AK112-IIT-007'},
        {value: 'AK112-IIT-009', label: 'AK112-IIT-009'},
        {value: 'AK112-IIT-C-E-0001', label: 'AK112-IIT-C-E-0001'},
        {value: 'AK112-IIT-C-E-0002', label: 'AK112-IIT-C-E-0002'},
        {value: 'AK112-IIT-C-E-0003', label: 'AK112-IIT-C-E-0003'},
        {value: 'AK112-IIT-C-E-0006', label: 'AK112-IIT-C-E-0006'},
        {value: 'AK112-IIT-C-E-0007', label: 'AK112-IIT-C-E-0007'},
        {value: 'AK112-IIT-C-M-0001', label: 'AK112-IIT-C-M-0001'},
        {value: 'AK112-IIT-C-W-0001', label: 'AK112-IIT-C-W-0001'},
        {value: 'AK112ZY001', label: 'AK112ZY001'},
        {value: 'AK114', label: 'AK114'},
        {value: 'AK114-101', label: 'AK114-101'},
        {value: 'AK114-102', label: 'AK114-102'},
        {value: 'KF041', label: 'KF041'},
        {value: 'AK115', label: 'AK115'},
        {value: 'AK115-101', label: 'AK115-101'},
        {value: 'KF059', label: 'KF059'},
        {value: 'AK116', label: 'AK116'},
        {value: 'AK117', label: 'AK117'},
        {value: 'AK117-101', label: 'AK117-101'},
        {value: 'AK117-102', label: 'AK117-102'},
        {value: 'AK117-103', label: 'AK117-103'},
        {value: 'AK117-104', label: 'AK117-104'},
        {value: 'AK117-201', label: 'AK117-201'},
        {value: 'AK117-202', label: 'AK117-202'},
        {value: 'AK117-203', label: 'AK117-203'},
        {value: 'AK117-204', label: 'AK117-204'},
        {value: 'AK117-205', label: 'AK117-205'},
        {value: 'AK117-206', label: 'AK117-206'},
        {value: 'AK117-301', label: 'AK117-301'},
        {value: 'AK117-302', label: 'AK117-302'},
        {value: 'AK117-SAS', label: 'AK117-SAS'},
        {value: 'KF039', label: 'KF039'},
        {value: 'AK117ZY001', label: 'AK117ZY001'},
        {value: 'AK119', label: 'AK119'},
        {value: 'AK119-101', label: 'AK119-101'},
        {value: 'AK119-102', label: 'AK119-102'},
        {value: 'AK119-103', label: 'AK119-103'},
        {value: 'AK119-104', label: 'AK119-104'},
        {value: 'AK119-105', label: 'AK119-105'},
        {value: 'AK119-201', label: 'AK119-201'},
        {value: 'AK119-202', label: 'AK119-202'},
        {value: 'KF068', label: 'KF068'},
        {value: 'AK120', label: 'AK120'},
        {value: 'AK120-101', label: 'AK120-101'},
        {value: 'AK120-102', label: 'AK120-102'},
        {value: 'AK120-201', label: 'AK120-201'},
        {value: 'AK120-202', label: 'AK120-202'},
        {value: 'AK120-203', label: 'AK120-203'},
        {value: 'AK120-204', label: 'AK120-204'},
        {value: 'AK120-205', label: 'AK120-205'},
        {value: 'AK120-206', label: 'AK120-206'},
        {value: 'AK120-207', label: 'AK120-207'},
        {value: 'AK120-301', label: 'AK120-301'},
        {value: 'KF070', label: 'KF070'},
        {value: 'AK122', label: 'AK122'},
        {value: 'AK123', label: 'AK123'},
        {value: 'AK124', label: 'AK124'},
        {value: 'AK125', label: 'AK125'},
        {value: 'AK126', label: 'AK126'},
        {value: 'AK127', label: 'AK127'},
        {value: 'AK127-101', label: 'AK127-101'},
        {value: 'AK127-102', label: 'AK127-102'},
        {value: 'AK127-103', label: 'AK127-103'},
        {value: 'AK127-104', label: 'AK127-104'},
        {value: 'AK127ZY001', label: 'AK127ZY001'},
        {value: 'AK128', label: 'AK128'},
        {value: 'AK129', label: 'AK129'},
        {value: 'AK129-101', label: 'AK129-101'},
        {value: 'AK129-102', label: 'AK129-102'},
        {value: 'AK129-103', label: 'AK129-103'},
        {value: 'KF083', label: 'KF083'},
        {value: 'AK130', label: 'AK130'},
        {value: 'AK130-101', label: 'AK130-101'},
        {value: 'AK130-102', label: 'AK130-102'},
        {value: 'AK131', label: 'AK131'},
        {value: 'AK131-101', label: 'AK131-101'},
        {value: 'AK132', label: 'AK132'},
        {value: 'AK132-101', label: 'AK132-101'},
        {value: 'AK133', label: 'AK133'},
        {value: 'AK134', label: 'AK134'},
        {value: 'AK135', label: 'AK135'},
        {value: 'AK136', label: 'AK136'},
        {value: 'AK137', label: 'AK137'},
        {value: 'AK138', label: 'AK138'},
        {value: 'AK138D1', label: 'AK138D1'},
        {value: 'AK139', label: 'AK139'},
        {value: 'AK140', label: 'AK140'},
        {value: 'AK140N', label: 'AK140N'},
        {value: 'AK140N-IIT-001', label: 'AK140N-IIT-001'},
        {value: 'AK140N-IIT-002', label: 'AK140N-IIT-002'},
        {value: 'AK141', label: 'AK141'},
        {value: 'AK142(研发项目KF085)', label: 'AK142(研发项目KF085)'},
        {value: 'AK143', label: 'AK143'},
        {value: 'AK143(研发项目KF085)', label: 'AK143(研发项目KF085)'},
        {value: 'AK144', label: 'AK144'},
        {value: 'AK145', label: 'AK145'},
        {value: 'AK146', label: 'AK146'},
        {value: 'AK146D1', label: 'AK146D1'},
        {value: 'AK147', label: 'AK147'},
        {value: 'AK148', label: 'AK148'},
        {value: 'AK150', label: 'AK150'},
        {value: '公摊内部订单（财务和临床专用）', label: '公摊内部订单（财务和临床专用）'},
        {value: 'KF000', label: 'KF000'},
        {value: 'KF002', label: 'KF002'},
        {value: 'KF005', label: 'KF005'},
        {value: 'KF006', label: 'KF006'},
        {value: 'KF018', label: 'KF018'},
        {value: 'KF021', label: 'KF021'},
        {value: 'KF022', label: 'KF022'},
        {value: 'KF023', label: 'KF023'},
        {value: 'KF024', label: 'KF024'},
        {value: 'KF026', label: 'KF026'},
        {value: 'KF027', label: 'KF027'},
        {value: 'KF032', label: 'KF032'},
        {value: 'KF033', label: 'KF033'},
        {value: 'KF035', label: 'KF035'},
        {value: 'KF036', label: 'KF036'},
        {value: 'KF037', label: 'KF037'},
        {value: 'KF040', label: 'KF040'},
        {value: 'KF042', label: 'KF042'},
        {value: 'KF042ZB1', label: 'KF042ZB1'},
        {value: 'KF047', label: 'KF047'},
        {value: 'KF049', label: 'KF049'},
        {value: 'KF052', label: 'KF052'},
        {value: 'KF061', label: 'KF061'},
        {value: 'KF062', label: 'KF062'},
        {value: 'KF063', label: 'KF063'},
        {value: 'KF069', label: 'KF069'},
        {value: 'KF071', label: 'KF071'},
        {value: 'KF072', label: 'KF072'},
        {value: 'KF073', label: 'KF073'},
        {value: 'KF074', label: 'KF074'},
        {value: 'KF075', label: 'KF075'},
        {value: 'KF076', label: 'KF076'},
        {value: 'KF077', label: 'KF077'},
        {value: 'KF078', label: 'KF078'},
        {value: 'KF079', label: 'KF079'},
        {value: 'KF084', label: 'KF084'},
        {value: 'KF085', label: 'KF085'},
        {value: 'KF086', label: 'KF086'},
        {value: 'KF087', label: 'KF087'},
        {value: 'KF088', label: 'KF088'},
        {value: 'KF089', label: 'KF089'},
        {value: 'KF090', label: 'KF090'},
        {value: 'KF091', label: 'KF091'},
        {value: 'KF092', label: 'KF092'},
        {value: 'KF093', label: 'KF093'},
        {value: 'KF094', label: 'KF094'},
        {value: 'KF095', label: 'KF095'},
        {value: 'KF096', label: 'KF096'},
        {value: 'KF097', label: 'KF097'},
        {value: 'KF098', label: 'KF098'},
        {value: 'KF099', label: 'KF099'},
        {value: 'KF100', label: 'KF100'},
        {value: 'KF101', label: 'KF101'},
        {value: 'KF102', label: 'KF102'},
        {value: 'KF103', label: 'KF103'},
        {value: 'KF104', label: 'KF104'},
        {value: 'KF105', label: 'KF105'},
        {value: 'KF106', label: 'KF106'},
        {value: 'KF107', label: 'KF107'},
        {value: 'KF108', label: 'KF108'},
        {value: 'KF109', label: 'KF109'},
        {value: 'SPH4336-201 药业联合上药', label: 'SPH4336-201 药业联合上药'},
      ],
      monthOptions: [
        {value: '1月', label: '1月'},
        {value: '2月', label: '2月'},
        {value: '3月', label: '3月'},
        {value: '4月', label: '4月'},
        {value: '5月', label: '5月'},
        {value: '6月', label: '6月'},
        {value: '7月', label: '7月'},
        {value: '8月', label: '8月'},
        {value: '9月', label: '9月'},
        {value: '10月', label: '10月'},
        {value: '11月', label: '11月'},
        {value: '12月', label: '12月'}]
    };
  },
  created() {
    this.getYear();
    this.selectAllCompany();
    this.selectAllCostType();
    this.selectAllMonth();
    this.getResearchDevelopmentExpenseYearClassificationSummaryChart();
    this.getCostClassificationSummaryProportionChart();
    this.getCompanyResearchDevelopmentExpenseSummaryChart();
    this.getCostClassificationSummaryYOYChart();
    this.getSinglePipelineProjectSummaryYOYChart();
    this.getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart();
    this.getSingleProjectYearCostChart();
  },
  mounted() {
  },
  methods: {
    /** 全选公司主体 */
    selectAllCompany() {
      this.queryParams.companyName = this.companyOptions.map(a => a.value)
    },
    /** 清空公司主体 */
    clearSelectCompany() {
      this.queryParams.companyName = []
    },
    /** 全选成本分类 */
    selectAllCostType() {
      this.queryParams.costType = this.costTypeOptions.map(a => a.value)
    },
    /** 清空成本分类 */
    clearSelectCostType() {
      this.queryParams.costType = []
    },
    /** 全选月份 */
    selectAllMonth() {
      this.queryParams.month = this.monthOptions.map(a => a.value)
    },
    /** 清空月份 */
    clearSelectMonth() {
      this.queryParams.month = []
    },
    /** 获取当前年份 */
    async getYear() {
      let year = new Date().getFullYear();
      for (let i = 2017; i <= year; i++) {
        this.yearData.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询各年度研发费用分类汇总  图表数据 */
    async getResearchDevelopmentExpenseYearClassificationSummaryChart() {
      this.loading = true;
      this.queryParams.params = {};
      getResearchDevelopmentExpenseYearClassificationSummaryChart(this.queryParams).then(response => {
        this.researchDevelopmentExpenseYearClassificationSummaryList = response.rows;
        var a = this.researchDevelopmentExpenseYearClassificationSummaryList[0].costList;
        var b = this.researchDevelopmentExpenseYearClassificationSummaryList[0].yearList;
        var c = this.researchDevelopmentExpenseYearClassificationSummaryList[0].mapListData;
        this.getResearchDevelopmentExpenseYearClassificationSummaryChartVoList(a, b, c);
      });
    },
    getResearchDevelopmentExpenseYearClassificationSummaryChartVoList(a, b, c) {
      let chartDom = document.getElementById('researchDevelopmentExpenseYearClassificationSummaryChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;
      option = {
        title: {
          text: '各年度研发费用分类汇总',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function (params) {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach(item => {
              if (item.value != 0) {
                result += `${item.marker} ${item.seriesName}: ${item.data}<br/>`;
              }
            });
            return result;
          }
        },
        toolbox: {
          feature: {
            dataView: {show: true, readOnly: false},
            magicType: {show: true, type: ['line', 'bar']},
            restore: {show: true},
            saveAsImage: {show: true}
          }
        },
        legend: {
          data: a
        },
        xAxis: [
          {
            type: 'category',
            data: b,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '成本分类金额（万元）',
            axisLabel: {
              formatter: '{value}' + ' '
            }
          },
          {
            type: 'value',
            name: '年度合计金额（万元）',
            axisLabel: {
              formatter: '{value}' + ' '
            }
          }
        ],
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          if (key == '年度汇总') {
            x.type = 'line';
            x.yAxisIndex = 1;
            x.color = 'rgb(7,245,245)';
            x.label = {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            };
          } else {
            x.type = 'bar';
            x.stack = 'bar';
            x.label = {
              show: false,
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          x.data = map[key];
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询成本分类汇总占比  图表数据 */
    async getCostClassificationSummaryProportionChart() {
      this.loading = true;
      this.queryParams.params = {};
      getCostClassificationSummaryProportionChart(this.queryParams).then(response => {
        this.costClassificationSummaryProportionList = response.rows;
        var a = this.costClassificationSummaryProportionList[0].costList;
        var b = this.costClassificationSummaryProportionList[0].mapListData;
        this.getCostClassificationSummaryProportionChartVoList(a, b);
      });
    },
    getCostClassificationSummaryProportionChartVoList(a, b) {
      let chartDom = document.getElementById('costClassificationSummaryProportionChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;
      option = {
        title: {
          text: this.queryParams.year + '年成本分类汇总占比',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          x: 'center',
          y: 'bottom',
          bottom: '5%',
          data: a
          /* left: '15%',
           top: 'middle',
           orient: 'vertical'*/

        },
        series: [
          {
            name: ' ',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: b,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontWeight: 200,
                    fontsize: 16
                  },
                  formatter: function (params) {
                    if (params.value != 0) {
                      return params.name + ' ' + echarts.format.addCommas(params.value) + ' (' + params.percent + '%)';
                    } else {
                      return params.name + ' ' + echarts.format.addCommas(params.value) + ' (0 %)';
                    }
                  }
                },
                labelLine: {
                  show: true,
                  smooth: 0.5,
                  length: 20,
                  length2: 20
                }
              }
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    /** 查询各公司研发费用汇总 图表数据 */
    async getCompanyResearchDevelopmentExpenseSummaryChart() {
      this.loading = true;
      this.queryParams.params = {};
      getCompanyResearchDevelopmentExpenseSummaryChart(this.queryParams).then(response => {
        this.companyResearchDevelopmentExpenseSummaryList = response.rows;
        var a = this.companyResearchDevelopmentExpenseSummaryList[0].companyList;
        var b = this.companyResearchDevelopmentExpenseSummaryList[0].moneyList;
        this.getCompanyResearchDevelopmentExpenseSummaryChartVoList(a, b);
      });
    },
    getCompanyResearchDevelopmentExpenseSummaryChartVoList(a, b) {
      var chartDom = document.getElementById('companyResearchDevelopmentExpenseSummaryChart');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        title: {
          text: this.queryParams.year + '年各公司研发费用汇总',
          subtext: '金额（万元）',
          /* textStyle:{
             color: '#333',
             fontSize: 30,
             fontWeight: 'bold'
           }*/
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a,
          axisLabel: {
            interval: 0,
            rotate: 40
          },
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: b,
            type: 'bar',
            label: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    /** 查询成本分类汇总同比 图表数据 */
    async getCostClassificationSummaryYOYChart() {
      this.loading = true;
      this.queryParams.params = {};
      getCostClassificationSummaryYOYChart(this.queryParams).then(response => {
        this.costClassificationSummaryYOYList = response.rows;
        var a = this.costClassificationSummaryYOYList[0].costList;
        var b = this.costClassificationSummaryYOYList[0].mapListData;
        this.getCostClassificationSummaryYOYChartVoList(a, b);
      });
    },
    getCostClassificationSummaryYOYChartVoList(a, b) {
      let chartDom = document.getElementById('costClassificationSummaryYOYChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: this.queryParams.yearMonth + "成本分类汇总同比",
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          /* x.barWidth = '50%';
           x.barMaxWidth = '30';*/
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    /** 查询单管线、项目研发费用汇总同比 图表数据 */
    async getSinglePipelineProjectSummaryYOYChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSinglePipelineProjectSummaryYOYChart(this.queryParams).then(response => {
        this.singlePipelineProjectSummaryYOYList = response.rows;
        var a = this.singlePipelineProjectSummaryYOYList[0].projectList;
        var b = this.singlePipelineProjectSummaryYOYList[0].mapListData;
        this.getSinglePipelineProjectSummaryYOYChartVoList(a, b);
      });
    },
    getSinglePipelineProjectSummaryYOYChartVoList(a, b) {
      let chartDom = document.getElementById('singlePipelineProjectSummaryYOYChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: this.queryParams.yearMonth + '-' + this.queryParams.singlePipeline + "、项目研发费用汇总同比",
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          /* x.barWidth = '50%';
           x.barMaxWidth = '30';*/
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    /** 查询多管线、研发费用汇总同比 图表数据 */
    async getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart() {
      this.loading = true;
      this.queryParams.params = {};
      getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart(this.queryParams).then(response => {
        this.multiplePipelineResearchDevelopmentExpenseSummaryYOYList = response.rows;
        var a = this.multiplePipelineResearchDevelopmentExpenseSummaryYOYList[0].pipelineList;
        var b = this.multiplePipelineResearchDevelopmentExpenseSummaryYOYList[0].mapListData;
        this.getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChartVoList(a, b);
      });
    },
    getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChartVoList(a, b) {
      let chartDom = document.getElementById('multiplePipelineResearchDevelopmentExpenseSummaryYOYChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: this.queryParams.yearMonth + '-' + "多管线、研发费用汇总同比",
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          // x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          /* x.barWidth = '50%';
           x.barMaxWidth = '30';*/
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    /** 查询单项目各年度成本类别 图表数据 */
    async getSingleProjectYearCostChart() {
      this.loading = true;
      this.queryParams.params = {};
      getSingleProjectYearCostChart(this.queryParams).then(response => {
        this.singleProjectYearCostList = response.rows;
        var a = this.singleProjectYearCostList[0].yearList;
        var b = this.singleProjectYearCostList[0].mapListData;
        this.getSingleProjectYearCostChartVoList(a, b);
      });
    },
    getSingleProjectYearCostChartVoList(a, b) {
      let chartDom = document.getElementById('singleProjectYearCostChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;

      option = {
        title: {
          text: this.queryParams.singlePipeline + '-' + "各年度成本类别",
          subtext: '金额（万元）'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {},
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: a
          /* axisLabel: {
             interval: 0,
             rotate: 40
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (let i = 0; i < b.length; i++) {
        var map = b[i];
        for (let mapKey in map) {
          let x = {};
          x.name = mapKey;
          x.type = 'bar';
          x.stack = 'total';
          x.emphasis = {
            focus: 'series'
          };
          x.data = map[mapKey];
          /* x.barWidth = '50%';
           x.barMaxWidth = '30';*/
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      ;
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getResearchDevelopmentExpenseYearClassificationSummaryChart();
      this.getCostClassificationSummaryProportionChart();
      this.getCompanyResearchDevelopmentExpenseSummaryChart();
      this.getCostClassificationSummaryYOYChart();
      this.getSinglePipelineProjectSummaryYOYChart();
      this.getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart();
      this.getSingleProjectYearCostChart();
    }
  }
};
</script>

