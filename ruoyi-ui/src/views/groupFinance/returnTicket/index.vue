<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangeApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="费用承担" prop="costBearing">
        <el-input
          v-model="queryParams.costBearing"
          placeholder="请输入费用承担"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收款单位" prop="receivingUnit">
        <el-input
          v-model="queryParams.receivingUnit"
          placeholder="请输入收款单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="丢票退票" prop="lostTicketReturnTicket">
        <el-input
          v-model="queryParams.lostTicketReturnTicket"
          placeholder="请输入丢票、退票"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['groupFinance:returnTicket:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="returnTicketList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="OA单号" align="center" prop="oANumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人姓名" align="center" prop="reimbursementPerson" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担" align="center" prop="costBearing" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="收款单位" align="center" prop="receivingUnit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="丢票、退票" align="center" prop="lostTicketReturnTicket" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票类型" align="center" prop="invoiceType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="丢专票类型" align="center" prop="specialTicketType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票金额" align="center" prop="invoiceValue" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号" align="center" prop="invoiceNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="差额发票" align="center" prop="differenceInvoice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="丢、退票原因" align="center" prop="reason" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="关联审批单" align="center" prop="associatedApprovalForm" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listReturnTicket} from "@/api/groupFinance/returnTicket";

export default {
  name: "ReturnTicket",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 退票表格数据
      returnTicketList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前环节时间范围
      daterangeApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationDate: null,
        oANumber: null,
        applicationSubject: null,
        reimbursementPerson: null,
        department: null,
        costBearing: null,
        receivingUnit: null,
        lostTicketReturnTicket: null,
        invoiceType: null,
        specialTicketType: null,
        projectNumber: null,
        invoiceValue: null,
        invoiceNumber: null,
        differenceInvoice: null,
        reason: null,
        associatedApprovalForm: null,
        documentStatus: null,
        currentSession: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询退票列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
        this.queryParams.params["beginApplicationDate"] = this.daterangeApplicationDate[0];
        this.queryParams.params["endApplicationDate"] = this.daterangeApplicationDate[1];
      }
      listReturnTicket(this.queryParams).then(response => {
        this.returnTicketList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationDate: null,
        oANumber: null,
        applicationSubject: null,
        reimbursementPerson: null,
        department: null,
        costBearing: null,
        receivingUnit: null,
        lostTicketReturnTicket: null,
        invoiceType: null,
        specialTicketType: null,
        projectNumber: null,
        invoiceValue: null,
        invoiceNumber: null,
        differenceInvoice: null,
        reason: null,
        associatedApprovalForm: null,
        documentStatus: null,
        currentSession: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeApplicationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationDate)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/returnTicket/export', {
        ...this.queryParams
      }, `returnTicket_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
