<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="表单类型" prop="tableName">
        <el-select placeholder="请选择..." clearable filterable v-model="queryParams.tableName">
          <el-option
            v-for="item in this.queryList"
            :key="item.dataName"
            :label="item.dataName"
            :value="item.dataName">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="申请日期">
        <el-date-picker
          v-model="queryParams.queryDate"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="主题" prop="docSubject">
        <el-input
          v-model="queryParams.docSubject"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="流程金额" prop="expenseAmount">
        <el-input
          v-model="queryParams.expenseAmount"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请人" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="当前节点" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="表单状态" prop="documentStatus">
        <el-select placeholder="请选择..." clearable filterable v-model="queryParams.documentStatus">
          <el-option
            v-for="item in this.documentStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ReportCollectionController:ReportCollection:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" border
              :max-height="tableMaxHeight">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="表单类型" align="center" prop="tableName" width="200" :show-overflow-tooltip="true" fixed/>
      <el-table-column label="单号" align="center" prop="oddNumber" width="180" :show-overflow-tooltip="true" fixed>
        <template v-slot="scope">
          <span @click="jump(scope.row)" style="color: #3967FF;cursor:pointer;">{{ scope.row.oddNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主题" align="center" prop="docSubject" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="费用主体" align="center" prop="expenseEntity" width="180" :show-overflow-tooltip="true"/>
      <el-table-column label="部门" align="center" prop="department" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="流程金额" align="center" prop="expenseAmount" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicationName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="表单状态" align="center" prop="documentStatus" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="当前节点" align="center" prop="currentSession" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="100" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="节点到达时间" align="center" prop="nodeArrivalTime" width="170">-->
<!--        <template v-slot="scope">-->
<!--          <span>{{ parseTime(scope.row.nodeArrivalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="滞留小时" align="center" prop="retentionHours" width="200" :show-overflow-tooltip="true"/>-->
      <el-table-column label="财务BP节点到达时间" align="center" prop="nodeDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.nodeDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="财务BP节点操作时间" align="center" prop="nodePassDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.nodePassDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理时长" align="center" prop="manageLimit" width="100" :show-overflow-tooltip="true">
        <template v-slot="scope">
          <span>{{ scope.row.manageLimit == null ? '' : scope.row.manageLimit + '天'}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {queryDataList,queryDataTableList} from "@/api/groupFinance/ReportCollection.js";

export default {
  name: "ReportCollection",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      disabled: 'disabled',
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 数据集合
      dataList: [],
      documentStatusList:[
        {
          value: '草稿',
          label: '草稿'
        }, {
          value: '待审',
          label: '待审'
        }, {
          value: '驳回',
          label: '驳回'
        }, {
          value: '废弃',
          label: '废弃'
        }, {
          value: '结束',
          label: '结束'
        }
      ],
      queryList: [],
      // 查询参数
      queryParams: {
        // 页码、页显示量
        pageNum: 1,
        pageSize: 10,
        // 日期查询控件值集合
        queryDate: null,
        // 日期范围查询开始、结束
        startDate: null,
        endDate: null,
        // 其他查询参数
        id: null,
        name: null,
        applicationDate: null,
        tableName: "FD05租车费申请",
        dataTable:{
          flag: '1',
        }
      },/** queryParams 范围 */
    };/** return 范围 */
  }, /** data 范围 */
  created() {
    this.getDataTable();
    this.getList();
  }, /** created 范围 */
  computed: {
    /** 页面适应 */
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  }, /** computed 范围 */
  methods: {
    /** 数据查询 */
    getList() {
      this.loading = true;
      if (this.queryParams.queryDate != null && this.queryParams.queryDate !== '') {
        this.queryParams.startDate = this.queryParams.queryDate[0];
        this.queryParams.endDate = this.queryParams.queryDate[1];
      }
      queryDataList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getDataTable(){
      queryDataTableList(this.queryParams.dataTable).then(response => {
        this.queryList = response;
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = null;
      this.queryParams.startDate = null;
      this.queryParams.endDate = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('ReportCollectionController/ReportCollection/export', {
        ...this.queryParams
      }, `财务报表_${dateTime}.xlsx`)
    },
    jump(row) {
      window.open(row.filePath + row.id + row.suffix)
    },
  }, /** methods 范围 */
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
    },
    'queryParams.queryDate1'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate1 = ''
        this.queryParams.startDate1 = ''
        this.queryParams.endDate1 = ''
      }
    },
    'queryParams.queryDate2'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate2 = ''
        this.queryParams.startDate2 = ''
        this.queryParams.endDate2 = ''
      }
    },
  },/** watch 范围 */

};
</script>
