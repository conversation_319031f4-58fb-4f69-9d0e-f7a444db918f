<template>
  <div>
    <div
      ref="myChart"
      id="applyChart"
      style="width: 1000px; height: 900px; background-color: #ffffff; padding: 20px; border-radius: 20px;"
    ></div>
  </div>
</template>

<script>
import { pieChart } from "@/api/groupFinance/supplyChainChart";
import * as echarts from "echarts";
export default {
  data() {
    return {
      costBearingCompany: [],
      dataMap: {},
      totalMap: {},
      option: {},
      applyChart: {},
      pieName: [],
      // 后台数据
      list: []
    };
  },
  created() {
    // this.demo1();
  },
  mounted() {
    // this.newCart();
    this.initApplyCharts();
    this.initData();
  },
  methods: {
    // 初始化图表
    initApplyCharts() {
      this.applyChart = echarts.init(document.querySelector("#applyChart"));
      // 饼图
      this.applyChart.setOption({
        legend: {
          // 图例
          left: "left",
          orient: "vertical"
        },
        title: {
          // 设置饼图标题，位置设为顶部居中
          text: "供应链总支出",
          top: "20%",
          left: "center"
        },
        tooltip: {
          trigger: "item"
        },
        legend: {
          icon: "circle",
          top: "0",
          left: "right"
        },
        series: [
          {
            type: "pie",
            label: {
              show: true,
              formatter: "{b} : {c} ({d}%)", // b代表名称，c代表对应值，d代表百分比
              padding: [0, 10]
              // overflow: "none",
              // fontSize: "10",
              // fontWeight: "bold",
            },
            radius: "30%", //饼图半径
            padding: [0, 100],
            data: [],
            //每一个扇形的颜色
            itemStyle: {
              borderRadius: 10,
              normal: {
                color: function(colors) {
                  let colorList = [
                    "#a78a7d",
                    "#5470c6",
                    "#298270",
                    "#fc8251",
                    "#5470c6",
                    "#33beff",
                    "#fc8251",
                    "#5470c6",
                    "#33FFDD",
                    "#fc8251",
                    "#5710c6",
                    "#3F1FDD",
                    "#f81251",
                    "#4710c6",
                    "#a49acb",
                    "#fc8511",
                    "#c601b2",
                    "#5833ff",
                    "#fc8251",
                    "rgba(229,91,215,0.36)",
                    "#c7c027",
                    "#f151fc",
                    "#5470c6",
                    "#1f8775"
                  ];
                  return colorList[colors.dataIndex];
                }
              }
            }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      });
    },
    //  动态获取饼图数据 并对数据进行处理
    initData() {
      // 处理查询参数
      let params = { ...this.queryParams };
      // 执行查询 查询工单运维表
      pieChart(params).then(response => {
        this.list = response.rows;
        let applyName = this.list.map(item => {
          item.costBearingCompany;
          return item.costBearingCompany;
        });
        let total = this.list.map(item => {
          return item.total;
        });
        let dataList = [];
        this.list.forEach(list => {
          let obj1 = { name: list.costBearingCompany, value: list.total };
          console.log(list.costBearingCompany + list.total);
          dataList.push(obj1);
        });
        this.applyChart.setOption({
          series: [
            {
              // 饼图的数据源
              data: dataList
            }
          ]
        });
      });
    }
  }
};
</script>

<style>
</style>
