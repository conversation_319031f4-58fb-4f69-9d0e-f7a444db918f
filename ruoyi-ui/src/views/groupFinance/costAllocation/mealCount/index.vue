<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="就餐时间" prop="createTime">
        <el-date-picker
          :clearable="false"
          style="width: 220px"
          v-model="queryParams.mealDate"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"/>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['groupFinance:meal:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="mealCountList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column label="姓名" fixed align="center" prop="employeeName"/>
      <el-table-column label="工号" align="center" prop="jobNumber"/>
      <el-table-column label="公司主体" align="center" prop="company"/>
      <el-table-column label="部门" align="center" prop="department" :show-overflow-tooltip='true'/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode"/>
      <el-table-column label="早餐次数" align="center" prop="breakfastCount"/>
      <el-table-column label="午餐次数" align="center" prop="lunchCount"/>
      <el-table-column label="晚餐次数" align="center" prop="dinnerCount"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import {listMealCount} from "@/api/groupFinance/costAllocation";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "costAllocationMealCount",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外出/出差明细表格数据
      mealCountList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        company: null,
        department: null,
        mealDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 上月第一天
              const endDate = new Date(now.getFullYear(), now.getMonth(), 0); // 上月最后一天
              endDate.setDate(endDate.getDate()); // 将时间设置为上月最后一天 23:59:59（如果不加这句，时间会变为下月的第一天 00:00:00）
              picker.$emit('pick', [startDate, endDate]);
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 本月第一天
              picker.$emit('pick', [startDate, now]);
            }
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0, 1); // 本年第一天
              picker.$emit('pick', [start, end]);
            }
          },]
      },
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam(){
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 上月第一天
      const endDate = new Date(now.getFullYear(), now.getMonth(), 0); // 上月最后一天
      endDate.setDate(endDate.getDate());
      this.queryParams.mealDate = [parseTime(startDate, '{y}-{m}-{d}'), parseTime(endDate, '{y}-{m}-{d}')]
    },
    /** 查询外出/出差明细列表 */
    getList() {
      this.loading = true;
      listMealCount(this.queryParams).then(response => {
        this.mealCountList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        company: null,
        department: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/costAllocation/meal/export', {
        ...this.queryParams
      }, `用餐次数统计_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
