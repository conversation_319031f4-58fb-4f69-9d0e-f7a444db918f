<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="85px">
      <el-form-item label="采集人" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入采集人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采集人工号" prop="userAccount">
        <el-input
          v-model="queryParams.userAccount"
          placeholder="请输入采集人工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发票号" prop="invoiceNo">
        <el-input
          v-model="queryParams.invoiceNo"
          placeholder="请输入发票号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联单据" prop="accounttingNo">
        <el-input
          v-model="queryParams.accounttingNo"
          placeholder="请输入关联单据"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采集时间" prop="createTime">
        <el-date-picker clearable
          v-model="dateRangeCreate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['groupFinance:associatedQuotaInvoice:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="associatedQuotaInvoiceList" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="采集人" align="center" prop="userName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="采集人工号" align="center" prop="userAccount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="采集时间" align="center" prop="createTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号" align="center" prop="invoiceNo" width="200" fixed :show-overflow-tooltip="true"/>
      <el-table-column label="发票代码" align="center" prop="invoiceCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="amountTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="是否可抵扣" align="center" prop="isDeduct" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="可抵扣税额" align="center" prop="deductTax" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="修改时间" align="center" prop="updateTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="归属机构ID" align="center" prop="orgId" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="归属机构名称" align="center" prop="orgName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="签收状态" align="center" prop="signStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="附件地址" align="center" prop="fileAddress" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="关联单据" align="center" prop="accounttingNo" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listAssociatedQuotaInvoice, getAssociatedQuotaInvoice, delAssociatedQuotaInvoice, addAssociatedQuotaInvoice, updateAssociatedQuotaInvoice } from "@/api/groupFinance/associatedQuotaInvoice";

export default {
  name: "AssociatedQuotaInvoice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //采集时间范围
      dateRangeCreate: [],
      // 已关联通用定额发票表格数据
      associatedQuotaInvoiceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        userAccount: null,
        createTime: null,
        invoiceNo: null,
        invoiceCode: null,
        amountTax: null,
        isDeduct: null,
        deductTax: null,
        updateTime: null,
        orgId: null,
        orgName: null,
        signStatus: null,
        fileAddress: null,
        accounttingNo: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询已关联通用定额发票列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRangeCreate && '' != this.dateRangeCreate) {
        this.queryParams.params["beginCreateTime"] = this.dateRangeCreate[0];
        this.queryParams.params["endCreateTime"] = this.dateRangeCreate[1];
      }
      listAssociatedQuotaInvoice(this.queryParams).then(response => {
        this.associatedQuotaInvoiceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userName: null,
        userAccount: null,
        createTime: null,
        invoiceNo: null,
        invoiceCode: null,
        amountTax: null,
        isDeduct: null,
        deductTax: null,
        updateTime: null,
        orgId: null,
        orgName: null,
        signStatus: null,
        fileAddress: null,
        accounttingNo: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userName)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('groupFinance/associatedQuotaInvoice/export', {
        ...this.queryParams
      }, `associatedQuotaInvoice_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
