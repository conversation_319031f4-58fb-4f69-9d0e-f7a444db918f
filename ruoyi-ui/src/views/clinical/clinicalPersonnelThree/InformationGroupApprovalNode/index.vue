<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="提交日期">
        <el-date-picker
          v-model="queryParams.queryDate"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="节点到达时间">
        <el-date-picker
          v-model="queryParams.queryDate1"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="节点操作时间">
        <el-date-picker
          v-model="queryParams.queryDate2"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="申请人" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="处理人" prop="xxzName">
        <el-input
          v-model="queryParams.xxzName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="表单名称" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="操作类型" prop="operationType">
        <el-input
          v-model="queryParams.operationType"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="单号" prop="oddNumber">
        <el-input
          v-model="queryParams.oddNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="文档状态" prop="documentStatus">
        <el-input
          v-model="queryParams.documentStatus"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

<!--      <el-form-item label="城市" prop="city">-->
<!--        <el-select placeholder="请选择..." clearable filterable v-model="queryParams.city">-->
<!--          <el-option-->
<!--            v-for="item in this.queryList"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ClinicalPersonnelThree:InformationGroupApprovalNode:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" border
              :max-height="tableMaxHeight">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="单号" align="center" prop="oddNumber" width="200" :show-overflow-tooltip="true" fixed>
        <template v-slot="scope">
          <span @click="jump(scope.row)" style="color: #3967FF;cursor:pointer;">{{ scope.row.oddNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="name" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人所属部门" align="center" prop="department" width="250" :show-overflow-tooltip="true"/>
      <el-table-column label="直属领导" align="center" prop="zsName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="信息组处理人" align="center" prop="xxzName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="表单名称" align="center" prop="tableName" width="250" :show-overflow-tooltip="true"/>
      <el-table-column label="节点名称" align="center" prop="nodeName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="提交日期" align="center" prop="applicationDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点到达时间" align="center" prop="nodeDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.nodeDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点操作时间" align="center" prop="nodePassDate" width="100">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.nodePassDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作类型" align="center" prop="operationType" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="处理时限" align="center" prop="manageLimit" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="完成状态" align="center" prop="completionStatus" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="超时天数" align="center" prop="exceedDays" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="100" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {queryInformationGroupApprovalNodeList} from "@/api/clinical/InformationGroupApprovalNode.js";

export default {
  name: "InformationGroupApprovalNode",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      disabled: 'disabled',
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 数据集合
      dataList: [],
      queryList: [],
      // 查询参数
      queryParams: {
        // 页码、页显示量
        pageNum: 1,
        pageSize: 10,
        // 日期查询控件值集合
        queryDate: null,
        // 日期范围查询开始、结束
        startDate: null,
        endDate: null,
        // 日期查询控件值集合
        queryDate1: null,
        // 日期范围查询开始、结束
        startDate1: null,
        endDate1: null,
        // 日期查询控件值集合
        queryDate2: null,
        // 日期范围查询开始、结束
        startDate2: null,
        endDate2: null,
        // 其他查询参数
        id: null,
        name: null,
        applicationDate: null,
      },/** queryParams 范围 */
    };/** return 范围 */
  }, /** data 范围 */
  created() {
    this.getList();
  }, /** created 范围 */
  computed: {
    /** 页面适应 */
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  }, /** computed 范围 */
  methods: {
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.queryParams.queryDate != null && this.queryParams.queryDate !== '') {
        this.queryParams.startDate = this.queryParams.queryDate[0];
        this.queryParams.endDate = this.queryParams.queryDate[1];
      }
      if (this.queryParams.queryDate1 != null && this.queryParams.queryDate1 !== '') {
        this.queryParams.startDate1 = this.queryParams.queryDate1[0];
        this.queryParams.endDate1 = this.queryParams.queryDate1[1];
      }
      if (this.queryParams.queryDate2 != null && this.queryParams.queryDate2 !== '') {
        this.queryParams.startDate2 = this.queryParams.queryDate2[0];
        this.queryParams.endDate2 = this.queryParams.queryDate2[1];
      }
      queryInformationGroupApprovalNodeList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = null;
      this.queryParams.startDate = null;
      this.queryParams.endDate = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('ClinicalPersonnelThree/InformationGroupApprovalNode/export', {
        ...this.queryParams
      }, `信息组审批节点合集导出_${dateTime}.xlsx`)
    },
    jump(row) {
      window.open(row.filePath)
    },
  }, /** methods 范围 */
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
    },
    'queryParams.queryDate1'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate1 = ''
        this.queryParams.startDate1 = ''
        this.queryParams.endDate1 = ''
      }
    },
    'queryParams.queryDate2'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate2 = ''
        this.queryParams.startDate2 = ''
        this.queryParams.endDate2 = ''
      }
    },
  },/** watch 范围 */

};
</script>
