<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="入职日期">
        <el-date-picker
          v-model="queryParams.serviceDate"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="性别" prop="sex">
        <el-input
          v-model="queryParams.sex"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="员工类型" prop="employeeType">
        <el-input
          v-model="queryParams.employeeType"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="办公地点" prop="officeLocation">
        <el-input
          v-model="queryParams.officeLocation"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="报到地点" prop="reportLocation">
        <el-input
          v-model="queryParams.reportLocation"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="直接上级" prop="manager">
        <el-input
          v-model="queryParams.manager"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['EmploymentNoticeClinicalController:EmploymentNoticeClinical:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" border
              :max-height="tableMaxHeight">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="姓名" align="center" prop="name" width="100" :show-overflow-tooltip="true" fixed/>

      <el-table-column label="职级" align="center" prop="rank" :show-overflow-tooltip="true"/>

      <el-table-column label="所属部门" align="center" prop="department" :width="400" :show-overflow-tooltip="true"/>

      <el-table-column label="性别" align="center" prop="sex" :width="80" :show-overflow-tooltip="true"/>

      <el-table-column label="员工类型" align="center" prop="employeeType" :show-overflow-tooltip="true"/>

      <el-table-column label="办公地点" align="center" prop="officeLocation" :show-overflow-tooltip="true"/>

      <el-table-column label="报到地点" align="center" prop="reportLocation" :show-overflow-tooltip="true"/>

      <el-table-column label="拟入职日期" align="center" prop="serviceDate">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.serviceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

<!--      <el-table-column label="联系电话" align="center" prop="phone" width="120" :show-overflow-tooltip="true"/>-->

      <el-table-column label="职务" align="center" prop="position" :show-overflow-tooltip="true"/>

      <el-table-column label="直接上级" align="center" prop="manager" :width="100" :show-overflow-tooltip="true"/>

<!--      <el-table-column label="试用期限" align="center" prop="trialPeriod" width="100" :show-overflow-tooltip="true">-->
<!--        <template v-slot="scope">-->
<!--        <span>-->
<!--          {{ Number(scope.row.trialPeriod) + '个月' }}-->
<!--        </span>-->
<!--        </template>-->
<!--      </el-table-column>-->

<!--      <el-table-column label="工时类型" align="center" prop="jobType" width="115" :show-overflow-tooltip="true"/>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {queryDataList} from "@/api/clinical/EmploymentNoticeClinical.js";
import {number} from "echarts";

export default {
  name: "EmploymentNoticeClinical",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      disabled: 'disabled',
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 数据集合
      dataList: [],
      queryList: [],
      // 查询参数
      queryParams: {
        // 页码、页显示量
        pageNum: 1,
        pageSize: 10,
        // 日期查询控件值集合
        // 日期范围查询开始、结束
        queryDate: null,
        startDate: null,
        endDate: null,
        // 其他查询参数
        id: null,
        name: null,
        applicationDate: null,
      },/** queryParams 范围 */
    };/** return 范围 */
  }, /** data 范围 */
  created() {
    this.getList();
  }, /** created 范围 */
  computed: {
    number() {
      return number
    },
    /** 页面适应 */
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  }, /** computed 范围 */
  methods: {
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.queryParams.queryDate != null && this.queryParams.queryDate !== '') {
        this.queryParams.startDate = this.queryParams.queryDate[0];
        this.queryParams.endDate = this.queryParams.queryDate[1];
      }
      queryDataList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = null;
      this.queryParams.startDate = null;
      this.queryParams.endDate = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('EmploymentNoticeClinicalController/EmploymentNoticeClinical/export', {
        ...this.queryParams
      }, `待入职通知单_${dateTime}.xlsx`)//注意 {dateTime} 前面添加 美元 符号
    },
    jump(row) {
      window.open(row.filePath + row.id + row.suffix)
    },
  }, /** methods 范围 */
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
    },
  },/** watch 范围 */

};
</script>
