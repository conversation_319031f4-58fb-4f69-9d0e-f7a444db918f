<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="OA编号" prop="oaReimbursementNumber">
        <el-input
          v-model="queryParams.oaReimbursementNumber"
          placeholder="请输入OA编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="报销人" prop="reimbursementPerson">
        <el-input
          v-model="queryParams.reimbursementPerson"
          placeholder="请输入报销人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNo">
        <el-input
          v-model="queryParams.projectNo"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="当前环节" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入当前环节"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请主题" prop="applicationSubject">
        <el-input
          v-model="queryParams.applicationSubject"
          placeholder="请输入申请主题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请单单号" prop="applicationFormNumber">
        <el-input
          v-model="queryParams.applicationFormNumber"
          placeholder="请输入申请单单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请单名称" prop="applicationFormName">
        <el-input
          v-model="queryParams.applicationFormName"
          placeholder="请输入申请单名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:fd72ClinicalExpenseReimbursement:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fd72ClinicalExpenseReimbursementList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="ID" align="center" prop="id"/>-->
      <el-table-column label="OA编号" align="center" prop="oaReimbursementNumber" width="150"
                       :show-overflow-tooltip="true" fixed>
        <template slot-scope="scope">
        <span @click="handleClick(scope.row)" style="color: #00afff">
          {{ scope.row.oaReimbursementNumber }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="dateCreated" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCreated, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销类别" align="center" prop="reimbursementCategory" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="公司代码" align="center" prop="companyCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="公司名称" align="center" prop="companyName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人" align="center" prop="reimbursementPerson" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人工号" align="center" prop="reimbursementPersonNumber" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人工号" align="center" prop="applicantNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人职务" align="center" prop="applicantPosition" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="关联审批单" align="center" prop="relatedApprovalForm" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="业务发生期间" align="center" prop="businessPeriod" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="业务类型" align="center" prop="businessType" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="类型细分" align="center" prop="typeSegmentation" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="总裁办业务" align="center" prop="ceoBusiness" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="借款余额" align="center" prop="loanBalance" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="是否冲抵借款" align="center" prop="isWhetherLoan" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="合计金额" align="center" prop="totalAmount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="关联审批单金额" align="center" prop="relatedApprovalAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="差异金额" align="center" prop="differenceAmount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="业务描述" align="center" prop="serviceDescription" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号码" align="center" prop="invoiceNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="excludingTaxAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="专用发票税额" align="center" prop="specialInvoiceTaxAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="reimbursementAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNo" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP应付凭证状" align="center" prop="sapPayNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="voucherPushDate" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="150" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="FD06ID" align="center" prop="fd06ID"/>-->
<!--      <el-table-column label="FD06申请单编号" align="center" prop="fd06Number" width="150" :show-overflow-tooltip="true">-->
<!--        <template slot-scope="scope">-->
<!--        <span @click="handleClickFD06(scope.row)" style="color: #00afff">-->
<!--          {{ scope.row.fd06Number }}-->
<!--        </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="FD06申请单名称" align="center" prop="fd06Name" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD06招待类型" align="center" prop="fd06HospitalityType" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD06业务描述" align="center" prop="fd06ServiceDescription" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD06预计金额" align="center" prop="fd06EstimatedAmount" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      &lt;!&ndash;      <el-table-column label="CO-04ID" align="center" prop="co04ID"/>&ndash;&gt;-->
<!--      <el-table-column label="CO-04申请单编号" align="center" prop="co04Number" width="150" :show-overflow-tooltip="true">-->
<!--        <template slot-scope="scope">-->
<!--        <span @click="handleClickCO04(scope.row)" style="color: #00afff">-->
<!--          {{ scope.row.co04Number }}-->
<!--        </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="CO-04申请单名称" align="center" prop="co04Name" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="CO-04客户名称" align="center" prop="co04CustomerName" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="CO-04业务描述" align="center" prop="co04ServiceDescription" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="CO-04预计金额" align="center" prop="co04EstimatedAmount" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      &lt;!&ndash;      <el-table-column label="FD09ID" align="center" prop="fd09ID"/>&ndash;&gt;-->
<!--      <el-table-column label="FD09申请单编号" align="center" prop="fd09Number" width="150" :show-overflow-tooltip="true">-->
<!--        <template slot-scope="scope">-->
<!--        <span @click="handleClickFD09(scope.row)" style="color: #00afff">-->
<!--          {{ scope.row.fd09Number }}-->
<!--        </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="FD09申请单名称" align="center" prop="fd09Name" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09费用类型" align="center" prop="fd09ExpenseType" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09客户姓名" align="center" prop="fd09CustomerName" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09受试者编号" align="center" prop="fd09SubjectID" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09AE/SAE/访视编号" align="center" prop="fd09VisitNumber" width="200"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09是否公司垫付" align="center" prop="fd09AdvancePayment" width="200"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09是否经铨融支付" align="center" prop="fd09QuanrongPayment" width="200"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09是否经医院协议支付" align="center" prop="fd09ProtocolPayment" width="200"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09费用描述" align="center" prop="fd09CostDescription" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="FD09申请金额" align="center" prop="fd09ApplicationsAmount" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
      <el-table-column label="申请单编号" align="center" prop="applicationFormNumber" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
        <span @click="handleClickFD(scope.row)" style="color: #00afff;">
          {{ scope.row.applicationFormNumber }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="申请单名称" align="center" prop="applicationFormName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="招待/费用类型" align="center" prop="applicationFormType" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="描述" align="center" prop="applicationFormDescribe" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="applicationFormAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="客户姓名" align="center" prop="customerName" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="受试者编号" align="center" prop="subjectId" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="AE/SAE/访视编号" align="center" prop="visitNumber" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="是否公司垫付" align="center" prop="fd09AdvancePayment" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="是否经铨融支付" align="center" prop="fd09QuanrongPayment" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="是否经医院协议支付" align="center" prop="fd09ProtocolPayment" width="200"
                       :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {
  listFd72ClinicalExpenseReimbursement,
  getFd72ClinicalExpenseReimbursement,
  delFd72ClinicalExpenseReimbursement,
  addFd72ClinicalExpenseReimbursement,
  updateFd72ClinicalExpenseReimbursement
} from "@/api/clinical/fd72ClinicalExpenseReimbursement";

export default {
  name: "Fd72ClinicalExpenseReimbursement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // FD72临床费用报销表格数据
      fd72ClinicalExpenseReimbursementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除状态时间范围
      daterangeDateCreated: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oaReimbursementNumber: null,
        dateCreated: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        applicant: null,
        applicantNumber: null,
        applicantPosition: null,
        department: null,
        costCenterCode: null,
        costCenterName: null,
        relatedApprovalForm: null,
        businessPeriod: null,
        businessType: null,
        typeSegmentation: null,
        ceoBusiness: null,
        loanBalance: null,
        isWhetherLoan: null,
        totalAmount: null,
        relatedApprovalAmount: null,
        differenceAmount: null,
        serviceDescription: null,
        invoiceNumber: null,
        excludingTaxAmount: null,
        specialInvoiceTaxAmount: null,
        reimbursementAmount: null,
        currency: null,
        projectNo: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapPayNumber: null,
        voucherPushDate: null,
        applicationSubject: null,
        fd06ID: null,
        fd06Number: null,
        fd06Name: null,
        fd06HospitalityType: null,
        fd06ServiceDescription: null,
        fd06EstimatedAmount: null,
        co04ID: null,
        co04Number: null,
        co04Name: null,
        co04CustomerName: null,
        co04ServiceDescription: null,
        co04EstimatedAmount: null,
        fd09ID: null,
        fd09Number: null,
        fd09Name: null,
        fd09ExpenseType: null,
        fd09CustomerName: null,
        fd09SubjectID: null,
        fd09VisitNumber: null,
        fd09AdvancePayment: null,
        fd09QuanrongPayment: null,
        fd09ProtocolPayment: null,
        fd09CostDescription: null,
        fd09ApplicationsAmount: null,
        executionDate: null,
        deleteStatus: null,
        fdId: null,
        applicationFormId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //FD72单据链接
    handleClick(row) {
      const processNumber = row.oaReimbursementNumber;
      const id = row.fdId;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    //CO-04快递费单据链接
    handleClickCO04(row) {
      const processNumber = row.co04Number;
      const id = row.co04ID;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    //FD06业务招待费单据链接
    handleClickFD06(row) {
      const processNumber = row.fd06Number;
      const id = row.fd06ID;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    //FD09其他费用申请单据链接
    handleClickFD09(row) {
      const processNumber = row.fd09Number;
      const id = row.fd09ID;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    //FD09其他费用申请单据链接
    handleClickFD(row) {
      const id = row.applicationFormId;
        const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
    },
    /** 查询FD72临床费用报销列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listFd72ClinicalExpenseReimbursement(this.queryParams).then(response => {
        this.fd72ClinicalExpenseReimbursementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        oaReimbursementNumber: null,
        dateCreated: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        applicant: null,
        applicantNumber: null,
        applicantPosition: null,
        department: null,
        costCenterCode: null,
        costCenterName: null,
        relatedApprovalForm: null,
        businessPeriod: null,
        businessType: null,
        typeSegmentation: null,
        ceoBusiness: null,
        loanBalance: null,
        isWhetherLoan: null,
        totalAmount: null,
        relatedApprovalAmount: null,
        differenceAmount: null,
        serviceDescription: null,
        invoiceNumber: null,
        excludingTaxAmount: null,
        specialInvoiceTaxAmount: null,
        reimbursementAmount: null,
        currency: null,
        projectNo: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapPayNumber: null,
        voucherPushDate: null,
        applicationSubject: null,
        fd06ID: null,
        fd06Number: null,
        fd06Name: null,
        fd06HospitalityType: null,
        fd06ServiceDescription: null,
        fd06EstimatedAmount: null,
        co04ID: null,
        co04Number: null,
        co04Name: null,
        co04CustomerName: null,
        co04ServiceDescription: null,
        co04EstimatedAmount: null,
        fd09ID: null,
        fd09Number: null,
        fd09Name: null,
        fd09ExpenseType: null,
        fd09CustomerName: null,
        fd09SubjectID: null,
        fd09VisitNumber: null,
        fd09AdvancePayment: null,
        fd09QuanrongPayment: null,
        fd09ProtocolPayment: null,
        fd09CostDescription: null,
        fd09ApplicationsAmount: null,
        executionDate: null,
        deleteStatus: null,
        fdId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDateCreated = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加FD72临床费用报销";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFd72ClinicalExpenseReimbursement(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改FD72临床费用报销";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFd72ClinicalExpenseReimbursement(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFd72ClinicalExpenseReimbursement(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除FD72临床费用报销编号为"' + ids + '"的数据项？').then(function () {
        return delFd72ClinicalExpenseReimbursement(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/fd72ClinicalExpenseReimbursement/export', {
        ...this.queryParams
      }, `fd72ClinicalExpenseReimbursement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
