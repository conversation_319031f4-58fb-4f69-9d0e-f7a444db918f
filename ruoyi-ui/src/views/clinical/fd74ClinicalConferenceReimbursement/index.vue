<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="OA编号" prop="oaReimbursementNumber">
        <el-input
          v-model="queryParams.oaReimbursementNumber"
          placeholder="请输入OA编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="报销人" prop="reimbursementPerson">
        <el-input
          v-model="queryParams.reimbursementPerson"
          placeholder="请输入报销人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNo">
        <el-input
          v-model="queryParams.projectNo"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="当前环节" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入当前环节"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:fd74ClinicalConferenceReimbursement:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fd74ClinicalConferenceReimbursementList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="ID" align="center" prop="id"/>-->
      <el-table-column label="OA编号" align="center" prop="oaReimbursementNumber" width="150"
                       :show-overflow-tooltip="true" fixed>
        <template slot-scope="scope">
        <span @click="handleClick(scope.row)" style="color: #00afff">
          {{ scope.row.oaReimbursementNumber }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="dateCreated" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCreated, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="报销类别" align="center" prop="reimbursementCategory" width="150"-->
<!--                       :show-overflow-tooltip="true"/>-->
      <el-table-column label="公司代码" align="center" prop="companyCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="公司名称" align="center" prop="companyName" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="公司主体" align="center" prop="companyEntity" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="报销人" align="center" prop="reimbursementPerson" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="报销人工号" align="center" prop="reimbursementPersonNumber" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="申请人工号" align="center" prop="applicantNumber" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="申请人职务" align="center" prop="applicantPosition" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="关联会议申请" align="center" prop="relatedMeetingApplication" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="业务发生期间" align="center" prop="businessPeriod" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="开始日期" align="center" prop="startDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="endDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="开始日期" align="center" prop="startDate" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="结束日期" align="center" prop="endDate" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="会议名称" align="center" prop="meetingName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="会议类型" align="center" prop="meetingType" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="会议类别" align="center" prop="meetingCategory" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="会议形式" align="center" prop="meetingFormat" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="公司角色" align="center" prop="companyRole" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="面向对象" align="center" prop="objectOriented" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="省份" align="center" prop="province" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="城市" align="center" prop="city" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="覆盖人数" align="center" prop="peopleCovered" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="签到人数" align="center" prop="signNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="专家人数" align="center" prop="expertsNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="会议资料" align="center" prop="meetingMaterials" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="邀请函" align="center" prop="invitation" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="日程表" align="center" prop="schedule" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="签到表" align="center" prop="attendanceSheet" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="照片" align="center" prop="photo" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="支付方式" align="center" prop="paymentMethod" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="借款余额" align="center" prop="loanBalance" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="是否冲抵借款" align="center" prop="isWhetherLoan" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="合计金额" align="center" prop="totalAmount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="会议预算金额" align="center" prop="conferenceBudgetAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="差异金额" align="center" prop="differenceAmount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="差异说明" align="center" prop="differenceExplanation" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="业务描述" align="center" prop="serviceDescription" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="excludingTaxAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="专用发票税额" align="center" prop="specialInvoiceTaxAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="reimbursementAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNo" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP应付凭证状" align="center" prop="sapPayNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="voucherPushDate" width="150"
                       :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="FD20ID" align="center" prop="fd20ID" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="FD20申请单编号" align="center" prop="fd20Number" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
        <span @click="handleClickFD20(scope.row)" style="color: #00afff">
          {{ scope.row.fd20Number }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="FD20申请单名称" align="center" prop="fd20Name" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="FD20申请金额" align="center" prop="fd20Amount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="FD20业务描述" align="center" prop="fd20ServiceDescription" width="150"
                       :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listFd74ClinicalConferenceReimbursement,
  getFd74ClinicalConferenceReimbursement,
  delFd74ClinicalConferenceReimbursement,
  addFd74ClinicalConferenceReimbursement,
  updateFd74ClinicalConferenceReimbursement
} from "@/api/clinical/fd74ClinicalConferenceReimbursement";

export default {
  name: "Fd74ClinicalConferenceReimbursement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // FD74临床会议报销 表格数据
      fd74ClinicalConferenceReimbursementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除状态时间范围
      daterangeDateCreated: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oaReimbursementNumber: null,
        dateCreated: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        companyEntity: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        applicant: null,
        applicantNumber: null,
        applicantPosition: null,
        department: null,
        costCenterCode: null,
        costCenterName: null,
        relatedMeetingApplication: null,
        businessPeriod: null,
        startDate: null,
        endDate: null,
        meetingName: null,
        meetingType: null,
        meetingCategory: null,
        meetingFormat: null,
        companyRole: null,
        objectOriented: null,
        province: null,
        city: null,
        peopleCovered: null,
        signNumber: null,
        expertsNumber: null,
        meetingMaterials: null,
        invitation: null,
        schedule: null,
        attendanceSheet: null,
        photo: null,
        paymentMethod: null,
        loanBalance: null,
        isWhetherLoan: null,
        totalAmount: null,
        conferenceBudgetAmount: null,
        differenceAmount: null,
        differenceExplanation: null,
        serviceDescription: null,
        excludingTaxAmount: null,
        specialInvoiceTaxAmount: null,
        reimbursementAmount: null,
        currency: null,
        projectNo: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapPayNumber: null,
        voucherPushDate: null,
        fd20ID: null,
        fd20Number: null,
        fd20Name: null,
        fd20Amount: null,
        fd20ServiceDescription: null,
        executionDate: null,
        deleteStatus: null,
        fdId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //FD74单据链接
    handleClick(row) {
      const processNumber = row.oaReimbursementNumber;
      const id = row.fdId;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    //FD20单据链接
    handleClickFD20(row) {
      const processNumber = row.fd20Number;
      const id = row.fd20ID;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    /** 查询FD74临床会议报销列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listFd74ClinicalConferenceReimbursement(this.queryParams).then(response => {
        this.fd74ClinicalConferenceReimbursementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        oaReimbursementNumber: null,
        dateCreated: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        companyEntity: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        applicant: null,
        applicantNumber: null,
        applicantPosition: null,
        department: null,
        costCenterCode: null,
        costCenterName: null,
        relatedMeetingApplication: null,
        businessPeriod: null,
        startDate: null,
        endDate: null,
        meetingName: null,
        meetingType: null,
        meetingCategory: null,
        meetingFormat: null,
        companyRole: null,
        objectOriented: null,
        province: null,
        city: null,
        peopleCovered: null,
        signNumber: null,
        expertsNumber: null,
        meetingMaterials: null,
        invitation: null,
        schedule: null,
        attendanceSheet: null,
        photo: null,
        paymentMethod: null,
        loanBalance: null,
        isWhetherLoan: null,
        totalAmount: null,
        conferenceBudgetAmount: null,
        differenceAmount: null,
        differenceExplanation: null,
        serviceDescription: null,
        excludingTaxAmount: null,
        specialInvoiceTaxAmount: null,
        reimbursementAmount: null,
        currency: null,
        projectNo: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapPayNumber: null,
        voucherPushDate: null,
        fd20ID: null,
        fd20Number: null,
        fd20Name: null,
        fd20Amount: null,
        fd20ServiceDescription: null,
        executionDate: null,
        deleteStatus: null,
        fdId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDateCreated = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加FD74临床会议报销";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFd74ClinicalConferenceReimbursement(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改FD74临床会议报销";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFd74ClinicalConferenceReimbursement(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFd74ClinicalConferenceReimbursement(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除FD74临床会议报销编号为"' + ids + '" 的数据项？').then(function () {
          return delFd74ClinicalConferenceReimbursement(ids);
        }
      ).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/fd74ClinicalConferenceReimbursement/export', {
        ...this.queryParams
      }, `fd74ClinicalConferenceReimbursement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
