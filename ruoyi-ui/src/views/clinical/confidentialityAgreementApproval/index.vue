<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="200px">

      <el-form-item label="单号" prop="oddNumbers">
        <el-input
          v-model="queryParams.oddNumbers"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangeApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="中心名称" prop="centerName">
        <el-input
          v-model="queryParams.centerName"
          placeholder="请输入中心名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNo">
        <el-input
          v-model="queryParams.projectNo"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="当前处理人" prop="currentProcessor">
        <el-input
          v-model="queryParams.currentProcessor"
          placeholder="请输入当前处理人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item label="中心签署人姓名" prop="signatoryName">
        <el-input
          v-model="queryParams.signatoryName"
          placeholder="请输入中心签署人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="当前环节" prop="documentStatus">
        <el-input
          v-model="queryParams.documentStatus"
          placeholder="请输入当前环节"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="文档状态" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入文档状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="协议生效日期" prop="effectiveDate">
        <el-input
          v-model="queryParams.effectiveDate"
          placeholder="请输入协议生效日期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="协议实际签署日期">
        <el-date-picker
          v-model="daterangeSignDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="中心编号" prop="centerNumber">
        <el-input
          v-model="queryParams.centerNumber"
          placeholder="请输入中心编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="主流程归档日期">
        <el-date-picker
          v-model="daterangeFilingDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="保密协议归档份数" prop="fileNumber">
        <el-input
          v-model="queryParams.fileNumber"
          placeholder="请输入保密协议归档份数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="保密协议原件接收人" prop="recipient">
        <el-input
          v-model="queryParams.recipient"
          placeholder="请输入保密协议原件接收人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="子流程原件接收人日期">
        <el-date-picker
          v-model="daterangeRecipientDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:confidentialityAgreementApproval:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="confidentialityAgreementApprovalList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="单号" align="center" prop="oddNumbers" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <a @click="handleClick(scope.row)" style="color: #00afff">
            {{ scope.row.oddNumbers }}
          </a>
        </template>
      </el-table-column>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目号" align="center" prop="projectNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="签订公司" align="center" prop="company" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心签署人姓名" align="center" prop="signatoryName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="协议生效日期" align="center" prop="effectiveDate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="协议实际签署日期" align="center" prop="signDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.signDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效期(年）" align="center" prop="validityPeriod" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="保密协议归档份数" align="center" prop="fileNumber" width="200" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="子流程原件份数" align="center" prop="originalCopy" width="200" :show-overflow-tooltip="true"/>-->
      <el-table-column label="主流程归档日期" align="center" prop="filingDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.filingDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="保密协议原件接收人" align="center" prop="recipient" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="子流程原件接收人日期" align="center" prop="recipientDate" width="200"
                       :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {
  listConfidentialityAgreementApproval
} from "@/api/clinical/confidentialityAgreementApproval";

export default {
  name: "ConfidentialityAgreementApproval",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 保密协议审批表格数据
      confidentialityAgreementApprovalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 申请日期范围
      daterangeApplicationDate: [],
      // 协议实际签署日期时间范围
      daterangeSignDate: [],
      // 主流程归档日期时间范围
      daterangeFilingDate: [],
      // 子流程原件接收人日期时间范围
      daterangeRecipientDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fdId: null,
        oddNumbers: null,
        applicationDate: null,
        projectNo: null,
        company: null,
        centerName: null,
        signatoryName: null,
        effectiveDate: null,
        applicant: null,
        signDate: null,
        validityPeriod: null,
        centerNumber: null,
        originalCopy: null,
        filingDate: null,
        currentProcessor: null,
        documentStatus: null,
        currentSession: null,
        recipient: null,
        recipientDate: null,
        fileNumber: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick(row) {
      // const processNumber = row.processNumber;
      const id = row.fdId;
      // if (processNumber != null && processNumber != '' && id != null && id != '') {
      //   if (processNumber.includes('TY005')) {
      const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
      //     window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      //   } else {
      // const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
      window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      // }
      // }
    },
    /** 查询保密协议审批列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
        this.queryParams.params["beginApplicationDate"] = this.daterangeApplicationDate[0];
        this.queryParams.params["endApplicationDate"] = this.daterangeApplicationDate[1];
      }
      if (null != this.daterangeSignDate && '' != this.daterangeSignDate) {
        this.queryParams.params["beginSignDate"] = this.daterangeSignDate[0];
        this.queryParams.params["endSignDate"] = this.daterangeSignDate[1];
      }
      if (null != this.daterangeFilingDate && '' != this.daterangeFilingDate) {
        this.queryParams.params["beginFilingDate"] = this.daterangeFilingDate[0];
        this.queryParams.params["endFilingDate"] = this.daterangeFilingDate[1];
      }
      if (null != this.daterangeRecipientDate && '' != this.daterangeRecipientDate) {
        this.queryParams.params["beginRecipientDate"] = this.daterangeRecipientDate[0];
        this.queryParams.params["endRecipientDate"] = this.daterangeRecipientDate[1];
      }
      listConfidentialityAgreementApproval(this.queryParams).then(response => {
        this.confidentialityAgreementApprovalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        fdId: null,
        oddNumbers: null,
        applicationDate: null,
        projectNo: null,
        company: null,
        centerName: null,
        signatoryName: null,
        effectiveDate: null,
        applicant: null,
        signDate: null,
        validityPeriod: null,
        centerNumber: null,
        originalCopy: null,
        filingDate: null,
        currentProcessor: null,
        documentStatus: null,
        currentSession: null,
        recipient: null,
        recipientDate: null,
        fileNumber: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeApplicationDate = [];
      this.daterangeSignDate = [];
      this.daterangeFilingDate = [];
      this.daterangeRecipientDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fdId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/confidentialityAgreementApproval/export', {
        ...this.queryParams
      }, `confidentialityAgreementApproval_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
