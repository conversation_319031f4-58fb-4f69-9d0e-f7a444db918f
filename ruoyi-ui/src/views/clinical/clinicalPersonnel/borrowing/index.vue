<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="queryParams.queryDate"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="姓名" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="单号" prop="oddNumber">
        <el-input
          v-model="queryParams.oddNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="中心编号" prop="centerNumber">
        <el-input
          v-model="queryParams.centerNumber"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="中心名称" prop="centerName">
        <el-input
          v-model="queryParams.centerName"
          placeholder="请输入..."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ClinicalPersonnel:borrowing:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" border
              :max-height="tableMaxHeight">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="100" fixed>
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单号" align="center" prop="oddNumber" width="160" :show-overflow-tooltip="true" fixed>
        <template v-slot="scope">
          <span @click="jump(scope.row)" style="color: #3967FF;cursor:pointer;">{{ scope.row.oddNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="applicationName" width="100" :show-overflow-tooltip="true"
                       fixed/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="250" :show-overflow-tooltip="true"/>
      <el-table-column label="岗位" align="center" prop="position" width="80" :show-overflow-tooltip="true"/>
      <el-table-column label="PM审核人" align="center" prop="pmName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="事务部专员" align="center" prop="swName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="160" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNumber" width="160" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="文件名称" align="center" prop="fileName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="版本号/合同编号" align="center" prop="versionNumber" width="250"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="版本日期" align="center" prop="versionDate" width="160" :show-overflow-tooltip="true"/>
      <el-table-column label="文件格式" align="center" prop="fileType" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="借阅原因" align="center" prop="borrowingReason" width="250"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="借阅提供文件日期" align="center" prop="jyDate" width="170">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.jyDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="借阅提供纸质文件日期" align="center" prop="jyzzDate" width="170">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.jyzzDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="纸质归还日期" align="center" prop="ghDate" width="170">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.ghDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {queryClinicalBorrowingList} from "@/api/clinical/ClinicalBorrowing.js";

export default {
  name: "ClinicalBorrowing",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      // 数据集合
      dataList: [],
      // 查询参数
      queryParams: {
        // 页码、页显示量
        pageNum: 1,
        pageSize: 10,
        // 日期查询控件值集合
        queryDate: null,
        // 日期范围查询开始、结束
        startDate: null,
        endDate: null,
        // 其他查询参数
        id: null,
        name: null,
        applicationDate: null,
      },/** queryParams 范围 */
    };/** return 范围 */
  }, /** data 范围 */
  created() {
    this.getList();
  }, /** created 范围 */
  computed: {
    /** 页面适应 */
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  }, /** computed 范围 */
  methods: {
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.queryParams.queryDate != null && this.queryParams.queryDate !== '') {
        this.queryParams.startDate = this.queryParams.queryDate[0];
        this.queryParams.endDate = this.queryParams.queryDate[1];
      }
      queryClinicalBorrowingList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = null;
      this.queryParams.startDate = null;
      this.queryParams.endDate = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('ClinicalPersonnel/borrowing/export', {
        ...this.queryParams
      }, `导出_${dateTime}.xlsx`)
    },
    jump(row) {
      window.open("http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=" + row.id + "&s_css=default")
    },
  }, /** methods 范围 */
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
    }
  },/** watch 范围 */

};
</script>
