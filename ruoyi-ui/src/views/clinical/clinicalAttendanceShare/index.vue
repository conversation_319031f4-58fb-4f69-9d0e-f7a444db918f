<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作日期" label-width="90px">
        <el-date-picker
          ref="contractDatePicker"
          v-model="queryParams.queryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:comparison:clinicalAttendanceShare:export']"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :max-height="tableMaxHeight" :data="dataList"
              @selection-change="handleSelectionChange">
      <!--      <el-table-column type="selection" width="55" align="center"/>-->
      <el-table-column label="公司代码" align="center" prop="companyCode" key="companyCode" v-if="columns[0].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="公司" align="center" prop="company" key="company" v-if="columns[1].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="部门" align="center" prop="endDepartment" key="endDepartment" v-if="columns[2].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="成本中心" align="center" prop="costCenter" key="costCenter" v-if="columns[3].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="项目订单号" align="center" prop="projectOrderNo" key="projectOrderNo"
                       v-if="columns[4].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="项目号" align="center" prop="projectNumber" key="projectNumber" v-if="columns[5].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="报工工时" align="center" prop="workHour" key="workHour"
                       v-if="columns[6].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工作日期" align="center" prop="workDate" key="workDate"
                       v-if="columns[7].visible"
                       :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {queryClinicalAttendanceShareList} from "@/api/clinical/clinical";

export default {
  name: "Clinical",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        queryDate: '',
        department: '',
        company: '',
        name: '',
        startDate: null,
        endDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 0, label: `公司代码`, visible: true},
        {key: 1, label: `公司`, visible: true},
        {key: 2, label: `部门`, visible: true},
        {key: 3, label: `成本中心`, visible: true},
        {key: 4, label: `项目订单号`, visible: true},
        {key: 5, label: `项目号`, visible: true},
        {key: 6, label: `报工工时`, visible: true},
        {key: 7, label: `工作日期`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /**  */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.queryParams.queryDate != null) {
        this.queryParams.startDate = this.queryParams.queryDate[0];
        this.queryParams.endDate = this.queryParams.queryDate[1];
      }
      queryClinicalAttendanceShareList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('clinical/comparison/clinicalAttendanceShare/export', {
        ...this.queryParams
      }, `工时汇总表2_${dateTime}.xlsx`)
    },
    watch: {
      // 监听日期清理后数据为null进行处理否则会报错
      'queryParams.queryDate'(newVal) {
        if (newVal == null) {
          this.queryParams.queryDate = ''
        }
      }
    }

  }


};
</script>
