<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="OA编号" prop="oaReimbursementNumber">
        <el-input
          v-model="queryParams.oaReimbursementNumber"
          placeholder="请输入OA编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="报销人" prop="reimbursementPerson">
        <el-input
          v-model="queryParams.reimbursementPerson"
          placeholder="请输入报销人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNo">
        <el-input
          v-model="queryParams.projectNo"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="当前环节" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入当前环节"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请主题" prop="applicationSubject">
        <el-input
          v-model="queryParams.applicationSubject"
          placeholder="请输入申请主题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:fd73ClinicalTravelReimbursement:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fd73ClinicalTravelReimbursementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="ID" align="center" prop="id" />-->
      <el-table-column label="OA编号" align="center" prop="oaReimbursementNumber" width="150"
                       :show-overflow-tooltip="true" fixed>
        <template slot-scope="scope">
        <span @click="handleClick(scope.row)" style="color: #00afff">
          {{ scope.row.oaReimbursementNumber }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="dateCreated" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCreated, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销类别" align="center" prop="reimbursementCategory" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="公司代码" align="center" prop="companyCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="公司名称" align="center" prop="companyName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人" align="center" prop="reimbursementPerson" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="报销人工号" align="center" prop="reimbursementPersonNumber" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人工号" align="center" prop="applicantNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人职务" align="center" prop="applicantPosition" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心代码" align="center" prop="costCenterCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心名称" align="center" prop="costCenterName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="关联审批单" align="center" prop="relatedApprovalForm" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="业务发生期间" align="center" prop="businessPeriod" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="出发日期" align="center" prop="departureDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.departureDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="返回日期" align="center" prop="returnDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.returnDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="出发日期" align="center" prop="departureDate" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="返回日期" align="center" prop="returnDate" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="出差天数" align="center" prop="businessDay" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="同行人员" align="center" prop="colleagues" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="借款余额" align="center" prop="loanBalance" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="是否冲抵借款" align="center" prop="isWhetherLoan" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="合计金额" align="center" prop="totalAmount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="费用科目代码" align="center" prop="expenseAccountCode" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="费用科目名称" align="center" prop="expenseAccountName" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="业务描述" align="center" prop="serviceDescription" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="发票号码" align="center" prop="invoiceNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="不含税金额" align="center" prop="excludingTaxAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="专用发票税额" align="center" prop="specialInvoiceTaxAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="reimbursementAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="币别" align="center" prop="currency" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNo" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP应付凭证状" align="center" prop="sapPayNumber" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="凭证推送日期" align="center" prop="voucherPushDate" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请主题" align="center" prop="applicationSubject" width="150" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="FD13ID" align="center" prop="fd13ID" />-->
      <el-table-column label="申请单编号" align="center" prop="fd13Number" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
        <span @click="handleClickFD13(scope.row)" style="color: #00afff">
          {{ scope.row.fd13Number }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="申请单名称" align="center" prop="fd13Name" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="申请单金额" align="center" prop="fd13Amount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="外出/差旅地点" align="center" prop="fd13Location" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请业务描述" align="center" prop="fd13ServiceDescription" width="150"
                       :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {
  listFd73ClinicalTravelReimbursement,
  getFd73ClinicalTravelReimbursement,
  delFd73ClinicalTravelReimbursement,
  addFd73ClinicalTravelReimbursement,
  updateFd73ClinicalTravelReimbursement
} from "@/api/clinical/fd73ClinicalTravelReimbursement";

export default {
  name: "Fd73ClinicalTravelReimbursement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // FD73临床差旅报销表格数据
      fd73ClinicalTravelReimbursementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除状态时间范围
      daterangeDateCreated: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oaReimbursementNumber: null,
        dateCreated: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        applicant: null,
        applicantNumber: null,
        applicantPosition: null,
        department: null,
        costCenterCode: null,
        costCenterName: null,
        relatedApprovalForm: null,
        businessPeriod: null,
        departureDate: null,
        returnDate: null,
        businessDay: null,
        colleagues: null,
        loanBalance: null,
        isWhetherLoan: null,
        totalAmount: null,
        expenseAccountCode: null,
        expenseAccountName: null,
        serviceDescription: null,
        invoiceNumber: null,
        excludingTaxAmount: null,
        specialInvoiceTaxAmount: null,
        reimbursementAmount: null,
        currency: null,
        projectNo: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapPayNumber: null,
        voucherPushDate: null,
        applicationSubject: null,
        fd13ID: null,
        fd13Number: null,
        fd13Name: null,
        fd13Amount: null,
        fd13Location: null,
        fd13ServiceDescription: null,
        executionDate: null,
        deleteStatus: null,
        fdId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //FD73单据链接
    handleClick(row) {
      const processNumber = row.oaReimbursementNumber;
      const id = row.fdId;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    //FD13和外出单单据链接
    handleClickFD13(row) {
      const processNumber = row.fd13Number;
      const id = row.fd13ID;
      if (processNumber != null && processNumber != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    /** 查询FD73临床差旅报销列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listFd73ClinicalTravelReimbursement(this.queryParams).then(response => {
        this.fd73ClinicalTravelReimbursementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        oaReimbursementNumber: null,
        dateCreated: null,
        reimbursementCategory: null,
        companyCode: null,
        companyName: null,
        reimbursementPerson: null,
        reimbursementPersonNumber: null,
        applicant: null,
        applicantNumber: null,
        applicantPosition: null,
        department: null,
        costCenterCode: null,
        costCenterName: null,
        relatedApprovalForm: null,
        businessPeriod: null,
        departureDate: null,
        returnDate: null,
        businessDay: null,
        colleagues: null,
        loanBalance: null,
        isWhetherLoan: null,
        totalAmount: null,
        expenseAccountCode: null,
        expenseAccountName: null,
        serviceDescription: null,
        invoiceNumber: null,
        excludingTaxAmount: null,
        specialInvoiceTaxAmount: null,
        reimbursementAmount: null,
        currency: null,
        projectNo: null,
        centerName: null,
        documentStatus: null,
        currentSession: null,
        sapPayNumber: null,
        voucherPushDate: null,
        applicationSubject: null,
        fd13ID: null,
        fd13Number: null,
        fd13Name: null,
        fd13Amount: null,
        fd13Location: null,
        fd13ServiceDescription: null,
        executionDate: null,
        deleteStatus: null,
        fdId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDateCreated = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加FD73临床差旅报销";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFd73ClinicalTravelReimbursement(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改FD73临床差旅报销";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFd73ClinicalTravelReimbursement(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFd73ClinicalTravelReimbursement(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除FD73临床差旅报销编号为"' + ids + '"的数据项？').then(function () {
        return delFd73ClinicalTravelReimbursement(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/fd73ClinicalTravelReimbursement/export', {
        ...this.queryParams
      }, `fd73ClinicalTravelReimbursement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
