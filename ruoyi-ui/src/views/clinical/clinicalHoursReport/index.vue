<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangeApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="单号" prop="oddNumber">
        <el-input
          v-model="queryParams.oddNumber"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入所属部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="单据状态" prop="documentStatus">
        <el-input
          v-model="queryParams.documentStatus"
          placeholder="请输入单据状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="工作日期">
        <el-date-picker
          v-model="daterangeWorkDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:clinicalHoursReport:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="clinicalHoursReportList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="表单类型" align="center" prop="formType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单号" align="center" prop="oddNumber" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <a @click="handleClick(scope.row)" style="color: #00afff">
            {{ scope.row.oddNumber }}
          </a>
        </template>
        </el-table-column>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工号" align="center" prop="jobNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所属部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="职务" align="center" prop="post" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="工作日期" align="center" prop="workDate" width="200" :show-overflow-tooltip="true">
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.workDate, '{y}-{m}-{d}') }}</span>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="工时" align="center" prop="workHours" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="工作内容" align="center" prop="workContent" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="流程状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listClinicalHoursReport } from "@/api/clinical/clinicalHoursReport";

export default {
  name: "ClinicalHoursReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床工时汇报明细表格数据
      clinicalHoursReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 流程状态时间范围
      daterangeApplicationDate: [],
      // 流程状态时间范围
      daterangeWorkDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        formType: null,
        oddNumber: null,
        applicationDate: null,
        jobNumber: null,
        applicant: null,
        department: null,
        post: null,
        workDate: null,
        projectNumber: null,
        workHours: null,
        workContent: null,
        documentStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.defaultDate();
    this.getList();
  },
  methods: {
    defaultDate() {
      let today = new Date();
      let year = today.getFullYear();
      let month = today.getMonth() + 1; // JavaScript中月份是从0开始的，所以需要+1
      // 获取当前月份第一天
      let firstDayOfMonth = new Date(year, month - 1, 1);
      // 确保月份和日期前面有零
      let formattedFirstDay = `${year}-${(month < 10 ? '0' : '') + month}-01`;
      // 获取当前月份的最后一天
      // 通过创建下个月的第一天，然后减去一天来获取本月最后一天
      let lastDayOfMonth = new Date(year, month, 0);
      let formattedLastDay = `${year}-${(month < 10 ? '0' : '') + month}-${lastDayOfMonth.getDate()}`;
      this.daterangeApplicationDate = [formattedFirstDay, formattedLastDay];
    },
    /** 查询临床工时汇报明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
        this.queryParams.params["beginApplicationDate"] = this.daterangeApplicationDate[0];
        this.queryParams.params["endApplicationDate"] = this.daterangeApplicationDate[1];
      }
      if (null != this.daterangeWorkDate && '' != this.daterangeWorkDate) {
        this.queryParams.params["beginWorkDate"] = this.daterangeWorkDate[0];
        this.queryParams.params["endWorkDate"] = this.daterangeWorkDate[1];
      }
      listClinicalHoursReport(this.queryParams).then(response => {
        this.clinicalHoursReportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        formType: null,
        oddNumber: null,
        applicationDate: null,
        jobNumber: null,
        applicant: null,
        department: null,
        post: null,
        workDate: null,
        projectNumber: null,
        workHours: null,
        workContent: null,
        documentStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeApplicationDate = [];
      this.daterangeWorkDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.formType)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/clinicalHoursReport/export', {
        ...this.queryParams
      }, `clinicalHoursReport_${new Date().getTime()}.xlsx`)
    },
    handleClick(row) {
      // const processNumber = row.processNumber;
      const id = row.id;
      // if (processNumber != null && processNumber != '' && id != null && id != '') {
      //   if (processNumber.includes('TY005')) {
          const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
      //     window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      //   } else {
      // const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
      window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      // }
      // }
    },
  }
};
</script>
