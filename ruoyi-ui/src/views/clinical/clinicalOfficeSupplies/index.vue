<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请单号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入申请单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间">
        <el-date-picker
          v-model="daterangeEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="当前环节" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入当前环节"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="处理人" prop="currentProcessor">
        <el-input
          v-model="queryParams.currentProcessor"
          placeholder="请输入处理人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="文档状态" prop="documentStatus">
        <el-input
          v-model="queryParams.documentStatus"
          placeholder="请输入文档状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="中心名称" prop="centerName">
        <el-input
          v-model="queryParams.centerName"
          placeholder="请输入中心名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:clinicalOfficeSupplies:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="clinicalOfficeSuppliesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="流程名称" align="center" prop="processName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请单号" align="center" prop="requestNo" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <a @click="handleClick(scope.row)" style="color: #00afff">
            {{ scope.row.requestNo }}
          </a>
        </template>
      </el-table-column>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科室" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="主要研究者" align="center" prop="principalIinvestigator" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="creationTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请理由" align="center" prop="applicationReason" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="名称" align="center" prop="name" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="品牌" align="center" prop="brand" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="规格" align="center" prop="specifications" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="number" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单位" align="center" prop="unit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="预算单价" align="center" prop="budgetUnitPrice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用" align="center" prop="cost" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="接收人" align="center" prop="recipient" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所在城市" align="center" prop="city" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="详细收货地址" align="center" prop="detailedShippingAddress" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="订单状态" align="center" prop="null1" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单号信息" align="center" prop="null2" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remark" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期望交付时间" align="center" prop="expectedDeliveryTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedDeliveryTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>



      <!--
      <el-table-column label="申请单号" align="center" prop="requestNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="creationTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="流程名称" align="center" prop="processName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请理由" align="center" prop="applicationReason" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="名称" align="center" prop="name" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="品牌" align="center" prop="brand" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="规格" align="center" prop="specifications" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单位" align="center" prop="unit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="number" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期望交付时间" align="center" prop="expectedDeliveryTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedDeliveryTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="接收人" align="center" prop="recipient" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="联系电话" align="center" prop="telephone" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所在城市" align="center" prop="city" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="详细收货地址" align="center" prop="detailedShippingAddress" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="结束时间" align="center" prop="endTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200" :show-overflow-tooltip="true"/>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listClinicalOfficeSupplies} from "@/api/clinical/clinicalOfficeSupplies";

export default {
  name: "ClinicalOfficeSupplies",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床办公用品表格数据
      clinicalOfficeSuppliesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前处理人时间范围
      daterangeCreationTime: [],
      // 当前处理人时间范围
      daterangeExpectedDeliveryTime: [],
      // 当前处理人时间范围
      daterangeEndTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        creationTime: null,
        processName: null,
        projectNumber: null,
        centerName: null,
        applicant: null,
        applicationReason: null,
        name: null,
        brand: null,
        specifications: null,
        unit: null,
        number: null,
        expectedDeliveryTime: null,
        recipient: null,
        telephone: null,
        city: null,
        detailedShippingAddress: null,
        endTime: null,
        documentStatus: null,
        currentSession: null,
        currentProcessor: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick(row) {
      // const processNumber = row.processNumber;
      const id = row.id;
      // if (processNumber != null && processNumber != '' && id != null && id != '') {
      //   if (processNumber.includes('TY005')) {
          const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
      //     window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      //   } else {
      // const url = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=` + id;
      window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      // }
      // }
    },
    /** 查询临床办公用品列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreationTime && '' != this.daterangeCreationTime) {
        this.queryParams.params["beginCreationTime"] = this.daterangeCreationTime[0];
        this.queryParams.params["endCreationTime"] = this.daterangeCreationTime[1];
      }
      if (null != this.daterangeExpectedDeliveryTime && '' != this.daterangeExpectedDeliveryTime) {
        this.queryParams.params["beginExpectedDeliveryTime"] = this.daterangeExpectedDeliveryTime[0];
        this.queryParams.params["endExpectedDeliveryTime"] = this.daterangeExpectedDeliveryTime[1];
      }
      if (null != this.daterangeEndTime && '' != this.daterangeEndTime) {
        this.queryParams.params["beginEndTime"] = this.daterangeEndTime[0];
        this.queryParams.params["endEndTime"] = this.daterangeEndTime[1];
      }
      listClinicalOfficeSupplies(this.queryParams).then(response => {
        this.clinicalOfficeSuppliesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        requestNo: null,
        creationTime: null,
        processName: null,
        projectNumber: null,
        centerName: null,
        applicant: null,
        applicationReason: null,
        name: null,
        brand: null,
        specifications: null,
        unit: null,
        number: null,
        expectedDeliveryTime: null,
        recipient: null,
        telephone: null,
        city: null,
        detailedShippingAddress: null,
        endTime: null,
        documentStatus: null,
        currentSession: null,
        currentProcessor: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreationTime = [];
      this.daterangeExpectedDeliveryTime = [];
      this.daterangeEndTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.requestNo)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/clinicalOfficeSupplies/export', {
        ...this.queryParams
      }, `clinicalOfficeSupplies_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
