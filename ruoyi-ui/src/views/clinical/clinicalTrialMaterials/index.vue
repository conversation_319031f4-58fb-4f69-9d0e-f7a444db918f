<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="96px">
      <el-form-item label="申请单号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入申请单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间">
        <el-date-picker
          v-model="daterangeEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="当前环节" prop="currentSession">
        <el-input
          v-model="queryParams.currentSession"
          placeholder="请输入当前环节"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目号" prop="projectNumber">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="处理人" prop="currentProcessor">
        <el-input
          v-model="queryParams.currentProcessor"
          placeholder="请输入处理人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="文档状态" prop="documentStatus">
        <el-input
          v-model="queryParams.documentStatus"
          placeholder="请输入文档状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="中心名称" prop="centerName">
        <el-input
          v-model="queryParams.centerName"
          placeholder="请输入中心名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="物资名称/类别" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物资名称/类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['clinical:clinicalTrialMaterials:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="clinicalTrialMaterialsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="流程名称" align="center" prop="processName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请单号" align="center" prop="requestNo" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
        <span @click="handleClick(scope.row)" style="color: #00afff">
          {{ scope.row.requestNo }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心状态" align="center" prop="null1" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="科室" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="主要研究者" align="center" prop="principalIinvestigator" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="creationTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请理由" align="center" prop="applicationReason" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="物资I级分类" align="center" prop="null2" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="物资II级分类" align="center" prop="null3" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="物资名称/类别" align="center" prop="materialName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="品牌" align="center" prop="productCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="型号" align="center" prop="model" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="规格" align="center" prop="productName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="设备编号/批号" align="center" prop="null4" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="生产日期/校准日期" align="center" prop="null5" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.null5, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="批号" align="center" prop="null6" width="200" :show-overflow-tooltip="true"/>-->
      <el-table-column label="数量" align="center" prop="number" width="200" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="校准/非校准" align="center" prop="null7" width="200" :show-overflow-tooltip="true"/>-->
      <el-table-column label="处理人员" align="center" prop="null8" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供货方式" align="center" prop="supplyMethod" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="处理节点" align="center" prop="null9" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="接收人" align="center" prop="recipient" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所在城市" align="center" prop="city" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="详细收货地址" align="center" prop="detailedShippingAddress" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="邮寄日期" align="center" prop="null10" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="运单号" align="center" prop="waybillNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="结束时间" align="center" prop="endTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交接单是否归档（网盘）" align="center" prop="null11" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remark" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期望交付时间" align="center" prop="expectedDeliveryTime" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>


      <!--
      <el-table-column label="申请单号" align="center" prop="requestNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="creationTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="流程名称" align="center" prop="processName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心编号" align="center" prop="centerNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="中心名称" align="center" prop="centerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="applicant" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请理由" align="center" prop="applicationReason" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="供货方式" align="center" prop="supplyMethod" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="物资类别" align="center" prop="materialCategory" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="物资名称/类别" align="center" prop="materialName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="品牌/商品编码" align="center" prop="productCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="规格型号/商品名称" align="center" prop="productName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="number" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单位" align="center" prop="unit" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期望交付时间" align="center" prop="expectedDeliveryTime" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="接收人" align="center" prop="recipient" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="所在城市" align="center" prop="city" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="详细收货地址" align="center" prop="detailedShippingAddress" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="结束时间" align="center" prop="endTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前环节" align="center" prop="currentSession" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" width="200"
                       :show-overflow-tooltip="true"/>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listClinicalTrialMaterials} from "@/api/clinical/clinicalTrialMaterials";

export default {
  name: "ClinicalTrialMaterials",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床试验物资表格数据
      clinicalTrialMaterialsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前处理人时间范围
      daterangeCreationTime: [],
      // 当前处理人时间范围
      daterangeEndTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        creationTime: null,
        processName: null,
        projectNumber: null,
        centerNo: null,
        centerName: null,
        applicant: null,
        applicationReason: null,
        supplyMethod: null,
        materialCategory: null,
        materialName: null,
        productCode: null,
        productName: null,
        number: null,
        unit: null,
        expectedDeliveryTime: null,
        recipient: null,
        city: null,
        detailedShippingAddress: null,
        endTime: null,
        documentStatus: null,
        currentSession: null,
        currentProcessor: null,
        model: null,
        fDId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick(row) {
      const requestNo = row.requestNo;
      const id = row.fDId;
      if (requestNo != null && requestNo != '' && id != null && id != '') {
        const url = `http://oa.akesobio.com:9926/km/review/km_review_main/kmReviewMain.do?method=view&fdId=` + id;
        window.open(url, '_blank'); // 在新窗口或标签页中打开链接
      }
    },
    /** 查询临床试验物资列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreationTime && '' != this.daterangeCreationTime) {
        this.queryParams.params["beginCreationTime"] = this.daterangeCreationTime[0];
        this.queryParams.params["endCreationTime"] = this.daterangeCreationTime[1];
      }
      if (null != this.daterangeEndTime && '' != this.daterangeEndTime) {
        this.queryParams.params["beginEndTime"] = this.daterangeEndTime[0];
        this.queryParams.params["endEndTime"] = this.daterangeEndTime[1];
      }
      listClinicalTrialMaterials(this.queryParams).then(response => {
        this.clinicalTrialMaterialsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        requestNo: null,
        creationTime: null,
        processName: null,
        projectNumber: null,
        centerNo: null,
        centerName: null,
        applicant: null,
        applicationReason: null,
        supplyMethod: null,
        materialCategory: null,
        materialName: null,
        productCode: null,
        productName: null,
        number: null,
        unit: null,
        expectedDeliveryTime: null,
        recipient: null,
        city: null,
        detailedShippingAddress: null,
        endTime: null,
        documentStatus: null,
        currentSession: null,
        currentProcessor: null,
        model: null,
        fDId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreationTime = [];
      this.daterangeEndTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.requestNo)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('clinical/clinicalTrialMaterials/export', {
        ...this.queryParams
      }, `clinicalTrialMaterials_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
