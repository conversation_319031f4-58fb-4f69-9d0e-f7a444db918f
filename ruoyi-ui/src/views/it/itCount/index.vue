<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:itCount:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="itCountList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="系统开发" align="center" prop="jobType1" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="系统运维" align="center" prop="jobType2" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="桌面运维" align="center" prop="jobType3" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="GMP工作" align="center" prop="jobType4" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="文员工作" align="center" prop="jobType5" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="其他工作" align="center" prop="jobType6" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="支援工作" align="center" prop="projectType1" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="文员项目" align="center" prop="projectType2" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="GMP项目" align="center" prop="projectType3" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="软件运维" align="center" prop="projectType4" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="网络运维" align="center" prop="projectType5" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="硬件运维" align="center" prop="projectType6" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="弱电工程" align="center" prop="projectType7" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="其他运维" align="center" prop="projectType8" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="RF费用" align="center" prop="projectType9" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="考试系统" align="center" prop="projectType10" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="SHR" align="center" prop="projectType11" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="OA" align="center" prop="projectType12" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP" align="center" prop="projectType13" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="发票识别" align="center" prop="projectType14" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="电子印章" align="center" prop="projectType15" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="邮箱系统" align="center" prop="projectType16" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="合同系统" align="center" prop="projectType17" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="电子印章" align="center" prop="projectType18" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="BI" align="center" prop="projectType19" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="SAP" align="center" prop="projectType20" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="DMS" align="center" prop="projectType21" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="TMS" align="center" prop="projectType22" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="QMS" align="center" prop="projectType23" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="LIMS" align="center" prop="projectType24" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="SRM" align="center" prop="projectType25" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="WMS" align="center" prop="projectType26" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="CRM" align="center" prop="projectType27" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="发票识别" align="center" prop="projectType28" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="资产管理系统" align="center" prop="projectType29" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="销售数据流向" align="center" prop="projectType30" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="考勤系统" align="center" prop="projectType31" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="数据分析系统" align="center" prop="projectType32" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="报表系统" align="center" prop="projectType33" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="考试系统" align="center" prop="projectType34" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="表单" align="center" prop="projectDetails1" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="接口" align="center" prop="projectDetails2" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="功能" align="center" prop="projectDetails3" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="报表" align="center" prop="projectDetails4" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="数据库" align="center" prop="projectDetails5" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="测试" align="center" prop="projectDetails6" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="需求分析" align="center" prop="projectDetails7" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="需求分析" align="center" prop="projectDetails8" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="权限角色" align="center" prop="projectDetails9" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="开发文档编写" align="center" prop="projectDetails10" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="单元需求分析" align="center" prop="projectDetails11" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="单元测试" align="center" prop="projectDetails12" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="集成测试" align="center" prop="projectDetails13" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="问题答疑" align="center" prop="projectDetails14" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="异常排查处理" align="center" prop="projectDetails15" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="流程表单异常" align="center" prop="projectDetails16" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="接口异常" align="center" prop="projectDetails17" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="系统更新" align="center" prop="projectDetails18" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="系统配置更改" align="center" prop="projectDetails19" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="用户操作指引" align="center" prop="projectDetails20" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="用户培训" align="center" prop="projectDetails21" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="用户操作文档编写" align="center" prop="projectDetails22" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="简易程序修改" align="center" prop="projectDetails23" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="资料整理" align="center" prop="projectDetails24" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="邮箱运维" align="center" prop="projectDetails25" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="问题单跟进" align="center" prop="projectDetails26" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="协助配合" align="center" prop="projectDetails27" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="重装系统" align="center" prop="projectDetails28" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="应用软件安装" align="center" prop="projectDetails29" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="软件技术支持" align="center" prop="projectDetails30" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="软件更新" align="center" prop="projectDetails31" width="100" :show-overflow-tooltip="true" />
      <el-table-column label="软件故障维修" align="center" prop="projectDetails32" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="交换机" align="center" prop="projectDetails33" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="信息安全设备" align="center" prop="projectDetails34" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="线路故障维修" align="center" prop="projectDetails35" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="组网配置" align="center" prop="projectDetails36" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="WiFi维护" align="center" prop="projectDetails37" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="外部网络" align="center" prop="projectDetails38" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="服务器" align="center" prop="projectDetails39" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="显示器" align="center" prop="projectDetails40" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="小型打印机" align="center" prop="projectDetails41" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="复印件" align="center" prop="projectDetails42" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="终端盒" align="center" prop="projectDetails43" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="音响" align="center" prop="projectDetails44" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="笔记本" align="center" prop="projectDetails45" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="摄像机" align="center" prop="projectDetails46" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="终端门禁" align="center" prop="projectDetails47" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="电话机" align="center" prop="projectDetails48" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="刻录机" align="center" prop="projectDetails49" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="投影仪" align="center" prop="projectDetails50" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="投影幕布" align="center" prop="projectDetails51" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="MAXHUB" align="center" prop="projectDetails52" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="弱电规划" align="center" prop="projectDetails53" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="弱电施工" align="center" prop="projectDetails54" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="弱电会议" align="center" prop="projectDetails55" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="基础数据" align="center" prop="projectDetails56" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="账号权限" align="center" prop="projectDetails57" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="运维协助" align="center" prop="projectDetails58" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="机房检查" align="center" prop="projectDetails59" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="账号权限开通/禁用" align="center" prop="projectDetails60" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="灾难恢复验证" align="center" prop="projectDetails61" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="数据可读性验证" align="center" prop="projectDetails62" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="设备CSV验证" align="center" prop="projectDetails63" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="部门验证方案协助" align="center" prop="projectDetails64" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="3Q验证" align="center" prop="projectDetails65" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="部门SOP文件起草" align="center" prop="projectDetails66" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="设备数据备份" align="center" prop="projectDetails67" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="设备管控" align="center" prop="projectDetails68" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="偏差调查" align="center" prop="projectDetails69" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="文件审阅" align="center" prop="projectDetails70" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="数据拷贝" align="center" prop="projectDetails71" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="GMP会议" align="center" prop="projectDetails72" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="GMP自检" align="center" prop="projectDetails73" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="CAPA执行" align="center" prop="projectDetails74" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="部门文件起草" align="center" prop="projectDetails75" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="文件签批流程" align="center" prop="projectDetails76" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="上岗培训" align="center" prop="projectDetails77" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="采购物资" align="center" prop="projectDetails78" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="入离职工作" align="center" prop="projectDetails79" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="邮寄收货工作" align="center" prop="projectDetails80" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="资产盘点" align="center" prop="projectDetails81" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="物资发放回收" align="center" prop="projectDetails82" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="费用、台账统计" align="center" prop="projectDetails83" width="120" :show-overflow-tooltip="true"/>
      <el-table-column label="系统修复" align="center" prop="projectDetails84" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="系统重装" align="center" prop="projectDetails85" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="技术支持" align="center" prop="projectDetails86" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="硬件故障定位" align="center" prop="projectDetails87" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="门禁网络故障" align="center" prop="projectDetails88" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="门禁硬件" align="center" prop="projectDetails89" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="门禁系统故障" align="center" prop="projectDetails90" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="VPN权限变更" align="center" prop="projectDetails91" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="部门邮箱申请" align="center" prop="projectDetails92" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="PC管理员权限、USB权限开通" align="center" prop="projectDetails93" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="EDOC2/VDrive权限变更" align="center" prop="projectDetails94" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listItCount } from "@/api/it/itCount";

export default {
  name: "ItCount",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // IT数据统计表格数据
      itCountList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        jobType1: null,
        jobType2: null,
        jobType3: null,
        jobType4: null,
        jobType5: null,
        jobType6: null,
        projectType1: null,
        projectType2: null,
        projectType3: null,
        projectType4: null,
        projectType5: null,
        projectType6: null,
        projectType7: null,
        projectType8: null,
        projectType9: null,
        projectType10: null,
        projectType11: null,
        projectType12: null,
        projectType13: null,
        projectType14: null,
        projectType15: null,
        projectType16: null,
        projectType17: null,
        projectType18: null,
        projectType19: null,
        projectType20: null,
        projectType21: null,
        projectType22: null,
        projectType23: null,
        projectType24: null,
        projectType25: null,
        projectType26: null,
        projectType27: null,
        projectType28: null,
        projectType29: null,
        projectType30: null,
        projectType31: null,
        projectType32: null,
        projectType33: null,
        projectType34: null,
        projectDetails1: null,
        projectDetails2: null,
        projectDetails3: null,
        projectDetails4: null,
        projectDetails5: null,
        projectDetails6: null,
        projectDetails7: null,
        projectDetails8: null,
        projectDetails9: null,
        projectDetails10: null,
        projectDetails11: null,
        projectDetails12: null,
        projectDetails13: null,
        projectDetails14: null,
        projectDetails15: null,
        projectDetails16: null,
        projectDetails17: null,
        projectDetails18: null,
        projectDetails19: null,
        projectDetails20: null,
        projectDetails21: null,
        projectDetails22: null,
        projectDetails23: null,
        projectDetails24: null,
        projectDetails25: null,
        projectDetails26: null,
        projectDetails27: null,
        projectDetails28: null,
        projectDetails29: null,
        projectDetails30: null,
        projectDetails31: null,
        projectDetails32: null,
        projectDetails33: null,
        projectDetails34: null,
        projectDetails35: null,
        projectDetails36: null,
        projectDetails37: null,
        projectDetails38: null,
        projectDetails39: null,
        projectDetails40: null,
        projectDetails41: null,
        projectDetails42: null,
        projectDetails43: null,
        projectDetails44: null,
        projectDetails45: null,
        projectDetails46: null,
        projectDetails47: null,
        projectDetails48: null,
        projectDetails49: null,
        projectDetails50: null,
        projectDetails51: null,
        projectDetails52: null,
        projectDetails53: null,
        projectDetails54: null,
        projectDetails55: null,
        projectDetails56: null,
        projectDetails57: null,
        projectDetails58: null,
        projectDetails59: null,
        projectDetails60: null,
        projectDetails61: null,
        projectDetails62: null,
        projectDetails63: null,
        projectDetails64: null,
        projectDetails65: null,
        projectDetails66: null,
        projectDetails67: null,
        projectDetails68: null,
        projectDetails69: null,
        projectDetails70: null,
        projectDetails71: null,
        projectDetails72: null,
        projectDetails73: null,
        projectDetails74: null,
        projectDetails75: null,
        projectDetails76: null,
        projectDetails77: null,
        projectDetails78: null,
        projectDetails79: null,
        projectDetails80: null,
        projectDetails81: null,
        projectDetails82: null,
        projectDetails83: null,
        projectDetails84: null,
        projectDetails85: null,
        projectDetails86: null,
        projectDetails87: null,
        projectDetails88: null,
        projectDetails89: null,
        projectDetails90: null,
        projectDetails91: null,
        projectDetails92: null,
        projectDetails93: null,
        projectDetails94: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询IT数据统计列表 */
    getList() {
      this.loading = true;
      listItCount(this.queryParams).then(response => {
        this.itCountList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        jobNumber: null,
        jobType1: null,
        jobType2: null,
        jobType3: null,
        jobType4: null,
        jobType5: null,
        jobType6: null,
        projectType1: null,
        projectType2: null,
        projectType3: null,
        projectType4: null,
        projectType5: null,
        projectType6: null,
        projectType7: null,
        projectType8: null,
        projectType9: null,
        projectType10: null,
        projectType11: null,
        projectType12: null,
        projectType13: null,
        projectType14: null,
        projectType15: null,
        projectType16: null,
        projectType17: null,
        projectType18: null,
        projectType19: null,
        projectType20: null,
        projectType21: null,
        projectType22: null,
        projectType23: null,
        projectType24: null,
        projectType25: null,
        projectType26: null,
        projectType27: null,
        projectType28: null,
        projectType29: null,
        projectType30: null,
        projectType31: null,
        projectType32: null,
        projectType33: null,
        projectType34: null,
        projectDetails1: null,
        projectDetails2: null,
        projectDetails3: null,
        projectDetails4: null,
        projectDetails5: null,
        projectDetails6: null,
        projectDetails7: null,
        projectDetails8: null,
        projectDetails9: null,
        projectDetails10: null,
        projectDetails11: null,
        projectDetails12: null,
        projectDetails13: null,
        projectDetails14: null,
        projectDetails15: null,
        projectDetails16: null,
        projectDetails17: null,
        projectDetails18: null,
        projectDetails19: null,
        projectDetails20: null,
        projectDetails21: null,
        projectDetails22: null,
        projectDetails23: null,
        projectDetails24: null,
        projectDetails25: null,
        projectDetails26: null,
        projectDetails27: null,
        projectDetails28: null,
        projectDetails29: null,
        projectDetails30: null,
        projectDetails31: null,
        projectDetails32: null,
        projectDetails33: null,
        projectDetails34: null,
        projectDetails35: null,
        projectDetails36: null,
        projectDetails37: null,
        projectDetails38: null,
        projectDetails39: null,
        projectDetails40: null,
        projectDetails41: null,
        projectDetails42: null,
        projectDetails43: null,
        projectDetails44: null,
        projectDetails45: null,
        projectDetails46: null,
        projectDetails47: null,
        projectDetails48: null,
        projectDetails49: null,
        projectDetails50: null,
        projectDetails51: null,
        projectDetails52: null,
        projectDetails53: null,
        projectDetails54: null,
        projectDetails55: null,
        projectDetails56: null,
        projectDetails57: null,
        projectDetails58: null,
        projectDetails59: null,
        projectDetails60: null,
        projectDetails61: null,
        projectDetails62: null,
        projectDetails63: null,
        projectDetails64: null,
        projectDetails65: null,
        projectDetails66: null,
        projectDetails67: null,
        projectDetails68: null,
        projectDetails69: null,
        projectDetails70: null,
        projectDetails71: null,
        projectDetails72: null,
        projectDetails73: null,
        projectDetails74: null,
        projectDetails75: null,
        projectDetails76: null,
        projectDetails77: null,
        projectDetails78: null,
        projectDetails79: null,
        projectDetails80: null,
        projectDetails81: null,
        projectDetails82: null,
        projectDetails83: null,
        projectDetails84: null,
        projectDetails85: null,
        projectDetails86: null,
        projectDetails87: null,
        projectDetails88: null,
        projectDetails89: null,
        projectDetails90: null,
        projectDetails91: null,
        projectDetails92: null,
        projectDetails93: null,
        projectDetails94: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.name)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/itCount/export', {
        ...this.queryParams
      }, `itCount_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
