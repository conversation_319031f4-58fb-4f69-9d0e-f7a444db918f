<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input
          v-model="queryParams.age"
          placeholder="请输入年龄"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主诉" prop="chiefComplaint">
        <el-input
          v-model="queryParams.chiefComplaint"
          placeholder="请输入主诉"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="家族史" prop="familyMedicalHistory">
        <el-input
          v-model="queryParams.familyMedicalHistory"
          placeholder="请输入家族史、既往史"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="现病史" prop="presentIllnessHistory">
        <el-input
          v-model="queryParams.presentIllnessHistory"
          placeholder="请输入现病史"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['caseCollect:caseCollectTable:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          @click="toUpdatePage"
          v-hasPermi="['caseCollect:caseCollectTable:edit']"
        >修改</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['caseCollect:caseCollectTable:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['caseCollect:caseCollectTable:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <template>
      <el-form class="caseForm" ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs type="border-card">
          <el-tab-pane label="基本资料">
              <el-form-item label="姓名:" prop="name">
                <span>{{form.name}}</span>
              </el-form-item>
              <el-form-item label="年龄:" prop="age">
                <span>{{form.age}}</span>
              </el-form-item>
              <el-form-item label="主诉:" prop="chiefComplaint">
                <span>{{form.chiefComplaint}}</span>
              </el-form-item>
              <el-form-item label="家族史、既往史:" prop="familyMedicalHistory">
                <span>{{form.familyMedicalHistory}}</span>
              </el-form-item>
              <el-form-item label="现病史:" prop="presentIllnessHistory">
                <span>{{form.presentIllnessHistory}}</span>
              </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="检查资料">
            <el-tabs type="border-card">
              <el-tab-pane label="体格检查">
                <el-form-item label="体格检查:" prop="physicalExamination">
                  <span>{{form.physicalExamination}}</span>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="实验室检查">
                <el-form-item label="血常规检查:" prop="bloodRoutineExamination">
                  <span>{{form.bloodRoutineExamination}}</span>
                </el-form-item>
                <el-form-item label="生化检查:" prop="biochemicalExamination">
                  <span>{{form.biochemicalExamination}}</span>
                </el-form-item>
                <el-form-item label="肿瘤标记物:" prop="tumorMarker">
                  <span>{{form.tumorMarker}}</span>
                </el-form-item>
                <el-form-item label="其他检查:" prop="laboratoryExaminationElse">
                  <span>{{form.laboratoryExaminationElse}}</span>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="治疗前影像学检查">
                <el-form-item label="PETCT影像:" prop="petctImage">
                  <image-preview :src="form.petctImage" :width="50" :height="50"/>
                </el-form-item>
                <el-form-item label="PETCT报告:" prop="petctReport">
                  <span>{{form.petctReport}}</span>
                </el-form-item>
                <el-form-item label="CT影像:" prop="ctImage">
                  <image-preview :src="form.ctImage" :width="50" :height="50"/>
                </el-form-item>
                <el-form-item label="CT报告:" prop="ctReport">
                  <span>{{form.ctReport}}</span>
                </el-form-item>
                <el-form-item label="MRI影像:" prop="mriImage">
                  <image-preview :src="form.mriImage" :width="50" :height="50"/>
                </el-form-item>
                <el-form-item label="MRI报告:" prop="mriReport">
                  <span>{{form.mriReport}}</span>
                </el-form-item>
                <el-form-item label="其他影像:" prop="elseImage">
                  <image-preview :src="form.elseImage" :width="50" :height="50"/>
                </el-form-item>
                <el-form-item label="其他报告:" prop="elseReport">
                  <span>{{form.elseReport}}</span>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="其他检查">
                <el-form-item label="PD-1检查:" prop="pd1">
                  <span>{{form.pd1}}</span>
                </el-form-item>
                <el-form-item label="基因检测:" prop="geneticTest">
                  <span>{{form.geneticTest}}</span>
                </el-form-item>
                <el-form-item label="免疫组化:" prop="immunohistochemical">
                  <span>{{form.immunohistochemical}}</span>
                </el-form-item>
                <el-form-item label="其他检查:" prop="elseExamination">
                  <span>{{form.elseExamination}}</span>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="临床诊断">
            <el-form-item label="病理诊断:" prop="pathologicDiagnosis">
              <span>{{form.pathologicDiagnosis}}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="围术期治疗">
            <el-tabs type="border-card">
              <el-tab-pane label="新辅助治疗">
                <el-form-item label="治疗方案:" prop="neoadjuvantTreatmentOptions">
                  <span>{{form.neoadjuvantTreatmentOptions}}</span>
                </el-form-item>
                <el-form-item label="治疗周期:" prop="neoadjuvantTreatmentCycle">
                  <span>{{form.neoadjuvantTreatmentCycle}}</span>
                </el-form-item>
                <el-form-item label="疗效评估:" prop="ntEvaluation">
                  <span>{{form.ntEvaluation}}</span>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="手术治疗">
                <el-form-item label="治疗时间:" prop="neoadjuvantTreatmentOptions">
                  <span>{{form.surgicalTreatmentTime}}</span>
                </el-form-item>
                <el-form-item label="治疗结果:" prop="surgicalTreatmentResult">
                  <span>{{form.surgicalTreatmentResult}}</span>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane label="术后辅助治疗">
                <el-form-item label="治疗方案:" prop="patPlan">
                  <span>{{form.patPlan}}</span>
                </el-form-item>
                <el-form-item label="治疗周期:" prop="patCycle">
                  <span>{{form.patCycle}}</span>
                </el-form-item>
                <el-form-item label="疗效评估:" prop="patEvaluation">
                  <span>{{form.patEvaluation}}</span>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="局部治疗">
            <el-form-item label="治疗方案:" prop="topicalTreatmentPlan">
              <span>{{form.topicalTreatmentPlan}}</span>
            </el-form-item>
            <el-form-item label="治疗周期:" prop="topicalTreatmentCycle">
              <span>{{form.topicalTreatmentCycle}}</span>
            </el-form-item>
            <el-form-item label="治疗影像:" prop="topicalTreatmentImage">
              <image-preview :src="form.topicalTreatmentImage" :width="50" :height="50"/>
            </el-form-item>
            <el-form-item label="影像报告:" prop="topicalTreatmentImagReport">
              <span>{{form.topicalTreatmentImagReport}}</span>
            </el-form-item>
            <el-form-item label="肿瘤标志物检查:" prop="ttTumorMarkerExamination">
              <span>{{form.ttTumorMarkerExamination}}</span>
            </el-form-item>
            <el-form-item label="疗效评估:" prop="ttEfficacyAssessment">
              <span>{{form.ttEfficacyAssessment}}</span>
            </el-form-item>
            <el-form-item label="其他内容:" prop="topicalTreatmentElse">
              <span>{{form.topicalTreatmentElse}}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="一线治疗">
            <el-form-item label="治疗方案:" prop="firstLineTreatmentPlan">
              <span>{{form.firstLineTreatmentPlan}}</span>
            </el-form-item>
            <el-form-item label="治疗周期:" prop="firstLineTreatmentCycle">
              <span>{{form.firstLineTreatmentCycle}}</span>
            </el-form-item>
            <el-form-item label="治疗影像:" prop="firstLineTreatmentImage">
              <image-preview :src="form.firstLineTreatmentImage" :width="50" :height="50"/>
            </el-form-item>
            <el-form-item label="治疗影像报告:" prop="fltImageReport">
              <span>{{form.fltImageReport}}</span>
            </el-form-item>
            <el-form-item label="肿瘤标志物检查:" prop="fltTumorMarkerExamination">
              <span>{{form.fltTumorMarkerExamination}}</span>
            </el-form-item>
            <el-form-item label="疗效评估:" prop="fltEfficacyAssessment">
              <span>{{form.fltEfficacyAssessment}}</span>
            </el-form-item>
            <el-form-item label="其他内容:" prop="firstLineTreatmentElse">
              <span>{{form.firstLineTreatmentElse}}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="二线治疗">
            <el-form-item label="治疗方案:" prop="secondLineTreatmentPlan">
              <span>{{form.secondLineTreatmentPlan}}</span>
            </el-form-item>
            <el-form-item label="治疗周期:" prop="secondLineTreatmentCycle">
              <span>{{form.secondLineTreatmentCycle}}</span>
            </el-form-item>
            <el-form-item label="治疗影像:" prop="secondLineTreatmentImage">
              <image-preview :src="form.secondLineTreatmentImage" :width="50" :height="50"/>
            </el-form-item>
            <el-form-item label="治疗影像报告:" prop="sltImageReport">
              <span>{{form.sltImageReport}}</span>
            </el-form-item>
            <el-form-item label="肿瘤标志物检查:" prop="sltTumorMarkerExamination">
              <span>{{form.sltTumorMarkerExamination}}</span>
            </el-form-item>
            <el-form-item label="疗效评估:" prop="sltEfficacyAssessment">
              <span>{{form.sltEfficacyAssessment}}</span>
            </el-form-item>
            <el-form-item label="其他内容:" prop="secondLineTreatmentElse">
              <span>{{form.secondLineTreatmentElse}}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="三线治疗">
            <el-form-item label="治疗方案:" prop="thirdLineTreatmentPlan">
              <span>{{form.thirdLineTreatmentPlan}}</span>
            </el-form-item>
            <el-form-item label="治疗周期:" prop="thirdLineTreatmentCycle">
              <span>{{form.thirdLineTreatmentCycle}}</span>
            </el-form-item>
            <el-form-item label="治疗影像:" prop="thirdLineTreatmentImage">
              <image-preview :src="form.thirdLineTreatmentImage" :width="50" :height="50"/>
            </el-form-item>
            <el-form-item label="治疗影像报告:" prop="tltImageReport">
              <span>{{form.tltImageReport}}</span>
            </el-form-item>
            <el-form-item label="肿瘤标志物检查:" prop="tltTumorMarkerExamination">
              <span>{{form.tltTumorMarkerExamination}}</span>
            </el-form-item>
            <el-form-item label="疗效评估:" prop="tltEfficacyAssessment">
              <span>{{form.tltEfficacyAssessment}}</span>
            </el-form-item>
            <el-form-item label="其他内容:" prop="thirdLineTreatmentElse">
              <span>{{form.thirdLineTreatmentElse}}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="不良反应处理">
            <el-form-item label="不良反应处理:" prop="adverseReactionHandle">
              <span>{{form.adverseReactionHandle}}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="病例总结与思考">
            <el-form-item label="病例总结与思考" prop="caseSummaryAndReflection">
              <span>{{form.caseSummaryAndReflection}}</span>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </template>

<!--    <el-table v-loading="loading" :data="caseCollectTableList" @selection-change="handleSelectionChange">-->
<!--      <el-table-column type="selection" width="55" align="center" />-->
<!--      <el-table-column label="姓名" align="center" prop="name" />-->
<!--      <el-table-column label="性别" align="center" prop="sex" />-->
<!--      <el-table-column label="年龄" align="center" prop="age" />-->
<!--      <el-table-column label="主诉" align="center" prop="chiefComplaint" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="家族史、既往史" align="center" prop="familyMedicalHistory" />-->
<!--      <el-table-column label="现病史" align="center" prop="presentIllnessHistory" />-->
<!--      <el-table-column label="体格检查" align="center" prop="physicalExamination" />-->
<!--      <el-table-column label="血常规检查" align="center" prop="bloodRoutineExamination" />-->
<!--      <el-table-column label="生化检查" align="center" prop="biochemicalExamination" />-->
<!--      <el-table-column label="肿瘤标记物" align="center" prop="tumorMarker" />-->
<!--      <el-table-column label="其他实验室检查" align="center" prop="laboratoryExaminationElse" />-->
<!--      <el-table-column label="PETCT影像" align="center" prop="petctImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.petctImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="PETCT报告" align="center" prop="petctReport" />-->
<!--      <el-table-column label="CT影像" align="center" prop="ctImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.ctImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="CT报告" align="center" prop="ctReport" />-->
<!--      <el-table-column label="MRI影像" align="center" prop="mriImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.mriImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="MRI报告" align="center" prop="mriReport" />-->
<!--      <el-table-column label="其他影像" align="center" prop="elseImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.elseImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="其他报告" align="center" prop="elseReport" />-->
<!--      <el-table-column label="PD-1检查" align="center" prop="pd1" />-->
<!--      <el-table-column label="基因检测" align="center" prop="geneticTest" />-->
<!--      <el-table-column label="免疫组化" align="center" prop="immunohistochemical" />-->
<!--      <el-table-column label="其他检查" align="center" prop="elseExamination" />-->
<!--      <el-table-column label="病理诊断" align="center" prop="pathologicDiagnosis" />-->
<!--      <el-table-column label="新辅助治疗方案" align="center" prop="neoadjuvantTreatmentOptions" />-->
<!--      <el-table-column label="新辅助治疗周期" align="center" prop="neoadjuvantTreatmentCycle" />-->
<!--      <el-table-column label="新辅助治疗疗效评估" align="center" prop="neoadjuvantTreatmentEvaluati" />-->
<!--      <el-table-column label="手术治疗时间" align="center" prop="surgicalTreatmentTime" />-->
<!--      <el-table-column label="手术治疗结果" align="center" prop="surgicalTreatmentResult" />-->
<!--      <el-table-column label="术后辅助治疗方案" align="center" prop="postoperativeAdjuvantTreatmentPlan" />-->
<!--      <el-table-column label="术后辅助治疗周期" align="center" prop="postoperativeAdjuvantTreatmentCycle" />-->
<!--      <el-table-column label="术后辅助治疗疗效评估" align="center" prop="postoperativeAdjuvantTreatmentEvaluation" />-->
<!--      <el-table-column label="局部治疗方案" align="center" prop="topicalTreatmentPlan" />-->
<!--      <el-table-column label="局部治疗周期" align="center" prop="topicalTreatmentCycle" />-->
<!--      <el-table-column label="局部治疗影像" align="center" prop="topicalTreatmentImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.topicalTreatmentImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="局部治疗影像报告" align="center" prop="topicalTreatmentImagReport" />-->
<!--      <el-table-column label="局部治疗肿瘤标志物检查" align="center" prop="ttTumorMarkerExamination" />-->
<!--      <el-table-column label="局部治疗疗效评估" align="center" prop="topicalTreatmentEfficacyAss" />-->
<!--      <el-table-column label="局部治疗其他内容" align="center" prop="topicalTreatmentElse" />-->
<!--      <el-table-column label="一线治疗方案" align="center" prop="firstLineTreatmentPlan" />-->
<!--      <el-table-column label="一线治疗周期" align="center" prop="firstLineTreatmentCycle" />-->
<!--      <el-table-column label="一线治疗影像" align="center" prop="firstLineTreatmentImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.firstLineTreatmentImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="一线治疗影像报告" align="center" prop="firstLineTreatmentImageRep" />-->
<!--      <el-table-column label="一线治疗肿瘤标志物检查" align="center" prop="fltTumorMarkerExamination" />-->
<!--      <el-table-column label="一线治疗疗效评估" align="center" prop="fltEfficacyAssessment" />-->
<!--      <el-table-column label="一线治疗其他内容" align="center" prop="firstLineTreatmentElse" />-->
<!--      <el-table-column label="二线治疗方案" align="center" prop="secondLineTreatmentPlan" />-->
<!--      <el-table-column label="二线治疗周期" align="center" prop="secondLineTreatmentCycle" />-->
<!--      <el-table-column label="二线治疗影像" align="center" prop="secondLineTreatmentImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.secondLineTreatmentImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="二线治疗影像报告" align="center" prop="secondLineTreatmentImageRe" />-->
<!--      <el-table-column label="二线治疗肿瘤标志物检查" align="center" prop="sltTumorMarkerExamination" />-->
<!--      <el-table-column label="二线治疗疗效评估" align="center" prop="sltEfficacyAssessment" />-->
<!--      <el-table-column label="二线治疗其他内容" align="center" prop="secondLineTreatmentElse" />-->
<!--      <el-table-column label="三线治疗方案" align="center" prop="thirdLineTreatmentPlan" />-->
<!--      <el-table-column label="三线治疗周期" align="center" prop="thirdLineTreatmentCycle" />-->
<!--      <el-table-column label="三线治疗影像" align="center" prop="thirdLineTreatmentImage" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <image-preview :src="scope.row.thirdLineTreatmentImage" :width="50" :height="50"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="三线治疗影像报告" align="center" prop="thirdLineTreatmentImageRep" />-->
<!--      <el-table-column label="三线治疗肿瘤标志物检查" align="center" prop="tltTumorMarkerExamination" />-->
<!--      <el-table-column label="三线治疗疗效评估" align="center" prop="tltEfficacyAssessment" />-->
<!--      <el-table-column label="三线治疗其他内容" align="center" prop="thirdLineTreatmentElse" />-->
<!--      <el-table-column label="不良反应处理" align="center" prop="adverseReactionHandle" />-->
<!--      <el-table-column label="病例总结与思考" align="center" prop="caseSummaryAndReflection" />-->
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['caseCollect:caseCollectTable:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['caseCollect:caseCollectTable:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--    </el-table>-->

<!--    <pagination-->
<!--      v-show="total>0"-->
<!--      :total="total"-->
<!--      :page.sync="queryParams.pageNum"-->
<!--      :limit.sync="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->
  </div>
</template>

<script>
import { listCaseCollectTable, getCaseCollectTable, delCaseCollectTable, addCaseCollectTable, updateCaseCollectTable } from "@/api/caseCollect/caseCollectTable";

export default {
  name: "CaseCollectTable",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 病例收集表格数据
      caseCollectTableList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        name: null,
        sex: null,
        age: null,
        chiefComplaint: null,
        familyMedicalHistory: null,
        presentIllnessHistory: null,
        physicalExamination: null,
        bloodRoutineExamination: null,
        biochemicalExamination: null,
        tumorMarker: null,
        laboratoryExaminationElse: null,
        petctImage: null,
        petctReport: null,
        ctImage: null,
        ctReport: null,
        mriImage: null,
        mriReport: null,
        elseImage: null,
        elseReport: null,
        pd1: null,
        geneticTest: null,
        immunohistochemical: null,
        elseExamination: null,
        pathologicDiagnosis: null,
        neoadjuvantTreatmentOptions: null,
        neoadjuvantTreatmentCycle: null,
        neoadjuvantTreatmentEvaluati: null,
        surgicalTreatmentTime: null,
        surgicalTreatmentResult: null,
        postoperativeAdjuvantTreatmentPlan: null,
        postoperativeAdjuvantTreatmentCycle: null,
        postoperativeAdjuvantTreatmentEvaluation: null,
        topicalTreatmentPlan: null,
        topicalTreatmentCycle: null,
        topicalTreatmentImage: null,
        topicalTreatmentImagReport: null,
        ttTumorMarkerExamination: null,
        topicalTreatmentEfficacyAss: null,
        topicalTreatmentElse: null,
        firstLineTreatmentPlan: null,
        firstLineTreatmentCycle: null,
        firstLineTreatmentImage: null,
        firstLineTreatmentImageRep: null,
        fltTumorMarkerExamination: null,
        fltEfficacyAssessment: null,
        firstLineTreatmentElse: null,
        secondLineTreatmentPlan: null,
        secondLineTreatmentCycle: null,
        secondLineTreatmentImage: null,
        secondLineTreatmentImageRe: null,
        sltTumorMarkerExamination: null,
        sltEfficacyAssessment: null,
        secondLineTreatmentElse: null,
        thirdLineTreatmentPlan: null,
        thirdLineTreatmentCycle: null,
        thirdLineTreatmentImage: null,
        thirdLineTreatmentImageRep: null,
        tltTumorMarkerExamination: null,
        tltEfficacyAssessment: null,
        thirdLineTreatmentElse: null,
        adverseReactionHandle: null,
        caseSummaryAndReflection: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    // this.getList();
    if (this.$route.query.id) {
      const id = this.$route.query.id;
      getCaseCollectTable(id).then(response => {
        this.form = response.data;
      })
    }
  },
  methods: {
    /** 查询病例收集列表 */
    getList() {
      this.loading = true;
      listCaseCollectTable(this.queryParams).then(response => {
        // this.caseCollectTableList = response.rows;
        this.form = response.rows[0];
        this.total = response.total;
        this.loading = false;
      });
    },
    //跳转到修改页面
    toUpdatePage() {
      this.$router.push({ path: '/caseCollect/update', query: { id: this.form.id }})
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        age: null,
        chiefComplaint: null,
        familyMedicalHistory: null,
        presentIllnessHistory: null,
        physicalExamination: null,
        bloodRoutineExamination: null,
        biochemicalExamination: null,
        tumorMarker: null,
        laboratoryExaminationElse: null,
        petctImage: null,
        petctReport: null,
        ctImage: null,
        ctReport: null,
        mriImage: null,
        mriReport: null,
        elseImage: null,
        elseReport: null,
        pd1: null,
        geneticTest: null,
        immunohistochemical: null,
        elseExamination: null,
        pathologicDiagnosis: null,
        neoadjuvantTreatmentOptions: null,
        neoadjuvantTreatmentCycle: null,
        neoadjuvantTreatmentEvaluati: null,
        surgicalTreatmentTime: null,
        surgicalTreatmentResult: null,
        postoperativeAdjuvantTreatmentPlan: null,
        postoperativeAdjuvantTreatmentCycle: null,
        postoperativeAdjuvantTreatmentEvaluation: null,
        topicalTreatmentPlan: null,
        topicalTreatmentCycle: null,
        topicalTreatmentImage: null,
        topicalTreatmentImagReport: null,
        ttTumorMarkerExamination: null,
        topicalTreatmentEfficacyAss: null,
        topicalTreatmentElse: null,
        firstLineTreatmentPlan: null,
        firstLineTreatmentCycle: null,
        firstLineTreatmentImage: null,
        firstLineTreatmentImageRep: null,
        fltTumorMarkerExamination: null,
        fltEfficacyAssessment: null,
        firstLineTreatmentElse: null,
        secondLineTreatmentPlan: null,
        secondLineTreatmentCycle: null,
        secondLineTreatmentImage: null,
        secondLineTreatmentImageRe: null,
        sltTumorMarkerExamination: null,
        sltEfficacyAssessment: null,
        secondLineTreatmentElse: null,
        thirdLineTreatmentPlan: null,
        thirdLineTreatmentCycle: null,
        thirdLineTreatmentImage: null,
        thirdLineTreatmentImageRep: null,
        tltTumorMarkerExamination: null,
        tltEfficacyAssessment: null,
        thirdLineTreatmentElse: null,
        adverseReactionHandle: null,
        caseSummaryAndReflection: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加病例收集";
    },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const id = row.id || this.ids
    //   getCaseCollectTable(id).then(response => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = "修改病例收集";
    //   });
    // },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCaseCollectTable(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCaseCollectTable(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除病例收集编号为"' + ids + '"的数据项？').then(function() {
        return delCaseCollectTable(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('caseCollect/caseCollectTable/export', {
        ...this.queryParams
      }, `caseCollectTable_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped>
.el-carousel__item{
  width: 80%;
  left: -15%;
}
.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 200px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n+1) {
  background-color: #d3dce6;
}
.caseForm {
  margin-top: 20px;
}
.el-carousel .el-carousel__item{
  background-color: rgba(234, 239, 239, 0.97); /* 将此值替换为你想要的背景颜色 */
}
</style>
