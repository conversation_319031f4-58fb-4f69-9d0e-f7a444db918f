package com.akesobio.web.CostControlTest;


import com.akesobio.ReportApplication;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.common.utils.uuid.UUID;
import com.akesobio.report.costControl.domain.EkpAttFile;
import com.akesobio.report.costControl.mapper.EkpAttFileMapper;
import com.akesobio.report.costControl.service.CloudpenseApiUtils;
import com.akesobio.report.costControlApi.domain.CostCenterData;
import com.akesobio.report.costControlApi.domain.CostCenterDataVo;
import com.akesobio.report.costControlApi.domain.SapCostCenter;
import com.akesobio.report.costControlApi.entity.DepartmentInfo;
import com.akesobio.report.costControlApi.mapper.DepartmentInfoMapper;
import com.akesobio.report.costControlApi.timeTask.CostControlTask;
import com.akesobio.report.ekp.mapper.EkpCommonMapper;
import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.mainData.domain.CostCenterUser;
import com.akesobio.report.mainData.mapper.CostCenterMapper;
import com.akesobio.report.meetingApi.MeetingApiService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import com.akesobio.report.mainData.mapper.CostCenterUserMapper;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
@Slf4j
public class CostControlTest {


//    public static void main(String[] args) {
////        String pathSource = "\\\\10.10.2.206\\resource" + e.getFdFilePath();
////        String pathDest = e.getFdFilePath().replace("/", "\\") + "\\" + e.getFdFileName();
//    }
    @Resource
    private EkpAttFileMapper ekpAttFileMapper;
    @Resource
    private DepartmentInfoMapper departmentInfoMapper;
    @Resource
    private CostCenterMapper costCenterMapper;

    @Resource
    private CostCenterUserMapper costCenterUserMapper;

    @Resource
    private EkpCommonMapper ekpCommonMapper;

    @Resource
    private CostControlTask costControlTask;

    @Resource
    private MeetingApiService meetingApiService;

    @Resource
    private CloudpenseApiUtils cloudpenseApiUtils;
    private static String accessToken = "";

    @Value("${costControl.costCenter.url}")
    private String costCenterUrl;

    @Value("${costControl.token.token_url}")
    private String token_url;
    @Value("${costControl.token.grant_type}")
    private String grant_type;
    @Value("${costControl.token.client_id}")
    private String client_id;
    @Value("${costControl.token.client_secret}")
    private String client_secret;
    public  void getToken() {
        System.out.println("执行无参方法");
        String post;
        String url = token_url + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret;
        try {
            post = HttpUtils.sendGet(url);
            JsonParser parser = new JsonParser();
            JsonObject jsonObject = parser.parse(post).getAsJsonObject();
            // 获取 data 节点
            JsonObject dataNode = jsonObject.getAsJsonObject("data");
            // 获取 access_token 值
            accessToken = dataNode.get("access_token").getAsString();
            System.out.println("Access Token: " + accessToken);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    //批量禁用成本中心
    public void test() {

        getToken();

        String excelPath = "C:\\Users\\<USER>\\Downloads\\DownloadData (6).xlsx";

        List<Map<Integer, String>> headers = new ArrayList<>();
        List<Map<Integer, Object>> dataList = new ArrayList<>();

        EasyExcel.read(excelPath, new AnalysisEventListener<Map<Integer, Object>>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                headers.add(new LinkedHashMap<>(headMap)); // 存储表头
            }

            @Override
            public void invoke(Map<Integer, Object> data, AnalysisContext context) {
                dataList.add(new LinkedHashMap<>(data)); // 存储数据行
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 转换结果到JSON
                List<Map<String, Object>> jsonResult = dataList.stream().map(row -> {
                    Map<String, Object> mappedRow = new LinkedHashMap<>();
                    headers.get(0).forEach((index, headerName) ->
                            mappedRow.put(headerName, row.get(index))
                    );
                    return mappedRow;
                }).collect(Collectors.toList());

                String json = JSON.toJSONString(jsonResult);
                log.info("Excel转换JSON结果：{}", json);
            }
        }).sheet().doRead();

//            List<CostCenterData> list = EasyExcel
//                .read(new File("C:\\Users\\<USER>\\Downloads\\DownloadData (6).xlsx"))
//                .head(CostCenterData.class)
//                .sheet()
//                .doReadSync();
        List<CostCenterData> costCenterDataList = new ArrayList<>();
        for (Map<Integer, Object> costCenterData: dataList) {

            CostCenterData costCenterData1 = new CostCenterData();
            costCenterData1.setCode((String) costCenterData.get(0));
            costCenterData1.setDepartment_name((String) costCenterData.get(1));

            int code = Integer.parseInt((String) costCenterData.get(0));

            if(code >= 658){
                costCenterData1.setType("C");
                costCenterData1.setEnabled_flag("N");
                costCenterDataList.add(costCenterData1);
            }
        }
//        System.out.printf(costCenterDataList.toString());
        if (!costCenterDataList.isEmpty()) {
            int batchSize = 200;
            int totalBatches = (costCenterDataList.size() + batchSize - 1) / batchSize; // 计算需要同步的批次数量
            for (int i = 0; i < totalBatches; i++) {
//                int num = i + 1;
                int start = i * batchSize;
                int end = Math.min(start + batchSize, costCenterDataList.size());
                List<CostCenterData> batchList = costCenterDataList.subList(start, end);

                com.akesobio.common.utils.uuid.UUID uuid = UUID.randomUUID();

                CostCenterDataVo data = new CostCenterDataVo();
                data.setBizId(uuid.toString());
                data.setTimestamp(System.currentTimeMillis());
                data.setData(batchList);

                Gson gson = new Gson();
                String json = gson.toJson(data);
                log.info("url: " + costCenterUrl);
                log.info("json: " + json);
                log.info("accessToken: " + accessToken);
                String info = HttpUtils.sendPost(costCenterUrl, json, "Bearer " + accessToken);
                log.info("第" + i + 1 + "次同步部门及成本中心主数据接口返回信息: {}", info);
//                    msg.append("第" + num + "次同步部门及成本中心主数据接口返回信息: " + info + ";--");
                try {
                    Thread.sleep(5000); // 暂停5秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Thread interrupted during sleep", e);
                }
            }
        }
    }

    @Test
    public void testCostControlTask() throws Exception {
        costControlTask.costCenterMainData();

//        List<CostCenter> costCenters = costCenterMapper.selectList(null);
//        List<Map<String, Object>> ekpUserList = ekpCommonMapper.ekpUser();
//        Map<String, Map<String, Object>> ekpUserMap = ekpUserList.stream().filter(item -> (String) item.get("fd_no") != null).collect(Collectors.toMap(s -> (String) s.get("fd_no"), Function.identity(), (existing, replacement) -> existing));
//
//        List<JSONObject> costCentersJson = costCenters.stream().map(costCenter -> JSONObject.parseObject(JSON.toJSONString(costCenter))).collect(Collectors.toList());
//
//        JSONArray jsonArray =  costCentersJson.stream().map(costCenter -> {
//            if(ekpUserMap.containsKey(costCenter.getString("supervisor"))){
//                costCenter.put("supervisorName", ekpUserMap.get(costCenter.getString("supervisor")).get("fd_name"));
//            }
//            return costCenter;
//        }).collect(Collectors.toCollection(JSONArray::new));
//
//        System.out.println(jsonArray.toJSONString());


//        List<Map<String, Object>> comparison = ekpCommonMapper.ekpBmcbzx();
//        //oa用户
//        List<Map<String, Object>> ekpUserList = ekpCommonMapper.ekpUser();
//        Map<String, Map<String, Object>> ekpUserMap = ekpUserList.stream().collect(Collectors.toMap(s -> (String) s.get("fd_id"), Function.identity(), (existing, replacement) -> existing));
//        //oa部门
//        List<Map<String, Object>> ekpDeptList = ekpCommonMapper.ekpDept();
//        Map<String, List<Map<String, Object>>> ekpDeptMap = ekpDeptList.stream().collect(Collectors.groupingBy(s -> (String) s.get("fd_name")));
////        ekpDeptMap.containsKey("市场中心");
//        //创建虚拟费用承担部门
//        Map<String, Map<String, Object>> comparisonMap = new HashMap<>();
//        for (Map<String, Object> item : comparison) {
//            // 假设每个Map中都有一个"id"键，我们用这个键的值作为新Map的键
//            String id = (String) item.get("dept_name");
//            comparisonMap.put(id, item);
//
//        }
//        List<DepartmentInfo> departmentInfoList = departmentInfoMapper.selectDepartmentInfoList1(new DepartmentInfo()).stream().filter(d -> d.getSuperiorDepartmentCode() != null).collect(Collectors.toList());
//        List<DepartmentInfo> root = departmentInfoList.stream().filter(d -> "商业运营部".equals(d.getDepartmentName())).collect(Collectors.toList());
//
//        for (DepartmentInfo d : root) {
//            dfs(departmentInfoList, d, null, comparisonMap, ekpDeptMap, ekpUserMap);
//        }
//
//        //设置业务上的末级节点
//        List<CostCenter> costCenters = costCenterMapper.selectList(null);
//        Set<Long> costCenterParentIdSet = costCenters.stream().filter(item -> item.getParentId() != null).map(CostCenter::getParentId).collect(Collectors.toSet());
//        costCenterParentIdSet.remove(44L);
//        for (CostCenter costCenter : costCenters) {
//            if (costCenterParentIdSet.contains(costCenter.getId())) costCenter.setBusinessTerminalNode("N");
//            else costCenter.setBusinessTerminalNode("Y");
//            costCenterMapper.updateById(costCenter);
//        }

    }
    void dfs(List<DepartmentInfo> departmentInfoList, DepartmentInfo root, CostCenter costCenterRoot, Map<String, Map<String, Object>> comparison, Map<String, List<Map<String, Object>>> ekpDeptMap, Map<String, Map<String, Object>> ekpUserMap) {
        LambdaQueryWrapper<CostCenter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostCenter::getCostCenterName, root.getDepartmentName());
        CostCenter costCenter = costCenterMapper.selectOne(queryWrapper);

        if (costCenter == null) {
            costCenter = new CostCenter();
            costCenter.setCostCenterName(root.getDepartmentName());
            if (costCenterRoot != null) costCenter.setParentId(costCenterRoot.getId());
            else costCenter.setParentId(null);
            if (comparison != null && comparison.containsKey(root.getDepartmentName())) {
                Map<String, Object> item = comparison.get(root.getDepartmentName());
                costCenter.setArea((String) item.get("area_code"));
                costCenter.setSapSuffixCode((String) item.get("sap_suffix_code"));

            }
            //获取部门主管
            List<Map<String, Object>> deptList = ekpDeptMap.get(costCenter.getCostCenterName());
            for (Map<String, Object> dept : deptList) {
                String userId = (String) dept.get("fd_this_leaderid");
                if (StringUtils.hasText(userId) && ekpUserMap.containsKey(userId)) {
                    String jobNumber = (String) ekpUserMap.get(userId).get("fd_no");
                    costCenter.setSupervisor(jobNumber);
                }
            }
            costCenterMapper.insert(costCenter);
        } else {
            if (comparison != null && comparison.containsKey(root.getDepartmentName())) {
                Map<String, Object> item = comparison.get(root.getDepartmentName());
                costCenter.setArea((String) item.get("area_code"));
                costCenter.setSapSuffixCode((String) item.get("sap_suffix_code"));
            }
            List<Map<String, Object>> deptList = ekpDeptMap.get(costCenter.getCostCenterName());
            for (Map<String, Object> dept : deptList) {
                String userId = (String) dept.get("fd_this_leaderid");
                if (StringUtils.hasText(userId) && ekpUserMap.containsKey(userId)) {
                    String jobNumber = (String) ekpUserMap.get(userId).get("fd_no");
                    costCenter.setSupervisor(jobNumber);
                }
            }
            costCenterMapper.updateById(costCenter);
        }
        for (DepartmentInfo d : departmentInfoList) {
            if (d.getSuperiorDepartmentCode().equals(root.getDepartmentId())) {
//                i++;
                dfs(departmentInfoList, d, costCenter, comparison, ekpDeptMap, ekpUserMap);
            }
        }
    }

    @Test
    public void testEkpAttFileMapper() {
        List<Map<String,Object>> ekpAttFileList = ekpCommonMapper.ekpBmcbzx();


        /** 对接SAP成本中心主数据*/
        List<SapCostCenter> list = new ArrayList<>();

        if (ekpAttFileList.size() > 0) {
            for (Map<String,Object> c : ekpAttFileList) {
                System.out.println(c);
//                SapCostCenter sapCostCenterData = new SapCostCenter();
//                sapCostCenterData.setCode("SAP_Cost_Center");
//                sapCostCenterData.setValue(c.getCostCenterName());
//                sapCostCenterData.setType(c.getCostCenterCode());
//                System.out.println("name:" + c.getCostCenterName());
//                System.out.println("value:" + c.getCostCenterCode());
//                if (c.getEnableFlag().equals("1")) {
//                    sapCostCenterData.setEnabled_flag("Y");
//                } else {
//                    sapCostCenterData.setEnabled_flag("N");
//                }
//                list.add(sapCostCenterData);
            }
        }

        Map<String, Map<String,Object>> resultMap = new HashMap<>();
        for (Map<String,Object> item : ekpAttFileList) {
            // 假设每个Map中都有一个"id"键，我们用这个键的值作为新Map的键
            String id = (String) item.get("dept_name");
            resultMap.put(id, item);
        }

    }

    @Test
    public void testDepartmentInfoMapper() {

//        List<Map<String,Object>> ekpAttFileList = ekpCommonMapper.ekpBmcbzx();
//        Map<String, Map<String,Object>> ekpAttFileMap = new HashMap<>();
//        for (Map<String,Object> item : ekpAttFileList) {
//            // 假设每个Map中都有一个"id"键，我们用这个键的值作为新Map的键
//            String id = (String) item.get("dept_name");
//            ekpAttFileMap.put(id, item);
//        }
//        List<DepartmentInfo> departmentInfoList = departmentInfoMapper.selectDepartmentInfoList1(new DepartmentInfo()).stream().filter(d -> d.getSuperiorDepartmentCode() != null).collect(Collectors.toList());
//        List<DepartmentInfo> root = departmentInfoList.stream().filter(d -> "商业运营部".equals(d.getDepartmentName())).collect(Collectors.toList());
//        for(DepartmentInfo d : root){
//            dfs(departmentInfoList,d,null,ekpAttFileMap);
//        }
//
//        for(DepartmentInfo d : departmentInfoList){
//
//        }

         List<Map<String,Object>> ekpUserList = ekpCommonMapper.ekpUser();
         List<CostCenter> costCenterList = costCenterMapper.selectList(null);
          Map<String, CostCenter> costCenterMap = costCenterList.stream().collect(Collectors.toMap(CostCenter::getCostCenterName, c -> c));

         for(Map<String,Object> item : ekpUserList){
             if(costCenterMap.containsKey((String) item.get("dept_name"))){
                 CostCenterUser costCenterUser = new CostCenterUser();
                 costCenterUser.setCostCenterId(costCenterMap.get((String) item.get("dept_name")).getId());
                 costCenterUser.setUserCode((String) item.get("fd_no"));
                 costCenterUserMapper.insert(costCenterUser);
             }
         }
        System.out.println(ekpUserList);

    }

    @Test
    public  void testsss() throws Exception {
        //请求参数



        JSONArray jsonArray1 = new JSONArray();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", "204");
        jsonObject.put("name", "广州2区");
        jsonObject.put("departmentHeadCode", "A01556");
        jsonObject.put("endLevelDepartment", "N");
        jsonObject.put("stateCode",1);
//        jsonArray = jsonArray.stream().filter(object -> {
//            JSONObject jsonObject = (JSONObject) object;
//            String departmentHeadCode = jsonObject.getString("departmentHeadCode");
//            return StringUtils.hasText(departmentHeadCode);
//        }).collect(Collectors.toCollection(JSONArray::new));
        jsonArray1.add(jsonObject);
        meetingApiService.updateCostDepartment(jsonArray1);

    }

//    @Test
//    public void ekpFileMapperTest() throws IOException {
//        EkpAttFile ekpAttFile = new EkpAttFile();
//        ekpAttFile.setFdKey("fd_3b52ac5280b27c");
//        ekpAttFile.setFdModelId("19209c4210b8fe54a98c9904d4cb8e0a");
//        List<EkpAttFile> ekpAttFiles = ekpAttFileMapper.selectEkpAttFile(ekpAttFile);
//        //将远程文件复制到本地
//        for (EkpAttFile e : ekpAttFiles) {
//            String pathSource = "\\\\10.10.2.200\\kmss\\resource"+e.getFdFilePath();
//            String pathDest = e.getFdFilePath().replace("/","\\")+"\\"+e.getFdFileName();
//            pathDest = pathDest.substring(1);
//            File file = new File(pathDest);
//            // 创建路径中的所有目录
//            file.getParentFile().mkdirs();
//            // 创建文件
//            file.createNewFile();
//            Path sourcePath = Paths.get(pathSource);
//            Path destinationPath = Paths.get(pathDest);
//            try (FileChannel sourceChannel = FileChannel.open(sourcePath, StandardOpenOption.READ);
//                 FileChannel destinationChannel = FileChannel.open(destinationPath, StandardOpenOption.WRITE, StandardOpenOption.CREATE)) {
//                // 缓冲区大小
//                byte[] buffer = new byte[1024];
//                int bytesRead;
//                long totalBytesRead = 0;
//
//                // 读取数据并写入目标文件
//                while ((bytesRead = sourceChannel.read(ByteBuffer.wrap(buffer))) != -1) {
//                    destinationChannel.write(ByteBuffer.wrap(buffer), totalBytesRead);
//                    totalBytesRead += bytesRead;
//                }
//            }
//            String res =  uploadFile(file.getAbsolutePath());
//
//        }
////        for (EkpAttFile e : ekpAttFiles) {
////          String res =  uploadFile("10.10.2.200\\kmss\\resource"+e.getFdFilePath());
////          System.out.println(res);
////        }
////        System.out.println(ekpAttFileMapper.selectEkpAttFile(ekpAttFile));
//    }
//    public static String getToken() {
//        String response = "";
//        String token_url = "https://taliopenapi.cloudpense.com/common/unAuth/tokens/get";
//        String grant_type = "client_credentials";
//        String client_id = "cloudpense2483631363b";
//        String client_secret = "44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98";
//        String url = token_url + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret;
//        try {
//            response = HttpUtils.sendGet(url);
//            JSONObject jsonObject = JSON.parseObject(response);
//            String accessToken = jsonObject.getJSONObject("data").getString("access_token");
//            log.info("response: " + response);
//            return accessToken;
////
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//    public static String uploadFile(String filePath) throws MalformedURLException {
//        String url = "https://taliopenapi.cloudpense.com/common/files/url";
//        RestTemplate template = new RestTemplate();
//        HttpHeaders headers = new HttpHeaders();
//        MediaType type = MediaType.parseMediaType("multipart/form-data");
//        headers.setContentType(type);
//        headers.set("access_token", getToken());
//        //设置请求体
//        FileSystemResource resource = new FileSystemResource(filePath);
////        UrlResource urlResource = new UrlResource("file://"+filePath);
//        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
//        form.add("file", resource);
//        HttpEntity<MultiValueMap<String, Object>> files = new HttpEntity<>(form, headers);
//        String attachmentResponse = template.postForObject(url, files, String.class);
//        log.info("attachmentResponse: " + attachmentResponse);
////        System.out.println(attachmentResponse);
//        return attachmentResponse;
//    }
}
