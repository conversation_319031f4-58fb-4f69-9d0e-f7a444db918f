package com.akesobio.iittools;

import com.akesobio.common.dto.OaContractRequest;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.iittools.service.IITOaService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * IIT OA服务示例类
 * 演示如何使用IITOaService处理Excel数据并转换为OA请求
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Slf4j
public class IITOaServiceDemo {

    //待同步项目号
    private static String projectStartDate = "AK112-IIT-C-N2-0013\n" +
            "AK112-IIT-C-N2-0020\n" +
            "AK112-IIT-C-N2-0021\n" +
            "AK112-IIT-C-N2-0022\n" +
            "AK112-IIT-C-N2-0023\n" +
            "AK104-IIT-C-N2-0145\n" +
            "AK104-IIT-C-N2-0153\n" +
            "AK104-IIT-C-N2-0161\n" +
            "AK104-IIT-C-M-0105\n";


//    private static Map<String, String> projectStartDateMap = Map.of(
//            "AK112-IIT-C-N2-0013", "",
//            "AK112-IIT-C-N2-0020", "",
//            "AK112-IIT-C-N2-0021", "",
//            "AK112-IIT-C-N2-0022", "",
//            "AK112-IIT-C-N2-0023", "",
//            "AK104-IIT-C-N2-0145", "",
//            "AK104-IIT-C-N2-0153", "",
//            "AK104-IIT-C-N2-0161", "",
//            "AK104-IIT-C-M-0105", "A00899"
//    );

    private static final Map<String, String> projectStartDateMap = new HashMap<String, String>()
    {{
//        put("AK104-IIT-C-E-0121", "A01813");
//        put("AK104-IIT-C-E-0122", "A01813");
//        put("AK104-IIT-C-E-0120", "A01813");
//        put("AK104-IIT-C-E-0129", "A01813");
//        put("AK104-IIT-C-M-0105", "A00899");
//        put("AK104-IIT-C-E-0115","A04816");
//        put("AK104-IIT-C-E-0027","A04816");
//        put("AK104-IIT-C-W-0149","A04632");

//        put("AK104-IIT-C-W-1062", "A04632");
//        put("AK104-IIT-C-W-0105", "A04632");
//        put("AK104-IIT-C-W-0160", "A04632");
        put("AK104-IIT-C-S-0141", "A03769");
//        put("AK104-IIT-C-S-0122", "A03758");


    }};


    /**
     * OA表单模型ID
     */
    private static final String FD_MODEL_ID = "193afd704ea1c7b70fc4ade470ab3490"; // 替换为实际的表单模型ID

    /**
     * OA流程ID
     */
    private static final String FD_FLOW_ID = "193afdc5e71e3e1d1309add471fa5da8"; // 替换为实际的流程ID

    /**
     * OA文档创建者ID
     */
    private static final String DOC_CREATOR = "{\"PersonNo\":\"B00490\"}"; // 替换为实际的创建者ID

    /**
     * 示例方法：从Excel文件读取数据并转换为OA请求对象
     */
    @Deprecated
    public static void excelToOaRequestsDemo() {
        try {
            // 创建IITOaService实例
            IITOaService iitOaService = new IITOaService(FD_MODEL_ID, FD_FLOW_ID, DOC_CREATOR);
            
            // Excel文件路径
            String excelFilePath = "C:\\Users\\<USER>\\Desktop\\新建 XLSX 工作表.xlsx"; // 替换为实际的Excel文件路径
            
            // 处理Excel文件并获取OA请求对象列表
            List<OaContractRequest> requestList = iitOaService.processExcelToOaRequests(excelFilePath, projectStartDateMap);
            
            // 处理结果
            System.out.println("共读取 " + requestList.size() + " 条数据");

            ObjectMapper objectMapper = new ObjectMapper();
            for (int i = 0; i < requestList.size(); i++) {
                OaContractRequest request = requestList.get(i);
                String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request);
                if(i == 0){
                    System.out.println("第一条请求的JSON字符串：");
                    System.out.println(json);
                }

            }
            
        } catch (Exception e) {
            System.err.println("处理Excel文件失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 新的示例方法：从Excel文件读取数据并转换为OA请求对象
     */
    public static void newExcelToOaRequestsDemo() {
        try {
            // 创建IITOaService实例
            IITOaService iitOaService = new IITOaService(FD_MODEL_ID, FD_FLOW_ID, DOC_CREATOR);
            // Excel文件路径
            String excelFilePath = "C:\\Users\\<USER>\\Downloads\\新建文件夹.xlsx"; // 替换为实际的Excel文件路径
            // 处理Excel文件并获取OA请求对象列表
            List<OaContractRequest> requestList = iitOaService.processExcelToOaRequests(excelFilePath,projectStartDateMap);
            // 处理结果
            System.out.println("共读取 " + requestList.size() + " 条数据");
            ObjectMapper objectMapper = new ObjectMapper();
            for (int i = 0; i < requestList.size(); i++) {
                OaContractRequest request = requestList.get(i);
                String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request);
                HttpUtils.sendPostJson("https://oa.akesobio.com/api/extend/modeling/createModeling",json);
            }
        } catch (Exception e) {
            System.err.println("处理Excel文件失败：" + e.getMessage());
            e.printStackTrace();
        }
    }



    /**
     * 示例方法：将ProjectDetailInputModel转换为IITApplicationFormValues
     */
    public static void newProjectDetailToFormValuesDemo() {
        System.out.println("\n===== 示例2：从Excel读取ProjectDetailInputModel，转换为OaContractRequest并打印 =====");
        try {
            // 1. 创建IITOaService实例
            IITOaService iitOaService = new IITOaService(FD_MODEL_ID, FD_FLOW_ID, DOC_CREATOR);

            // 2. 定义Excel文件路径
            String excelFilePath = "C:\\Users\\<USER>\\Downloads\\IIT-C类导入 (3).xlsx";

            // 3. 从Excel读取数据并转换为List<OaContractRequest>
            List<OaContractRequest> oaContractRequestList = iitOaService.processProjectDetailToOaRequests(excelFilePath,projectStartDateMap);

            if (oaContractRequestList == null || oaContractRequestList.isEmpty()) {
                System.out.println("从Excel未能读取到数据，或处理后OaContractRequest列表为空。");
                return;
            }
            System.out.println("从Excel共读取并转换了 " + oaContractRequestList.size() + " 条数据为OaContractRequest");

            // 4. 遍历列表，打印每个OaContractRequest
            ObjectMapper objectMapper = new ObjectMapper(); // ObjectMapper可以重用
            for (int i = 0; i < oaContractRequestList.size(); i++) {
                OaContractRequest oaRequest = oaContractRequestList.get(i);

//                    System.out.println("\n--- OaContractRequest " + (i + 1) + " ---");
//                    System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(oaRequest));
                    HttpUtils.sendPostJson("https://oa.akesobio.com/api/extend/modeling/createModeling",objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(oaRequest));


            }

        } catch (Exception e) {
            System.err.println("处理项目详细数据并生成OaContractRequest失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("===== 示例1：从Excel文件读取数据 =====");
        newExcelToOaRequestsDemo();
        newProjectDetailToFormValuesDemo();
    }
} 