package com.akesobio.system.service.impl;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.system.domain.MqConfig;
import com.akesobio.system.mapper.MqConfigMapper;
import com.akesobio.system.service.IMqConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 虚拟岗校验规则配置 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class MqConfigServiceImpl implements IMqConfigService
{
    @Autowired
    private MqConfigMapper mqConfigMapper;

    /**
     * 查询虚拟岗校验规则配置信息
     * 
     * @param id 配置ID
     * @return 虚拟岗校验规则配置信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public MqConfig selectMqConfigById(Long id)
    {
        return mqConfigMapper.selectMqConfigById(id);
    }

    /**
     * 根据接口名称查询虚拟岗校验规则配置信息
     * 
     * @param interfaceName 接口名称
     * @return 虚拟岗校验规则配置信息
     */
    @Override
    public MqConfig selectMqConfigByInterfaceName(String interfaceName)
    {
        MqConfig mqConfig = new MqConfig();
        mqConfig.setInterfaceName(interfaceName);
        return mqConfigMapper.selectMqConfig(mqConfig);
    }

    /**
     * 查询虚拟岗校验规则配置列表
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 虚拟岗校验规则配置集合
     */
    @Override
    public List<MqConfig> selectMqConfigList(MqConfig mqConfig)
    {
        return mqConfigMapper.selectMqConfigList(mqConfig);
    }

    /**
     * 新增虚拟岗校验规则配置
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    @Override
    public int insertMqConfig(MqConfig mqConfig)
    {
        return mqConfigMapper.insertMqConfig(mqConfig);
    }

    /**
     * 修改虚拟岗校验规则配置
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    @Override
    public int updateMqConfig(MqConfig mqConfig)
    {
        return mqConfigMapper.updateMqConfig(mqConfig);
    }

    /**
     * 批量删除虚拟岗校验规则配置
     * 
     * @param ids 需要删除的配置ID
     */
    @Override
    public void deleteMqConfigByIds(Long[] ids)
    {
        for (Long id : ids)
        {
            mqConfigMapper.deleteMqConfigById(id);
        }
    }

    /**
     * 校验接口名称是否唯一
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    @Override
    public boolean checkInterfaceNameUnique(MqConfig mqConfig)
    {
        Long id = StringUtils.isNull(mqConfig.getId()) ? -1L : mqConfig.getId();
        MqConfig info = mqConfigMapper.checkInterfaceNameUnique(mqConfig.getInterfaceName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return false;
        }
        return true;
    }
}
