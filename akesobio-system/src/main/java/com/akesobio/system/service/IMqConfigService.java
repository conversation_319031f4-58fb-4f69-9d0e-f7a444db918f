package com.akesobio.system.service;

import com.akesobio.system.domain.MqConfig;

import java.util.List;

/**
 * 虚拟岗校验规则配置 服务层
 * 
 * <AUTHOR>
 */
public interface IMqConfigService
{
    /**
     * 查询虚拟岗校验规则配置信息
     * 
     * @param id 配置ID
     * @return 虚拟岗校验规则配置信息
     */
    public MqConfig selectMqConfigById(Long id);

    /**
     * 根据接口名称查询虚拟岗校验规则配置信息
     * 
     * @param interfaceName 接口名称
     * @return 虚拟岗校验规则配置信息
     */
    public MqConfig selectMqConfigByInterfaceName(String interfaceName);

    /**
     * 查询虚拟岗校验规则配置列表
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 虚拟岗校验规则配置集合
     */
    public List<MqConfig> selectMqConfigList(MqConfig mqConfig);

    /**
     * 新增虚拟岗校验规则配置
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    public int insertMqConfig(MqConfig mqConfig);

    /**
     * 修改虚拟岗校验规则配置
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    public int updateMqConfig(MqConfig mqConfig);

    /**
     * 批量删除虚拟岗校验规则配置
     * 
     * @param ids 需要删除的配置ID
     */
    public void deleteMqConfigByIds(Long[] ids);

    /**
     * 校验接口名称是否唯一
     * 
     * @param mqConfig 虚拟岗校验规则配置信息
     * @return 结果
     */
    public boolean checkInterfaceNameUnique(MqConfig mqConfig);
}
