package com.akesobio.system.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.annotation.Excel.ColumnType;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 虚拟岗校验规则配置表 mq_config
 * 
 * <AUTHOR>
 */
public class MqConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置主键 */
    @Excel(name = "配置主键", cellType = ColumnType.NUMERIC)
    private Long id;

    /** 公司ID */
    @Excel(name = "公司ID", cellType = ColumnType.NUMERIC)
    private Long companyId;

    /** 交换机 */
    @Excel(name = "交换机")
    private String exchange;

    /** 路由键 */
    @Excel(name = "路由键")
    private String rootingKey;

    /** 平台 */
    @Excel(name = "平台")
    private String platform;

    /** 接口名称 */
    @Excel(name = "接口名称")
    private String interfaceName;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 事件代码 */
    @Excel(name = "事件代码")
    private String eventCode;

    /** 单据类型代码 */
    @Excel(name = "单据类型代码")
    private String headerTypeCode;

    /** 岗位编码 */
    @Excel(name = "岗位编码")
    private String positionCode;

    /** 工作流类型 */
    @Excel(name = "工作流类型")
    private String workflowType;

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    @NotNull(message = "公司ID不能为空")
    public Long getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(Long companyId)
    {
        this.companyId = companyId;
    }

    @NotBlank(message = "交换机不能为空")
    @Size(min = 0, max = 100, message = "交换机长度不能超过100个字符")
    public String getExchange()
    {
        return exchange;
    }

    public void setExchange(String exchange)
    {
        this.exchange = exchange;
    }

    @NotBlank(message = "路由键不能为空")
    @Size(min = 0, max = 200, message = "路由键长度不能超过200个字符")
    public String getRootingKey()
    {
        return rootingKey;
    }

    public void setRootingKey(String rootingKey)
    {
        this.rootingKey = rootingKey;
    }

    @NotBlank(message = "平台不能为空")
    @Size(min = 0, max = 50, message = "平台长度不能超过50个字符")
    public String getPlatform()
    {
        return platform;
    }

    public void setPlatform(String platform)
    {
        this.platform = platform;
    }

    @NotBlank(message = "接口名称不能为空")
    @Size(min = 0, max = 100, message = "接口名称长度不能超过100个字符")
    public String getInterfaceName()
    {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName)
    {
        this.interfaceName = interfaceName;
    }

    @Size(min = 0, max = 500, message = "描述长度不能超过500个字符")
    public String getDescription()
    {
        return description;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    @NotBlank(message = "事件代码不能为空")
    @Size(min = 0, max = 50, message = "事件代码长度不能超过50个字符")
    public String getEventCode()
    {
        return eventCode;
    }

    public void setEventCode(String eventCode)
    {
        this.eventCode = eventCode;
    }

    @NotBlank(message = "单据类型代码不能为空")
    @Size(min = 0, max = 50, message = "单据类型代码长度不能超过50个字符")
    public String getHeaderTypeCode()
    {
        return headerTypeCode;
    }

    public void setHeaderTypeCode(String headerTypeCode)
    {
        this.headerTypeCode = headerTypeCode;
    }

    @NotBlank(message = "岗位编码不能为空")
    @Size(min = 0, max = 50, message = "岗位编码长度不能超过50个字符")
    public String getPositionCode()
    {
        return positionCode;
    }

    public void setPositionCode(String positionCode)
    {
        this.positionCode = positionCode;
    }

    @Size(min = 0, max = 50, message = "工作流类型长度不能超过50个字符")
    public String getWorkflowType()
    {
        return workflowType;
    }

    public void setWorkflowType(String workflowType)
    {
        this.workflowType = workflowType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("companyId", getCompanyId())
            .append("exchange", getExchange())
            .append("rootingKey", getRootingKey())
            .append("platform", getPlatform())
            .append("interfaceName", getInterfaceName())
            .append("description", getDescription())
            .append("eventCode", getEventCode())
            .append("headerTypeCode", getHeaderTypeCode())
            .append("positionCode", getPositionCode())
            .append("workflowType", getWorkflowType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
