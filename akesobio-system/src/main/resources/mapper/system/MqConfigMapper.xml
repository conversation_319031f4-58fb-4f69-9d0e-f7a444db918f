<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.system.mapper.MqConfigMapper">
    
    <resultMap type="MqConfig" id="MqConfigResult">
        <id     property="id"                column="id"                />
        <result property="companyId"         column="company_id"        />
        <result property="exchange"          column="exchange"          />
        <result property="rootingKey"        column="rooting_key"       />
        <result property="platform"          column="platform"          />
        <result property="interfaceName"     column="interface_name"    />
        <result property="description"       column="description"       />
        <result property="eventCode"         column="event_code"        />
        <result property="headerTypeCode"    column="header_type_code"  />
        <result property="positionCode"      column="position_code"     />
        <result property="workflowType"      column="workflow_type"     />
        <result property="createBy"          column="created_by"        />
        <result property="createTime"        column="creation_date"     />
        <result property="updateBy"          column="last_updated_by"   />
        <result property="updateTime"        column="last_update_date"  />
        <result property="remark"            column="remark"            />
    </resultMap>

    <sql id="selectMqConfigVo">
        select id, company_id, exchange, rooting_key, platform, interface_name, description, 
               event_code, header_type_code, position_code, workflow_type, created_by, 
               creation_date, last_updated_by, last_update_date, remark
        from mq_config
    </sql>

    <select id="selectMqConfig" parameterType="MqConfig" resultMap="MqConfigResult">
        <include refid="selectMqConfigVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="companyId != null"> and company_id = #{companyId}</if>
            <if test="exchange != null and exchange != ''"> and exchange = #{exchange}</if>
            <if test="rootingKey != null and rootingKey != ''"> and rooting_key = #{rootingKey}</if>
            <if test="platform != null and platform != ''"> and platform = #{platform}</if>
            <if test="interfaceName != null and interfaceName != ''"> and interface_name = #{interfaceName}</if>
            <if test="eventCode != null and eventCode != ''"> and event_code = #{eventCode}</if>
            <if test="headerTypeCode != null and headerTypeCode != ''"> and header_type_code = #{headerTypeCode}</if>
            <if test="positionCode != null and positionCode != ''"> and position_code = #{positionCode}</if>
            <if test="workflowType != null and workflowType != ''"> and workflow_type = #{workflowType}</if>
        </where>
        limit 1
    </select>

    <select id="selectMqConfigById" parameterType="Long" resultMap="MqConfigResult">
        <include refid="selectMqConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectMqConfigList" parameterType="MqConfig" resultMap="MqConfigResult">
        <include refid="selectMqConfigVo"/>
        <where>
            <if test="companyId != null"> and company_id = #{companyId}</if>
            <if test="exchange != null and exchange != ''"> and exchange like concat('%', #{exchange}, '%')</if>
            <if test="rootingKey != null and rootingKey != ''"> and rooting_key like concat('%', #{rootingKey}, '%')</if>
            <if test="platform != null and platform != ''"> and platform like concat('%', #{platform}, '%')</if>
            <if test="interfaceName != null and interfaceName != ''"> and interface_name like concat('%', #{interfaceName}, '%')</if>
            <if test="eventCode != null and eventCode != ''"> and event_code like concat('%', #{eventCode}, '%')</if>
            <if test="headerTypeCode != null and headerTypeCode != ''"> and header_type_code like concat('%', #{headerTypeCode}, '%')</if>
            <if test="positionCode != null and positionCode != ''"> and position_code like concat('%', #{positionCode}, '%')</if>
            <if test="workflowType != null and workflowType != ''"> and workflow_type like concat('%', #{workflowType}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(creation_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(creation_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by creation_date desc
    </select>

    <select id="checkInterfaceNameUnique" parameterType="String" resultMap="MqConfigResult">
        <include refid="selectMqConfigVo"/>
        where interface_name = #{interfaceName} limit 1
    </select>

    <insert id="insertMqConfig" parameterType="MqConfig" useGeneratedKeys="true" keyProperty="id">
        insert into mq_config (
            <if test="companyId != null">company_id,</if>
            <if test="exchange != null and exchange != ''">exchange,</if>
            <if test="rootingKey != null and rootingKey != ''">rooting_key,</if>
            <if test="platform != null and platform != ''">platform,</if>
            <if test="interfaceName != null and interfaceName != ''">interface_name,</if>
            <if test="description != null">description,</if>
            <if test="eventCode != null and eventCode != ''">event_code,</if>
            <if test="headerTypeCode != null and headerTypeCode != ''">header_type_code,</if>
            <if test="positionCode != null and positionCode != ''">position_code,</if>
            <if test="workflowType != null">workflow_type,</if>
            <if test="createBy != null and createBy != ''">created_by,</if>
            <if test="updateBy != null and updateBy != ''">last_updated_by,</if>
            <if test="remark != null">remark,</if>
            creation_date,
            last_update_date
        ) values (
            <if test="companyId != null">#{companyId},</if>
            <if test="exchange != null and exchange != ''">#{exchange},</if>
            <if test="rootingKey != null and rootingKey != ''">#{rootingKey},</if>
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="interfaceName != null and interfaceName != ''">#{interfaceName},</if>
            <if test="description != null">#{description},</if>
            <if test="eventCode != null and eventCode != ''">#{eventCode},</if>
            <if test="headerTypeCode != null and headerTypeCode != ''">#{headerTypeCode},</if>
            <if test="positionCode != null and positionCode != ''">#{positionCode},</if>
            <if test="workflowType != null">#{workflowType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate(),
            sysdate()
        )
    </insert>

    <update id="updateMqConfig" parameterType="MqConfig">
        update mq_config
        <set>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="exchange != null and exchange != ''">exchange = #{exchange},</if>
            <if test="rootingKey != null and rootingKey != ''">rooting_key = #{rootingKey},</if>
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="interfaceName != null and interfaceName != ''">interface_name = #{interfaceName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="eventCode != null and eventCode != ''">event_code = #{eventCode},</if>
            <if test="headerTypeCode != null and headerTypeCode != ''">header_type_code = #{headerTypeCode},</if>
            <if test="positionCode != null and positionCode != ''">position_code = #{positionCode},</if>
            <if test="workflowType != null">workflow_type = #{workflowType},</if>
            <if test="updateBy != null and updateBy != ''">last_updated_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            last_update_date = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteMqConfigById" parameterType="Long">
        delete from mq_config where id = #{id}
    </delete>

    <delete id="deleteMqConfigByIds" parameterType="String">
        delete from mq_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
